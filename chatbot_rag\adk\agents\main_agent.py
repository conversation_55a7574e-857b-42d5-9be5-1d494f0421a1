"""
Main Agent implementation using Google's Agent Development Kit.
"""

import logging
from typing import Dict, Any, Optional, List

from google.adk.agents import Agent
from google.adk.models import GeminiModel

from chatbot_rag.adk.agents.code_analysis_agent import CodeAnalysisAgent
from chatbot_rag.adk.agents.refactoring_agent import RefactoringAgent
from chatbot_rag.adk.agents.documentation_agent import DocumentationAgent
from chatbot_rag.adk.agents.testing_agent import TestingAgent
from chatbot_rag.adk.agents.codebase_understanding_agent import CodebaseUnderstandingAgent
from chatbot_rag.adk.tools.vscode_tools import open_file_tool, edit_file_tool, save_file_tool
from chatbot_rag.adk.tools.cached_tools import search_code_cached_tool, get_file_structure_cached_tool, read_file_cached_tool
from chatbot_rag.adk.utils.adk_utils import initialize_model

# Configure logging
logger = logging.getLogger("main_agent")

class CodeAssistantAgent:
    """
    Main agent that coordinates specialized subagents.
    """

    def __init__(self, model=None, model_provider: Optional[str] = None, model_name: Optional[str] = None,
                 subagent_models: Optional[Dict[str, Dict[str, str]]] = None):
        """
        Initialize the Code Assistant Agent.

        Args:
            model: Optional model instance. If not provided, a model will be initialized based on model_provider and model_name.
            model_provider: Optional model provider (google, anthropic, ollama, openai).
            model_name: Optional model name.
            subagent_models: Optional dictionary mapping subagent types to model configurations.
                Example: {
                    "code_analysis": {"provider": "anthropic", "name": "claude-3-opus-20240229"},
                    "refactoring": {"provider": "ollama", "name": "deepseek-coder"},
                    "documentation": {"provider": "google", "name": "gemini-1.5-pro"},
                    "testing": {"provider": "openai", "name": "gpt-4o"},
                    "codebase_understanding": {"provider": "anthropic", "name": "claude-3.5-sonnet-20240620"}
                }
        """
        # If no model is provided but provider or name is, initialize a model
        if model is None and (model_provider is not None or model_name is not None):
            model = initialize_model(model_provider, model_name)
            logger.info(f"Initialized main model: {model_provider or 'default'}/{model_name or 'default'}")

        # Store model info for subagent creation
        self.model = model
        self.model_provider = model_provider
        self.model_name = model_name
        self.subagent_models = subagent_models or {}

        # Initialize subagents with potentially different models
        self.code_analysis_agent = self._create_subagent(
            "code_analysis",
            CodeAnalysisAgent,
            self.subagent_models.get("code_analysis")
        )

        self.refactoring_agent = self._create_subagent(
            "refactoring",
            RefactoringAgent,
            self.subagent_models.get("refactoring")
        )

        self.documentation_agent = self._create_subagent(
            "documentation",
            DocumentationAgent,
            self.subagent_models.get("documentation")
        )

        self.testing_agent = self._create_subagent(
            "testing",
            TestingAgent,
            self.subagent_models.get("testing")
        )

        self.codebase_understanding_agent = self._create_subagent(
            "codebase_understanding",
            CodebaseUnderstandingAgent,
            self.subagent_models.get("codebase_understanding")
        )

        # Create the main agent
        self.agent = Agent(
            name="code_assistant_agent",
            model=model or "gemini-2.0-flash",
            description="I am a code assistant that helps with various coding tasks.",
            instruction="""
            You are a code assistant that helps developers with various coding tasks. You can:
            1. Analyze code to identify issues and improvement opportunities
            2. Refactor code to improve readability, performance, or other goals
            3. Generate documentation for code
            4. Generate tests for code
            5. Search the codebase for relevant information
            6. Understand codebase structure, relationships, and patterns
            7. Open, edit, and save files in the IDE

            When a user asks a question, determine which specialized agent would be best suited to handle it,
            or use your own capabilities for general questions.

            For questions about codebase structure, architecture, or relationships between components,
            use the codebase understanding agent.

            Always be helpful, clear, and concise in your responses.
            """,
            tools=[
                # Use cached tools for better performance
                search_code_cached_tool,  # Cached version of search_code_tool
                get_file_structure_cached_tool,  # Cached file structure tool
                read_file_cached_tool,  # Cached file reading tool

                # Non-cached tools (these modify state, so caching doesn't make sense)
                open_file_tool,
                edit_file_tool,
                save_file_tool
            ],
            sub_agents=[
                self.code_analysis_agent.agent,
                self.refactoring_agent.agent,
                self.documentation_agent.agent,
                self.testing_agent.agent,
                self.codebase_understanding_agent.agent
            ]
        )

        logger.info("Code Assistant Agent initialized with all subagents")

    def _create_subagent(self, agent_type: str, agent_class, model_config: Optional[Dict[str, str]] = None):
        """
        Create a subagent with a specific model configuration.

        Args:
            agent_type: The type of agent (for logging)
            agent_class: The agent class to instantiate
            model_config: Optional model configuration with provider and name

        Returns:
            An instance of the specified agent class
        """
        if model_config and ("provider" in model_config or "name" in model_config):
            # Create a specific model for this subagent
            provider = model_config.get("provider")
            name = model_config.get("name")

            logger.info(f"Creating {agent_type} agent with custom model: {provider or 'default'}/{name or 'default'}")

            # Initialize the model
            custom_model = initialize_model(provider, name)
            return agent_class(custom_model)
        else:
            # Use the main agent's model
            logger.info(f"Creating {agent_type} agent with main model")
            return agent_class(self.model)

    def create_subagent(self, agent_class, custom_model=None, model_provider=None, model_name=None):
        """
        Create a subagent with a custom model configuration.

        Args:
            agent_class: The agent class to instantiate
            custom_model: Optional custom model instance to use
            model_provider: Optional model provider
            model_name: Optional model name

        Returns:
            An instance of the specified agent class
        """
        if custom_model:
            # Use the provided model instance
            return agent_class(custom_model)
        elif model_provider or model_name:
            # Create a new model with the specified provider and name
            custom_model = initialize_model(model_provider, model_name)
            return agent_class(custom_model)
        else:
            # Use the main agent's model
            return agent_class(self.model)

    async def understand_codebase(self, query: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Use the Codebase Understanding Agent to analyze the codebase structure.

        Args:
            query: The question about the codebase structure or architecture.
            context: Optional additional context.

        Returns:
            A dictionary containing the analysis results.
        """
        try:
            # Delegate to the codebase understanding agent
            result = await self.codebase_understanding_agent.understand_codebase(query, context)

            # Format the response
            return {
                "response": result.get("understanding", ""),
                "thinking": result.get("thinking", ""),
                "tool_calls": result.get("tool_calls", []),
                "session_id": result.get("session_id", ""),
                "codebase_context": result.get("codebase_context", {})
            }
        except Exception as e:
            logger.error(f"Error understanding codebase: {str(e)}")
            return {
                "error": str(e),
                "response": f"Error understanding codebase: {str(e)}"
            }

    async def process_query(self, query: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a user query.

        Args:
            query: The user's query.
            context: Optional additional context, such as file content, selected text, etc.

        Returns:
            A dictionary containing the response.
        """
        try:
            # Create a session for this query
            session = self.agent.create_session()

            # Prepare the input with context if available
            user_input = query
            if context:
                # If there's file context, include it
                if "fileName" in context and "language" in context and "fullText" in context:
                    user_input = f"""
                    {query}

                    Here's the current file I'm working with:
                    File: {context.get('fileName')}
                    Language: {context.get('language')}

                    ```{context.get('language')}
                    {context.get('fullText')}
                    ```
                    """
                # If there's selected text, include it
                elif "selectedText" in context and context.get("selectedText"):
                    user_input = f"""
                    {query}

                    Here's the selected code:

                    ```
                    {context.get('selectedText')}
                    ```
                    """

            # Run the agent
            response = await session.send_message(user_input)

            logger.info(f"Query processed: {query[:50]}...")

            # Extract suggested edits if any
            suggested_edits = []
            for tool_call in response.tool_calls:
                if tool_call.name in ["edit_file", "save_file"]:
                    suggested_edits.append({
                        "fileName": tool_call.args.get("file_path", ""),
                        "applied": False,
                        "edits": tool_call.args.get("edits", {}) if tool_call.name == "edit_file" else {
                            "fullText": tool_call.args.get("content", "")
                        }
                    })

            # Format the response
            return {
                "response": response.text,
                "thinking": response.thinking,
                "tool_calls": response.tool_calls,
                "session_id": session.session_id,
                "suggested_edits": suggested_edits if suggested_edits else None
            }

        except Exception as e:
            logger.error(f"Error processing query: {str(e)}")
            return {
                "error": str(e),
                "response": f"Error processing your request: {str(e)}"
            }
