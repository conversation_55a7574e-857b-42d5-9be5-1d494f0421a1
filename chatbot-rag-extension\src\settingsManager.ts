import * as vscode from 'vscode';

export interface RagAssistantSettings {
    apiUrl: string;
    markdownSupport: boolean;
    syntaxHighlighting: boolean;
    maxHistoryItems: number;
    authEnabled: boolean;
    authType: string;
    apiKey?: string;
    bearerToken?: string;
    username?: string;
    password?: string;
}

export class SettingsManager {
    private static readonly SECTION = 'ragAssistant';

    public static getSettings(): RagAssistantSettings {
        const config = vscode.workspace.getConfiguration(this.SECTION);

        return {
            apiUrl: config.get<string>('apiUrl', 'http://localhost:8000'),
            markdownSupport: config.get<boolean>('markdownSupport', true),
            syntaxHighlighting: config.get<boolean>('syntaxHighlighting', true),
            maxHistoryItems: config.get<number>('maxHistoryItems', 50),
            authEnabled: config.get<boolean>('authEnabled', false),
            authType: config.get<string>('authType', 'apiKey'),
            apiKey: config.get<string>('apiKey', ''),
            bearerToken: config.get<string>('bearerToken', ''),
            username: config.get<string>('username', ''),
            password: config.get<string>('password', '')
        };
    }

    public static async updateSetting<T>(key: string, value: T): Promise<void> {
        await vscode.workspace.getConfiguration(this.SECTION).update(key, value, vscode.ConfigurationTarget.Global);
    }

    public static openSettings(): void {
        vscode.commands.executeCommand('workbench.action.openSettings', `@ext:${this.SECTION}`);
    }
}
