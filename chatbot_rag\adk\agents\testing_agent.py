"""
Testing Agent implementation using Google's Agent Development Kit.
"""

import logging
from typing import Dict, Any, Optional

from google.adk.agents import Agent
from google.adk.models import GeminiModel

from chatbot_rag.adk.tools.code_tools import generate_tests_tool
from chatbot_rag.adk.tools.rag_tools import search_code_tool

# Configure logging
logger = logging.getLogger("testing_agent")

class TestingAgent:
    """
    Agent specialized in generating tests for code.
    """
    
    def __init__(self, model: Optional[GeminiModel] = None):
        """
        Initialize the Testing Agent.
        
        Args:
            model: Optional GeminiModel instance. If not provided, a default model will be used.
        """
        self.agent = Agent(
            name="testing_agent",
            model=model or "gemini-2.0-flash",
            description="I generate comprehensive tests for code.",
            instruction="""
            You are a testing expert. Your job is to generate comprehensive tests for code, including:
            1. Unit tests for individual functions and methods
            2. Edge case tests
            3. Integration tests where appropriate
            4. Mocking strategies for external dependencies
            
            Always follow the testing conventions of the language and framework (e.g., pytest for Python, Jest for JavaScript).
            When you need more context about the codebase, use the search_code tool.
            """,
            tools=[generate_tests_tool, search_code_tool]
        )
        
        logger.info("Testing Agent initialized")
    
    async def generate_tests(self, code: str, language: str = "python", context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate tests for the given code.
        
        Args:
            code: The code to test.
            language: The programming language of the code.
            context: Optional additional context.
            
        Returns:
            A dictionary containing the generated tests.
        """
        try:
            # Create a session for this test generation
            session = self.agent.create_session()
            
            # Prepare the input
            user_input = f"""
            Please generate comprehensive tests for the following {language} code:
            
            ```{language}
            {code}
            ```
            
            Follow the standard testing conventions for {language}.
            """
            
            # Run the agent
            response = await session.send_message(user_input)
            
            logger.info(f"Tests generated for {len(code)} characters of {language} code")
            
            # Format the response
            return {
                "tests": response.text,
                "thinking": response.thinking,
                "tool_calls": response.tool_calls,
                "session_id": session.session_id
            }
            
        except Exception as e:
            logger.error(f"Error generating tests: {str(e)}")
            return {
                "error": str(e),
                "tests": f"Error generating tests: {str(e)}"
            }
