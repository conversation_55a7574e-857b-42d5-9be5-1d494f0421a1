// Code block functionality for RAG Assistant

// Initialize code block functions
window.codeBlockFunctions = {
    // Variables for resize functionality
    isResizing: false,
    currentResizeBlock: null,
    initialHeight: 0,
    initialWidth: 0,
    initialX: 0,
    initialY: 0,

    // Toggle code block collapse/expand
    toggleCodeBlock: function(blockId) {
        const block = document.getElementById(blockId);
        const content = block.querySelector('.code-block-content');
        const button = block.querySelector('.code-block-collapse');
        const preElement = content.querySelector('pre');

        if (content.style.display === 'none') {
            // Expand
            content.style.display = 'flex'; // Use flex instead of block to maintain layout
            content.style.flexDirection = 'column';

            // Restore scrollbars and layout
            if (preElement) {
                preElement.style.overflow = 'auto';

                // Restore saved dimensions if available
                if (content.dataset.lastWidth && content.dataset.lastHeight) {
                    content.style.width = content.dataset.lastWidth;
                    content.style.height = content.dataset.lastHeight;

                    // Also update pre element dimensions
                    const width = parseInt(content.dataset.lastWidth);
                    const height = parseInt(content.dataset.lastHeight);
                    if (!isNaN(width) && !isNaN(height)) {
                        preElement.style.minWidth = (width - 20) + 'px'; // Account for padding
                        preElement.style.minHeight = (height - 20) + 'px'; // Account for padding
                    }
                }
            }

            button.textContent = '\u2212'; // Minus sign
        } else {
            // Save current dimensions before collapsing
            content.dataset.lastWidth = content.style.width || content.offsetWidth + 'px';
            content.dataset.lastHeight = content.style.height || content.offsetHeight + 'px';

            // Collapse
            content.style.display = 'none';
            button.textContent = '\u002B'; // Plus sign
        }
    },

    // Start resizing a code block
    startResize: function(blockId, event) {
        this.isResizing = true;
        const codeBlock = document.getElementById(blockId);
        this.currentResizeBlock = codeBlock.querySelector('.code-block-content');
        this.initialHeight = this.currentResizeBlock.offsetHeight;
        this.initialWidth = this.currentResizeBlock.offsetWidth;
        this.initialX = event.clientX;
        this.initialY = event.clientY;

        // Add resizing class to the code block
        codeBlock.classList.add('resizing');

        // Prevent text selection during resize
        event.preventDefault();

        // Add event listeners for resize
        document.addEventListener('mousemove', this.resize.bind(this));
        document.addEventListener('mouseup', this.stopResize.bind(this));
    },

    // Handle resize
    resize: function(e) {
        if (!this.isResizing) return;

        // Calculate deltas
        const deltaX = e.clientX - this.initialX;
        const deltaY = e.clientY - this.initialY;

        // Calculate new dimensions with minimums
        const newWidth = Math.max(300, this.initialWidth + deltaX); // Minimum width of 300px
        const newHeight = Math.max(100, this.initialHeight + deltaY); // Minimum height of 100px

        // Apply new dimensions
        this.currentResizeBlock.style.width = newWidth + 'px';
        this.currentResizeBlock.style.height = newHeight + 'px';

        // Ensure the pre element also resizes properly
        const preElement = this.currentResizeBlock.querySelector('pre');
        if (preElement) {
            preElement.style.minWidth = (newWidth - 20) + 'px'; // Account for padding
            preElement.style.minHeight = (newHeight - 20) + 'px'; // Account for padding
        }
    },

    // Stop resizing
    stopResize: function() {
        if (!this.isResizing) return;

        this.isResizing = false;

        // Remove resizing class from all code blocks
        document.querySelectorAll('.code-block.resizing').forEach(block => {
            block.classList.remove('resizing');
        });

        // Remove event listeners
        document.removeEventListener('mousemove', this.resize.bind(this));
        document.removeEventListener('mouseup', this.stopResize.bind(this));

        // Store the current dimensions to make them persistent
        if (this.currentResizeBlock) {
            this.currentResizeBlock.dataset.lastWidth = this.currentResizeBlock.style.width;
            this.currentResizeBlock.dataset.lastHeight = this.currentResizeBlock.style.height;
        }
    },

    // Find a code token in the file
    findCodeToken: function(token) {
        // Send a message to the extension to find the token
        if (window.vscode) {
            window.vscode.postMessage({
                type: 'findCodeToken',
                token: token
            });
        }
    },

    // Apply syntax highlighting to all code blocks
    highlightAll: function() {
        if (typeof Prism !== 'undefined') {
            Prism.highlightAll();
        }
    },

    // Process code blocks in content
    processCodeBlocks: function(content) {
        if (!content) return '';

        // Regular expression to match code blocks with language specification
        const codeBlockRegex = /```([a-zA-Z0-9_+-]*)(\r?\n[\s\S]*?\r?\n)```/g;

        // Replace code blocks with our enhanced UI
        return content.replace(codeBlockRegex, (_, language, code) => {
            // Clean up the code (remove first and last newlines)
            const cleanCode = code.replace(/^\r?\n/, '').replace(/\r?\n$/, '');

            // Determine if this is a terminal command block
            const isTerminal = language === 'bash' || language === 'sh' || language === 'shell' || language === 'cmd';

            // Create a unique ID for this code block
            const blockId = 'code-block-' + Date.now() + '-' + Math.random().toString(36).substring(2, 9);

            // Determine the file extension and path (for file blocks)
            let fileName = '';
            let filePath = '';

            if (!isTerminal && language) {
                // For code blocks, use the language as the file extension
                fileName = `example.${language}`;
            }

            // Create the code block HTML
            return this.createCodeBlockHTML(blockId, fileName, filePath, cleanCode, language, isTerminal);
        });
    },

    // Create HTML for a code block
    createCodeBlockHTML: function(blockId, fileName, filePath, code, language, isTerminal) {
        // Determine the block class based on type
        const blockClass = isTerminal ? 'code-block terminal-block' : 'code-block';

        // Determine the header title
        const headerTitle = isTerminal ? 'Terminal' : fileName;

        // Create the HTML
        return `
            <div id="${blockId}" class="${blockClass}">
                <div class="code-block-header">
                    <div class="code-block-title">
                        <button class="code-block-collapse" onclick="window.codeBlockFunctions.toggleCodeBlock('${blockId}')">−</button>
                        <span class="code-block-filename">${headerTitle}</span>
                        ${filePath ? `<span class="code-block-path">${filePath}</span>` : ''}
                    </div>
                    <div class="code-block-stats">
                        ${!isTerminal ? `
                            <span class="code-block-added">+0</span>
                            <span class="code-block-removed">-0</span>
                        ` : ''}
                        <div class="code-block-actions">
                            ${!isTerminal ? `<button class="code-block-action-button" disabled>Apply</button>` : ''}
                            ${!isTerminal ? `<button class="code-block-action-button" disabled>Undo</button>` : ''}
                        </div>
                    </div>
                </div>
                <div class="code-block-content">
                    <pre><code class="language-${language || 'plaintext'}">${this.escapeHtml(code)}</code></pre>
                    <div class="code-block-resize-handle" onmousedown="window.codeBlockFunctions.startResize('${blockId}', event)"></div>
                </div>
            </div>
        `;
    },

    // Process code tokens in content
    processCodeTokens: function(content) {
        if (!content) return '';

        // Regular expression to match code tokens `SomeCodeEntity`
        const tokenRegex = /`([^`]+)`/g;

        // Replace tokens with clickable spans
        return content.replace(tokenRegex, (_, token) => {
            return `<span class="code-token" onclick="window.codeBlockFunctions.findCodeToken('${token}')"><code>${token}</code></span>`;
        });
    },

    // Escape HTML special characters
    escapeHtml: function(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, m => map[m]);
    },

    // Apply changes to a file
    applyChanges: function(blockId, filePath) {
        const block = document.getElementById(blockId);
        if (!block) return;

        // Get the code content
        const codeElement = block.querySelector('code');
        if (!codeElement) return;

        const code = codeElement.textContent;

        // Send a message to the extension to apply the changes
        if (window.vscode) {
            vscode.postMessage({
                type: 'applyChanges',
                blockId: blockId,
                filePath: filePath,
                code: code
            });
        }
    },

    // Undo changes to a file
    undoChanges: function(blockId, filePath) {
        // Send a message to the extension to undo the changes
        if (window.vscode) {
            vscode.postMessage({
                type: 'undoChanges',
                blockId: blockId,
                filePath: filePath
            });
        }
    },

    // Update the UI after applying changes
    updateAfterApply: function(blockId, addedLines, removedLines) {
        const block = document.getElementById(blockId);
        if (!block) return;

        // Enable the Undo button and disable the Apply button
        const applyButton = block.querySelector('.code-block-actions button:first-child');
        const undoButton = block.querySelector('.code-block-actions button:last-child');

        if (applyButton) applyButton.disabled = true;
        if (undoButton) undoButton.disabled = false;

        // Update the line change indicators
        const addedSpan = block.querySelector('.code-block-added');
        const removedSpan = block.querySelector('.code-block-removed');

        if (addedSpan && typeof addedLines === 'number') {
            addedSpan.textContent = `+${addedLines}`;
            // Add a highlight effect
            addedSpan.classList.add('highlight');
            setTimeout(() => {
                addedSpan.classList.remove('highlight');
            }, 2000);
        }

        if (removedSpan && typeof removedLines === 'number') {
            removedSpan.textContent = `-${removedLines}`;
            // Add a highlight effect
            removedSpan.classList.add('highlight');
            setTimeout(() => {
                removedSpan.classList.remove('highlight');
            }, 2000);
        }

        // Show a success message
        this.showNotification(blockId, 'Changes applied successfully', 'success');
    },

    // Update the UI after undoing changes
    updateAfterUndo: function(blockId) {
        const block = document.getElementById(blockId);
        if (!block) return;

        // Enable the Apply button and disable the Undo button
        const applyButton = block.querySelector('.code-block-actions button:first-child');
        const undoButton = block.querySelector('.code-block-actions button:last-child');

        if (applyButton) applyButton.disabled = false;
        if (undoButton) undoButton.disabled = true;

        // Reset the line change indicators
        const addedSpan = block.querySelector('.code-block-added');
        const removedSpan = block.querySelector('.code-block-removed');

        if (addedSpan) addedSpan.textContent = '+0';
        if (removedSpan) removedSpan.textContent = '-0';

        // Show a success message
        this.showNotification(blockId, 'Changes undone', 'info');
    },

    // Show a notification in the code block
    showNotification: function(blockId, message, type = 'info') {
        const block = document.getElementById(blockId);
        if (!block) return;

        // Create a notification element
        const notification = document.createElement('div');
        notification.className = `code-block-notification ${type}`;
        notification.textContent = message;

        // Add the notification to the code block
        block.appendChild(notification);

        // Remove the notification after a delay
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => {
                if (notification.parentNode === block) {
                    notification.remove();
                }
            }, 500);
        }, 3000);
    }
};

// Listen for messages from the extension
window.addEventListener('message', function(event) {
    const message = event.data;

    // Handle code block updates
    if (message.type === 'updateCodeBlock' && message.blockId) {
        if (message.action === 'applied') {
            window.codeBlockFunctions.updateAfterApply(
                message.blockId,
                message.addedLines,
                message.removedLines
            );
        } else if (message.action === 'undone') {
            window.codeBlockFunctions.updateAfterUndo(message.blockId);
        }
    }
});

// Initialize highlighting when the page loads
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        window.codeBlockFunctions.highlightAll();

        // Add scroll event listeners to all code blocks
        document.querySelectorAll('.code-block-content').forEach(content => {
            content.addEventListener('scroll', function() {
                // Add scrolling class when scrolling starts
                this.closest('.code-block').classList.add('scrolling');

                // Clear any existing timeout
                if (this.scrollTimeout) {
                    clearTimeout(this.scrollTimeout);
                }

                // Set a timeout to remove the class after scrolling stops
                this.scrollTimeout = setTimeout(() => {
                    this.closest('.code-block').classList.remove('scrolling');
                }, 1000); // 1 second after scrolling stops
            });

            // Also add the same for the pre element inside
            const preElement = content.querySelector('pre');
            if (preElement) {
                preElement.addEventListener('scroll', function() {
                    // Add scrolling class when scrolling starts
                    content.closest('.code-block').classList.add('scrolling');

                    // Clear any existing timeout
                    if (this.scrollTimeout) {
                        clearTimeout(this.scrollTimeout);
                    }

                    // Set a timeout to remove the class after scrolling stops
                    this.scrollTimeout = setTimeout(() => {
                        content.closest('.code-block').classList.remove('scrolling');
                    }, 1000); // 1 second after scrolling stops
                });
            }
        });
    }, 0);
});
