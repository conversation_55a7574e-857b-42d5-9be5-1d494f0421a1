from typing import List, Dict
import numpy as np

class DocumentRanker:
    def merge_and_rank_docs(
        self, 
        initial_docs: List[Dict], 
        enhanced_docs: List[Dict],
        concepts: List[ConceptNode]
    ) -> List[Dict]:
        """Merge and rank documents using multiple factors"""
        all_docs = self._remove_duplicates(initial_docs + enhanced_docs)
        
        for doc in all_docs:
            doc['rank_score'] = self._calculate_rank_score(
                doc=doc,
                concepts=concepts,
                relevance_score=doc.get('relevance_score', 0.0),
                semantic_score=self._calculate_semantic_score(doc),
                recency_score=self._calculate_recency_score(doc)
            )
            
        # Sort by rank score
        ranked_docs = sorted(
            all_docs, 
            key=lambda x: x['rank_score'], 
            reverse=True
        )
        
        return ranked_docs

    def _calculate_rank_score(
        self, 
        doc: Dict,
        concepts: List[ConceptNode],
        relevance_score: float,
        semantic_score: float,
        recency_score: float
    ) -> float:
        """Calculate final rank score using weighted factors"""
        weights = {
            'relevance': 0.4,
            'semantic': 0.3,
            'concept_match': 0.2,
            'recency': 0.1
        }
        
        concept_match_score = self._calculate_concept_match_score(
            doc, 
            concepts
        )
        
        return (
            weights['relevance'] * relevance_score +
            weights['semantic'] * semantic_score +
            weights['concept_match'] * concept_match_score +
            weights['recency'] * recency_score
        )