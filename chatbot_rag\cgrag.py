class CGRAGProcessor:
    def __init__(self, llm, retriever):
        self.llm = llm
        self.retriever = retriever
        
    async def process_query(self, query: str) -> Tuple[str, List[Document]]:
        # First pass: Concept identification
        concept_prompt = f"""Identify key concepts and technical terms from this query 
        that would be most relevant for retrieving accurate information:
        Query: {query}
        Output format: comma-separated list of terms"""
        
        concepts = await self.llm.agenerate([concept_prompt])
        
        # Second pass: Enhanced retrieval
        enhanced_query = f"""Original query: {query}
        Related technical concepts: {concepts}
        Please provide comprehensive information about these specific aspects."""
        
        relevant_docs = await self.retriever.aget_relevant_documents(enhanced_query)
        return enhanced_query, relevant_docs