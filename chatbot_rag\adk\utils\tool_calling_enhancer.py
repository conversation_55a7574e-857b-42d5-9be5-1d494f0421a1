"""
Tool calling enhancer for models that don't natively support tool calling.

This module provides utilities to enhance tool calling capabilities for models
that don't natively support structured tool calling (like local models via Ollama).
"""

import json
import re
import logging
from typing import Dict, Any, List, Optional, Tuple

from chatbot_rag.adk.utils.tool_cache import get_tool_cache

# Configure logging
logger = logging.getLogger("tool_calling_enhancer")

class ToolCallingEnhancer:
    """
    Enhances tool calling capabilities for models without native tool support.

    This class provides methods to:
    1. Format prompts with tool descriptions
    2. Parse tool calls from unstructured text responses
    3. Execute tool calls and format results
    4. Handle multi-turn tool calling conversations
    """

    def __init__(self, temperature: float = 0.2):
        """
        Initialize the tool calling enhancer.

        Args:
            temperature: Temperature to use for tool calling prompts (lower is better for structured outputs)
        """
        self.temperature = temperature

    def format_tool_prompt(self, prompt: str, tools: List[Dict[str, Any]]) -> str:
        """
        Format a prompt with tool descriptions for models without native tool support.

        Args:
            prompt: The original user prompt
            tools: List of tool definitions

        Returns:
            A formatted prompt with tool descriptions
        """
        if not tools:
            return prompt

        # Create a detailed tool description
        tool_descriptions = "You have access to the following tools:\n\n"

        for i, tool in enumerate(tools):
            tool_descriptions += f"Tool {i+1}: {tool['name']}\n"
            tool_descriptions += f"Description: {tool['description']}\n"

            # Add parameter descriptions
            if "parameters" in tool and "properties" in tool["parameters"]:
                tool_descriptions += "Parameters:\n"
                for param_name, param_info in tool["parameters"]["properties"].items():
                    required = "Required" if "required" in tool["parameters"] and param_name in tool["parameters"]["required"] else "Optional"
                    param_type = param_info.get("type", "any")
                    param_desc = param_info.get("description", "")
                    tool_descriptions += f"  - {param_name} ({param_type}, {required}): {param_desc}\n"

            tool_descriptions += "\n"

        # Add instructions for using tools
        tool_instructions = """
To use a tool, respond with a JSON object in the following format:
```json
{
  "tool": "tool_name",
  "parameters": {
    "param1": "value1",
    "param2": "value2",
    ...
  }
}
```

You can also use multiple tools in sequence by providing multiple JSON objects, one per tool call.

If you don't need to use any tools to answer the question, just respond normally.

IMPORTANT: When using tools, ONLY respond with the JSON object(s) for the tool call(s). Do not include any other text before or after the JSON.
"""

        # Combine everything
        enhanced_prompt = f"{tool_descriptions}\n{tool_instructions}\n\nUser Query: {prompt}\n\nResponse:"

        return enhanced_prompt

    def extract_tool_calls(self, response: str) -> Tuple[List[Dict[str, Any]], str]:
        """
        Extract tool calls from a model response.

        Args:
            response: The model's response text

        Returns:
            Tuple of (list of extracted tool calls, remaining text)
        """
        tool_calls = []
        remaining_text = response

        # Try to extract JSON objects from the response
        # First, look for JSON blocks with code fences
        json_blocks = re.findall(r'```(?:json)?\s*({[\s\S]*?})\s*```', response)

        if json_blocks:
            # Process each JSON block
            for json_block in json_blocks:
                try:
                    # Parse the JSON
                    tool_call = json.loads(json_block)

                    # Check if it's a valid tool call
                    if "tool" in tool_call and "parameters" in tool_call:
                        tool_calls.append({
                            "name": tool_call["tool"],
                            "args": tool_call["parameters"]
                        })

                        # Remove the JSON block from the remaining text
                        remaining_text = remaining_text.replace(f"```json\n{json_block}\n```", "", 1)
                        remaining_text = remaining_text.replace(f"```\n{json_block}\n```", "", 1)
                        remaining_text = remaining_text.replace(f"```{json_block}```", "", 1)
                except json.JSONDecodeError:
                    logger.warning(f"Failed to parse JSON block: {json_block}")

        # If no JSON blocks with code fences, try to find raw JSON objects
        if not tool_calls:
            # Look for patterns that might be JSON objects
            json_patterns = re.findall(r'({[\s\S]*?})', response)

            for pattern in json_patterns:
                try:
                    # Try to parse as JSON
                    tool_call = json.loads(pattern)

                    # Check if it's a valid tool call
                    if "tool" in tool_call and "parameters" in tool_call:
                        tool_calls.append({
                            "name": tool_call["tool"],
                            "args": tool_call["parameters"]
                        })

                        # Remove the JSON pattern from the remaining text
                        remaining_text = remaining_text.replace(pattern, "", 1)
                except json.JSONDecodeError:
                    # Not a valid JSON object, continue
                    continue

        # Clean up remaining text
        remaining_text = remaining_text.strip()

        return tool_calls, remaining_text

    def format_tool_result(self, tool_name: str, result: Any) -> str:
        """
        Format a tool result for inclusion in the next prompt.

        Args:
            tool_name: The name of the tool that was called
            result: The result returned by the tool

        Returns:
            A formatted string describing the tool result
        """
        # Convert result to string if it's not already
        if isinstance(result, dict) or isinstance(result, list):
            result_str = json.dumps(result, indent=2)
        else:
            result_str = str(result)

        # Format the result
        formatted_result = f"""
Tool: {tool_name}
Result:
```
{result_str}
```

Based on the tool result above, please provide your response or make additional tool calls if needed.
"""

        return formatted_result

    async def process_with_tools(
        self,
        model_interface: Any,
        prompt: str,
        tools: List[Dict[str, Any]],
        max_tool_calls: int = 5,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process a prompt with tools using a model that doesn't natively support tool calling.

        Args:
            model_interface: The model interface to use
            prompt: The user's prompt
            tools: List of tool definitions
            max_tool_calls: Maximum number of tool calls to allow
            **kwargs: Additional arguments to pass to the model

        Returns:
            A dictionary containing the response and tool calls
        """
        # Format the initial prompt with tool descriptions
        current_prompt = self.format_tool_prompt(prompt, tools)

        # Keep track of the conversation and tool calls
        conversation_log = []
        all_tool_calls = []
        final_response = ""

        # Create a tool map for easy lookup
        tool_map = {tool["name"]: tool for tool in tools}

        # Process the conversation with tool calls
        for turn in range(max_tool_calls + 1):  # +1 for the final response
            # Get a response from the model
            model_response = await model_interface.generate_content(
                current_prompt,
                temperature=self.temperature,
                **kwargs
            )

            # Extract tool calls from the response
            tool_calls, remaining_text = self.extract_tool_calls(model_response.text)

            # Add to conversation log
            conversation_log.append({
                "role": "assistant",
                "content": model_response.text
            })

            # If no tool calls or we've reached the maximum, we're done
            if not tool_calls or turn == max_tool_calls:
                final_response = model_response.text if not tool_calls else remaining_text
                break

            # Process each tool call
            for tool_call in tool_calls:
                # Add to the list of all tool calls
                all_tool_calls.append(tool_call)

                # Get the tool definition
                tool_name = tool_call["name"]
                if tool_name not in tool_map:
                    logger.warning(f"Tool '{tool_name}' not found in tool definitions")
                    tool_result = f"Error: Tool '{tool_name}' not found"
                else:
                    # Get the tool function from the tool definition
                    tool_def = tool_map[tool_name]

                    # Check if the tool has a function attribute (for execution)
                    if hasattr(tool_def, 'function') and callable(tool_def.function):
                        try:
                            # Get the function
                            func = tool_def.function

                            # Get the tool cache
                            tool_cache = get_tool_cache()

                            # Check if we should skip caching (for non-deterministic tools)
                            skip_cache = getattr(tool_def, 'skip_cache', False)

                            # Try to get from cache if caching is enabled
                            cache_hit = False
                            if not skip_cache:
                                hit, cached_result = tool_cache.get(tool_name, tool_call["args"])
                                if hit:
                                    logger.info(f"Using cached result for tool '{tool_name}'")
                                    tool_result = cached_result
                                    cache_hit = True  # Mark that we got a cache hit

                            # Only execute the function if we didn't get a cache hit
                            if not cache_hit:
                                # Check if it's an async function
                                import inspect
                                if inspect.iscoroutinefunction(func):
                                    # Execute async function
                                    tool_result = await func(**tool_call["args"])
                                else:
                                    # Execute non-async function
                                    tool_result = func(**tool_call["args"])

                            # Cache the result if caching is enabled and we executed the function
                            if not skip_cache and not cache_hit:
                                tool_cache.set(tool_name, tool_call["args"], tool_result)
                        except Exception as e:
                            logger.error(f"Error executing tool '{tool_name}': {str(e)}")
                            tool_result = f"Error executing tool '{tool_name}': {str(e)}"
                    else:
                        # No function to execute, return a placeholder
                        logger.warning(f"Tool '{tool_name}' has no executable function")
                        tool_result = f"Tool '{tool_name}' cannot be executed (no function defined)"

                # Format the tool result
                tool_result_prompt = self.format_tool_result(tool_name, tool_result)

                # Add to conversation log
                conversation_log.append({
                    "role": "system",
                    "content": tool_result_prompt
                })

                # Update the prompt for the next turn
                current_prompt += f"\n\n{tool_result_prompt}"

        # Return the final response and tool calls
        return {
            "response": final_response,
            "tool_calls": all_tool_calls,
            "conversation": conversation_log
        }

    def extract_json_from_text(self, text: str) -> Optional[Dict[str, Any]]:
        """
        Extract a JSON object from text.

        Args:
            text: The text to extract JSON from

        Returns:
            The extracted JSON object, or None if no valid JSON was found
        """
        # Try to find JSON objects in the text
        json_match = re.search(r'{[\s\S]*?}', text)

        if json_match:
            try:
                # Try to parse the JSON
                json_obj = json.loads(json_match.group(0))
                return json_obj
            except json.JSONDecodeError:
                return None

        return None
