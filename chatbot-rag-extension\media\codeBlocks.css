/* Code block styling */
.code-block {
    margin: 10px 0;
    border-radius: 6px;
    overflow: hidden;
    border: 1px solid var(--vscode-editor-lineHighlightBorder);
    background-color: var(--vscode-editor-background);
    position: relative;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    /* Add transition for scrollbar hover effects */
    transition: all 0.3s ease;
}

/* Code block header */
.code-block-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 10px;
    background-color: var(--vscode-editorGroupHeader-tabsBackground);
    border-bottom: 1px solid var(--vscode-editor-lineHighlightBorder);
    font-family: var(--vscode-editor-font-family);
    font-size: 12px;
}

.code-block-title {
    display: flex;
    align-items: center;
    gap: 8px;
}

.code-block-collapse {
    background: none;
    border: none;
    color: var(--vscode-foreground);
    cursor: pointer;
    padding: 0;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
}

.code-block-filename {
    font-weight: bold;
    color: var(--vscode-foreground);
    cursor: pointer;
}

.code-block-path {
    color: var(--vscode-descriptionForeground);
    font-style: italic;
    margin-left: 4px;
}

/* Code block stats */
.code-block-stats {
    display: flex;
    align-items: center;
    gap: 8px;
}

.code-block-added {
    color: #4CAF50;
}

.code-block-removed {
    color: #F44336;
}

/* Code block actions */
.code-block-actions {
    display: flex;
    gap: 8px;
}

.code-block-action-button {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: none;
    border-radius: 3px;
    padding: 2px 6px;
    font-size: 11px;
    cursor: pointer;
}

.code-block-action-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Code block content */
.code-block-content {
    max-height: 300px;
    position: relative;
    display: flex;
    flex-direction: column;
    /* Remove overflow: auto to prevent external scrollbars */
    overflow: hidden;
    /* Ensure proper display when toggling */
    min-height: 100px;
}

/* Remove scrollbar styles for code-block-content since we're using pre scrollbars */

/* Also apply scrollbar styling to pre element */
.code-block-content pre::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

.code-block-content pre::-webkit-scrollbar-corner {
    background: transparent;
}

.code-block-content pre::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 0;
}

.code-block-content pre::-webkit-scrollbar-thumb {
    background: rgba(128, 128, 128, 0.2); /* Very transparent */
    border-radius: 4px;
    border: 2px solid transparent;
    min-height: 40px;
    transition: background 0.3s ease;
}

/* Hover state for pre scrollbars */
.code-block:hover .code-block-content pre::-webkit-scrollbar-thumb,
.code-block.scrolling .code-block-content pre::-webkit-scrollbar-thumb,
.code-block-content pre:hover::-webkit-scrollbar-thumb {
    background: var(--vscode-scrollbarSlider-background);
    border: 2px solid var(--vscode-editor-background);
}

.code-block-content pre::-webkit-scrollbar-thumb:hover {
    background: var(--vscode-scrollbarSlider-hoverBackground) !important;
}

.code-block-content pre::-webkit-scrollbar-thumb:active {
    background: var(--vscode-scrollbarSlider-activeBackground) !important;
}

.code-block-content pre {
    margin: 0;
    padding: 10px;
    font-family: var(--vscode-editor-font-family);
    font-size: 13px;
    line-height: 1.5;
    tab-size: 4;
    flex: 1;
    overflow: auto; /* Keep this to enable scrolling */
    width: 100%;
    box-sizing: border-box;
    min-height: 100%;
    /* Ensure the pre element takes up all available space */
    height: 100%;
}

.code-block-content code {
    font-family: inherit;
    display: block;
    min-width: 100%;
    white-space: pre;
}

/* Resize handle */
.code-block-resize-handle {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 14px;
    height: 14px;
    cursor: nwse-resize;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
    opacity: 0.6;
    transition: opacity 0.2s ease;
}

.code-block-resize-handle:hover {
    opacity: 1;
}

.code-block-resize-handle::after {
    content: '⤢'; /* Diagonal arrow pointing to bottom right */
    position: absolute;
    bottom: 0;
    right: 0;
    font-size: 14px;
    color: var(--vscode-descriptionForeground);
}

/* Line highlighting */
.line-added {
    background-color: rgba(80, 200, 120, 0.2);
}

.line-removed {
    background-color: rgba(255, 100, 100, 0.2);
    text-decoration: line-through;
}

.line-modified {
    background-color: rgba(255, 220, 100, 0.2);
}

/* Code token styling */
.code-token {
    color: var(--vscode-textLink-foreground);
    text-decoration: underline;
    cursor: pointer;
}

.code-token:hover {
    color: var(--vscode-textLink-activeForeground);
}

/* Terminal styling */
.terminal-block {
    background-color: var(--vscode-terminal-background, #1e1e1e);
    color: var(--vscode-terminal-foreground, #cccccc);
}

.terminal-block .code-block-header {
    background-color: var(--vscode-terminal-border, #333333);
}

.terminal-block pre {
    color: inherit;
}

/* Resizing state */
.code-block.resizing {
    user-select: none;
    pointer-events: none;
}

.code-block.resizing .code-block-content {
    transition: none;
    border: 1px dashed var(--vscode-focusBorder);
}

/* Notification styling */
.code-block-notification {
    position: absolute;
    bottom: 10px;
    right: 10px;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 100;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    animation: fadeIn 0.3s ease-in-out;
}

.code-block-notification.success {
    background-color: #4CAF50;
    color: white;
}

.code-block-notification.error {
    background-color: #F44336;
    color: white;
}

.code-block-notification.info {
    background-color: #2196F3;
    color: white;
}

.code-block-notification.warning {
    background-color: #FF9800;
    color: white;
}

.code-block-notification.fade-out {
    animation: fadeOut 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(10px); }
}

/* Highlight effect for line changes */
.code-block-added.highlight,
.code-block-removed.highlight {
    animation: pulse 1s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* Diff view styling */
.diff-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.diff-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: var(--vscode-editorGroupHeader-tabsBackground);
    border-bottom: 1px solid var(--vscode-editor-lineHighlightBorder);
}

.diff-file-path {
    font-weight: bold;
}

.diff-stats {
    display: flex;
    gap: 10px;
}

.diff-added {
    color: #4CAF50;
}

.diff-removed {
    color: #F44336;
}

.diff-content {
    flex: 1;
    overflow: auto;
    padding: 10px;
}

.diff-table {
    width: 100%;
    border-collapse: collapse;
    font-family: var(--vscode-editor-font-family);
    font-size: var(--vscode-editor-font-size);
}

.diff-line-number {
    width: 40px;
    text-align: right;
    padding: 0 5px;
    color: var(--vscode-editorLineNumber-foreground);
    border-right: 1px solid var(--vscode-editor-lineHighlightBorder);
    user-select: none;
}

.diff-line-prefix {
    width: 20px;
    text-align: center;
    user-select: none;
}

.diff-line-content {
    padding-left: 10px;
    white-space: pre;
}

.diff-line-added {
    background-color: rgba(80, 200, 120, 0.2);
}

.diff-line-added .diff-line-prefix {
    color: #4CAF50;
}

.diff-line-removed {
    background-color: rgba(255, 100, 100, 0.2);
}

.diff-line-removed .diff-line-prefix {
    color: #F44336;
}

.diff-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 10px;
    background-color: var(--vscode-editorGroupHeader-tabsBackground);
    border-top: 1px solid var(--vscode-editor-lineHighlightBorder);
}

.diff-action-button {
    padding: 6px 12px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
}

.diff-apply-button {
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
}

.diff-cancel-button {
    background-color: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);
}
