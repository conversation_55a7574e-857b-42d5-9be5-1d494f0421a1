"""
RAG-related tools for the ADK agents.
"""

import logging
from typing import Dict, Any, List, Optional

from google.adk.tools import Tool
from chatbot_rag.rag_utils import RAG_orchestrator

# Configure logging
logger = logging.getLogger("rag_tools")

async def search_code(query: str, selected_subjects: Optional[List[str]] = None) -> Dict[str, Any]:
    """
    Search for code in the codebase using RAG.
    
    Args:
        query: The search query.
        selected_subjects: Optional list of subjects to search in.
        
    Returns:
        A dictionary containing the search results.
    """
    try:
        # Initialize the RAG orchestrator
        orchestrator = RAG_orchestrator()
        
        # Get RAG context
        context, rag_chain = await orchestrator.get_RAG(
            query,
            selected_subjects or []
        )
        
        return {
            "query": query,
            "context": context,
            "selected_subjects": selected_subjects or [],
            "has_results": bool(context.strip())
        }
    except Exception as e:
        logger.error(f"Error searching code: {str(e)}")
        return {
            "query": query,
            "error": str(e),
            "context": "",
            "has_results": False
        }

# Create ADK Tool object
search_code_tool = Tool(
    name="search_code",
    description="Search for code in the codebase using RAG",
    function=search_code
)
