/* Code token styling for RAG Assistant */

/* Code token base styling */
.code-token {
    cursor: pointer;
    position: relative;
    padding: 0;
    border-radius: 3px;
    transition: background-color 0.2s ease;
}

.code-token code {
    font-family: var(--vscode-editor-font-family);
    background-color: var(--vscode-textCodeBlock-background, rgba(0, 0, 0, 0.1));
    padding: 1px 4px;
    border-radius: 3px;
    font-size: 0.9em;
}

.code-token:hover {
    background-color: rgba(100, 150, 255, 0.1);
}

.code-token:hover code {
    background-color: var(--vscode-textCodeBlock-background, rgba(0, 0, 0, 0.2));
}

/* Token type styling */
.token-type-class code {
    color: #4EC9B0;
}

.token-type-function code {
    color: #DCDCAA;
}

.token-type-variable code {
    color: #9CDCFE;
}

.token-type-interface code {
    color: #B8D7A3;
}

.token-type-type code {
    color: #4EC9B0;
}

.token-type-enum code {
    color: #B8D7A3;
}

.token-type-component code {
    color: #E06C75;
}

.token-type-module code {
    color: #C586C0;
}

.token-type-import code {
    color: #C586C0;
}

.token-type-css code {
    color: #CE9178;
}

.token-type-html code {
    color: #569CD6;
}

.token-type-json code {
    color: #D7BA7D;
}

/* Token state styling */
.token-found code {
    background-color: rgba(80, 200, 120, 0.2);
    transition: background-color 0.3s ease;
}

.token-not-found code {
    background-color: rgba(255, 100, 100, 0.2);
    transition: background-color 0.3s ease;
}

/* Token indicator styling */
.token-indicator {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    z-index: 10;
    animation: fadeIn 0.3s ease-in-out;
}

.token-indicator.success {
    background-color: #4CAF50;
    color: white;
}

.token-indicator.error {
    background-color: #F44336;
    color: white;
}

/* Token tooltip styling */
.token-tooltip {
    position: absolute;
    z-index: 1000;
    background-color: var(--vscode-editor-background);
    border: 1px solid var(--vscode-editor-lineHighlightBorder);
    border-radius: 4px;
    padding: 8px;
    font-size: 12px;
    max-width: 300px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    animation: fadeIn 0.2s ease-in-out;
}

.token-tooltip-header {
    font-weight: bold;
    margin-bottom: 4px;
    color: var(--vscode-foreground);
}

.token-tooltip-type {
    color: var(--vscode-descriptionForeground);
    margin-bottom: 4px;
}

.token-tooltip-file {
    color: var(--vscode-descriptionForeground);
    font-style: italic;
    margin-bottom: 4px;
    font-size: 11px;
}

.token-tooltip-description {
    color: var(--vscode-foreground);
    margin-top: 4px;
    font-size: 11px;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(5px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Token history panel */
.token-history-panel {
    margin-top: 10px;
    padding: 8px;
    background-color: var(--vscode-editor-background);
    border: 1px solid var(--vscode-editor-lineHighlightBorder);
    border-radius: 4px;
}

.token-history-title {
    font-weight: bold;
    margin-bottom: 6px;
    color: var(--vscode-foreground);
    font-size: 12px;
}

.token-history-list {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

.token-history-item {
    padding: 2px 6px;
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border-radius: 3px;
    font-size: 11px;
    cursor: pointer;
}

.token-history-item:hover {
    background-color: var(--vscode-button-hoverBackground);
}
