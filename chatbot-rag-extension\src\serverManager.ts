import * as vscode from 'vscode';
import * as path from 'path';
import { SettingsManager } from './settingsManager';

export class ServerManager {
    private static _instance: ServerManager;
    private _terminal: vscode.Terminal | undefined;
    private _isServerRunning: boolean = false;
    private _statusBarItem: vscode.StatusBarItem;

    private constructor() {
        // Create status bar item
        this._statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
        this._statusBarItem.command = 'chatbot-rag.checkServerStatus';
        this._statusBarItem.text = '$(server) RAG: Unknown';
        this._statusBarItem.tooltip = 'RAG Server Status - Click to check';
        this._statusBarItem.show();

        // Update status bar initially
        this.updateStatusBar();
    }

    public static getInstance(): ServerManager {
        if (!ServerManager._instance) {
            ServerManager._instance = new ServerManager();
        }
        return ServerManager._instance;
    }

    public get isServerRunning(): boolean {
        return this._isServerRunning;
    }

    /**
     * Update the status bar item based on server status
     */
    private updateStatusBar(): void {
        if (this._isServerRunning) {
            this._statusBarItem.text = '$(check) RAG: Running';
            this._statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.warningBackground');
            this._statusBarItem.tooltip = 'RAG Server is running - Click to check status';
        } else {
            this._statusBarItem.text = '$(error) RAG: Stopped';
            this._statusBarItem.backgroundColor = undefined;
            this._statusBarItem.tooltip = 'RAG Server is not running - Click to check status';
        }
    }

    /**
     * Start the FastAPI server in a terminal
     * @returns A promise that resolves when the server is started
     */
    public async startServer(): Promise<boolean> {
        try {
            // Check if server is already running
            if (this._isServerRunning) {
                vscode.window.showInformationMessage('RAG server is already running');
                return true;
            }

            // Find the workspace folder
            const workspaceFolders = vscode.workspace.workspaceFolders;
            if (!workspaceFolders) {
                vscode.window.showErrorMessage('No workspace folder found');
                return false;
            }

            // Get the workspace root
            const workspaceRoot = workspaceFolders[0].uri.fsPath;

            // Find the chatbot_rag folder
            const chatbotRagPath = path.join(workspaceRoot, 'chatbot_rag');

            // Check if the folder exists
            try {
                await vscode.workspace.fs.stat(vscode.Uri.file(chatbotRagPath));
            } catch (error) {
                vscode.window.showErrorMessage(`Could not find chatbot_rag folder at ${chatbotRagPath}`);
                return false;
            }

            // Create a terminal
            this._terminal = vscode.window.createTerminal('RAG Server');

            // Navigate to the chatbot_rag folder
            this._terminal.sendText(`cd "${chatbotRagPath}"`);

            // Activate the virtual environment if it exists
            this._terminal.sendText('if (Test-Path .venv\\Scripts\\Activate.ps1) { .venv\\Scripts\\Activate.ps1 } elseif (Test-Path venv\\Scripts\\Activate.ps1) { venv\\Scripts\\Activate.ps1 }');

            // Start the FastAPI server
            this._terminal.sendText('python -m uvicorn api.main:app --reload --host 0.0.0.0 --port 8000');

            // Show the terminal
            this._terminal.show();

            // Set the server as running
            this._isServerRunning = true;

            // Update status bar
            this.updateStatusBar();

            // Show a message
            vscode.window.showInformationMessage('RAG server started successfully');

            return true;
        } catch (error) {
            vscode.window.showErrorMessage(`Failed to start RAG server: ${error instanceof Error ? error.message : 'Unknown error'}`);
            return false;
        }
    }

    /**
     * Stop the FastAPI server
     */
    public async stopServer(): Promise<void> {
        // First check if the server is actually running
        const isRunning = await this.checkServerStatus();

        if (isRunning) {
            // Find all terminals and look for one that might be running the server
            const terminals = vscode.window.terminals;
            const serverTerminal = terminals.find(t => t.name === 'RAG Server') || this._terminal;

            if (serverTerminal) {
                // Send Ctrl+C to stop the server
                serverTerminal.sendText('\u0003');

                // Wait a moment for the server to stop
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Close the terminal
                serverTerminal.dispose();
                this._terminal = undefined;

                // Set the server as not running
                this._isServerRunning = false;

                // Update status bar
                this.updateStatusBar();

                // Show a message
                vscode.window.showInformationMessage('RAG server stopped');
            } else {
                // Server is running but we can't find the terminal
                // Try to kill the process using the OS
                try {
                    // On Windows, we can use taskkill to kill the process
                    const terminal = vscode.window.createTerminal('Kill Server');
                    terminal.sendText('taskkill /F /IM uvicorn.exe');
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    terminal.dispose();

                    // Set the server as not running
                    this._isServerRunning = false;
                    this.updateStatusBar();

                    vscode.window.showInformationMessage('RAG server process terminated');
                } catch (error) {
                    console.error('Error killing server process:', error);
                    vscode.window.showErrorMessage('Failed to stop the server process. You may need to stop it manually.');
                }
            }
        } else {
            vscode.window.showInformationMessage('No RAG server is running');
        }
    }

    /**
     * Check if the server is running by making a request to the health endpoint
     * @returns A promise that resolves to true if the server is running
     */
    public async checkServerStatus(): Promise<boolean> {
        try {
            const settings = SettingsManager.getSettings();
            const apiUrl = settings.apiUrl;

            // Make a request to the health endpoint
            const response = await fetch(`${apiUrl}/health`, {
                method: 'GET',
                headers: { 'Accept': 'application/json' }
            });

            if (response.ok) {
                this._isServerRunning = true;
                this.updateStatusBar();
                return true;
            } else {
                this._isServerRunning = false;
                this.updateStatusBar();
                return false;
            }
        } catch (error) {
            console.error('Error checking server status:', error);
            this._isServerRunning = false;
            this.updateStatusBar();
            return false;
        }
    }
}
