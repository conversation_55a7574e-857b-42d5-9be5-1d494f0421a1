"""
Codebase Understanding Agent implementation using Google's Agent Development Kit.

This specialized agent builds and maintains a mental model of the codebase structure,
understands relationships between components, and provides deeper insights about code.
"""

import logging
import os
import json
import datetime
from typing import Dict, Any, List, Optional, Tuple

from google.adk.agents import Agent
from google.adk.models import GeminiModel
from google.adk.tools import Tool

from chatbot_rag.rag_utils import RAG_orchestrator
from chatbot_rag.adk.tools.rag_tools import search_code_tool
from chatbot_rag.adk.utils.adk_utils import initialize_model

# Constants
DATA_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'data')
CONTEXT_FILE = os.path.join(DATA_DIR, 'codebase_context.json')

# Configure logging
logger = logging.getLogger("codebase_understanding_agent")

class CodebaseUnderstandingAgent:
    """
    Agent specialized in understanding codebase structure and relationships.
    """

    def __init__(self, model: Optional[GeminiModel] = None):
        """
        Initialize the Codebase Understanding Agent.

        Args:
            model: Optional GeminiModel instance. If not provided, a default model will be used.
        """
        # Initialize the model if not provided
        self.model = model or initialize_model()

        # Create custom tools for codebase understanding
        self.tools = self._create_tools()

        # Initialize the agent
        self.agent = Agent(
            name="codebase_understanding_agent",
            model=self.model,
            description="I understand codebase structure, relationships, and patterns to provide deeper code insights.",
            instruction="""
            You are a codebase understanding expert. Your job is to:

            1. Build and maintain a mental model of the codebase structure
            2. Understand relationships between components (imports, inheritance, dependencies)
            3. Identify architectural patterns and design principles
            4. Map functionality to specific code locations
            5. Provide insights about code organization and structure

            When analyzing a codebase:
            - Focus on understanding the big picture first, then dive into details
            - Pay attention to imports and dependencies to map relationships
            - Identify key abstractions and interfaces
            - Recognize common design patterns and architectural styles
            - Consider both structure (organization) and behavior (functionality)

            Always be specific and provide concrete examples from the code.
            When you need more information, use the appropriate tools to explore the codebase.
            """,
            tools=self.tools
        )

        # Initialize codebase context (will be built up over time)
        self.codebase_context = {
            "modules": {},
            "key_components": {},
            "relationships": {},
            "patterns": [],
            "last_updated": None
        }

        # Try to load existing context from disk
        self.load_context_from_disk()

        logger.info("Codebase Understanding Agent initialized")

    def save_context_to_disk(self) -> bool:
        """
        Save the codebase context to disk for persistence across restarts.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Ensure data directory exists
            os.makedirs(DATA_DIR, exist_ok=True)

            # Update the last_updated timestamp
            self.codebase_context["last_updated"] = datetime.datetime.now().isoformat()

            # Save to file
            with open(CONTEXT_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.codebase_context, f, indent=2, ensure_ascii=False)

            logger.info(f"Codebase context saved to {CONTEXT_FILE}")
            return True

        except Exception as e:
            logger.error(f"Error saving codebase context: {str(e)}")
            return False

    def load_context_from_disk(self) -> bool:
        """
        Load the codebase context from disk.

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if os.path.exists(CONTEXT_FILE):
                with open(CONTEXT_FILE, 'r', encoding='utf-8') as f:
                    loaded_context = json.load(f)

                # Update the current context with loaded data
                self.codebase_context.update(loaded_context)

                logger.info(f"Codebase context loaded from {CONTEXT_FILE}")
                logger.info(f"Last updated: {self.codebase_context.get('last_updated', 'unknown')}")
                return True
            else:
                logger.info("No existing codebase context found. Starting fresh.")
                return False

        except Exception as e:
            logger.error(f"Error loading codebase context: {str(e)}")
            return False

    def _create_tools(self) -> List[Tool]:
        """
        Create the tools needed for codebase understanding.

        Returns:
            List of Tool objects
        """
        # Include the search_code_tool from rag_tools
        tools = [search_code_tool]

        # Add specialized tools for codebase understanding
        tools.append(Tool(
            name="scan_directory_structure",
            description="Scan the directory structure of the codebase to understand organization",
            function=self.scan_directory_structure
        ))

        tools.append(Tool(
            name="analyze_imports",
            description="Analyze imports and dependencies between files",
            function=self.analyze_imports
        ))

        tools.append(Tool(
            name="identify_key_components",
            description="Identify key components, classes, and interfaces in the codebase",
            function=self.identify_key_components
        ))

        tools.append(Tool(
            name="map_functionality",
            description="Map functionality descriptions to specific code locations",
            function=self.map_functionality
        ))

        return tools

    async def understand_codebase(self, query: str, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Analyze the codebase to answer a specific query about code structure or relationships.

        Args:
            query: The question about the codebase
            context: Optional additional context

        Returns:
            A dictionary containing the analysis results
        """
        try:
            # Create a session for this analysis
            session = self.agent.create_session()

            # Prepare the input with context about what we already know
            codebase_context_summary = self._summarize_codebase_context()

            user_input = f"""
            Please help me understand the following about the codebase:

            {query}

            Here's what we already know about the codebase structure:
            {codebase_context_summary}
            """

            # Run the agent
            response = await session.send_message(user_input)

            logger.info(f"Codebase understanding query processed: {query[:100]}...")

            # Extract and update any new codebase insights
            self._update_codebase_context(response.text, query)

            # Format the response
            return {
                "understanding": response.text,
                "thinking": response.thinking,
                "tool_calls": response.tool_calls,
                "session_id": session.session_id,
                "codebase_context": self.codebase_context
            }

        except Exception as e:
            logger.error(f"Error understanding codebase: {str(e)}")
            return {
                "error": str(e),
                "understanding": f"Error analyzing codebase: {str(e)}"
            }

    def scan_directory_structure(self, root_dir: Optional[str] = None, exclude_patterns: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Scan the directory structure of the codebase.

        Args:
            root_dir: The root directory to scan. If None, uses the current working directory.
            exclude_patterns: Patterns to exclude (e.g., ["node_modules", "*.pyc"])

        Returns:
            Dictionary with directory structure information
        """
        try:
            # Default to current working directory if not specified
            root_dir = root_dir or os.getcwd()
            exclude_patterns = exclude_patterns or ["__pycache__", "*.pyc", "node_modules", ".git", ".vscode"]

            # Initialize structure
            structure = {
                "root": root_dir,
                "directories": [],
                "files_by_type": {},
                "summary": ""
            }

            # Walk the directory
            for dirpath, dirnames, filenames in os.walk(root_dir):
                # Skip excluded directories
                dirnames[:] = [d for d in dirnames if not any(
                    pattern in d for pattern in exclude_patterns if not pattern.startswith("*")
                )]

                # Process this directory
                rel_path = os.path.relpath(dirpath, root_dir)
                if rel_path != ".":
                    structure["directories"].append(rel_path)

                # Process files
                for filename in filenames:
                    # Skip excluded files
                    if any(pattern.replace("*", "") in filename for pattern in exclude_patterns if pattern.startswith("*")):
                        continue

                    # Get file extension
                    _, ext = os.path.splitext(filename)
                    ext = ext.lstrip(".").lower() or "no_extension"

                    # Add to files by type
                    if ext not in structure["files_by_type"]:
                        structure["files_by_type"][ext] = []

                    file_path = os.path.join(rel_path, filename)
                    if file_path.startswith(".\\") or file_path.startswith("./"):
                        file_path = file_path[2:]

                    structure["files_by_type"][ext].append(file_path)

            # Generate summary
            total_files = sum(len(files) for files in structure["files_by_type"].values())
            file_type_summary = ", ".join(f"{len(files)} {ext} files" for ext, files in structure["files_by_type"].items())

            structure["summary"] = f"Found {len(structure['directories'])} directories and {total_files} files ({file_type_summary})."

            # Update codebase context
            self.codebase_context["directory_structure"] = {
                "directories": structure["directories"][:100],  # Limit to avoid too much data
                "file_types": {k: len(v) for k, v in structure["files_by_type"].items()},
                "summary": structure["summary"]
            }

            # Save updated context to disk
            self.save_context_to_disk()

            return structure

        except Exception as e:
            logger.error(f"Error scanning directory structure: {str(e)}")
            return {
                "error": str(e),
                "root": root_dir,
                "directories": [],
                "files_by_type": {},
                "summary": f"Error scanning directory structure: {str(e)}"
            }

    async def analyze_imports(self, file_paths: Optional[List[str]] = None, max_files: int = 50) -> Dict[str, Any]:
        """
        Analyze imports and dependencies between files.

        Args:
            file_paths: Specific files to analyze. If None, will sample important files.
            max_files: Maximum number of files to analyze

        Returns:
            Dictionary with import and dependency information
        """
        try:
            # Initialize results
            results = {
                "files_analyzed": [],
                "import_graph": {},
                "key_dependencies": {},
                "summary": ""
            }

            # If no file paths provided, sample important files
            if not file_paths:
                # Use directory structure if available
                if "directory_structure" in self.codebase_context:
                    file_types = self.codebase_context["directory_structure"]["file_types"]

                    # Prioritize Python, JavaScript, TypeScript files
                    priority_types = ["py", "js", "ts", "tsx", "jsx"]
                    file_paths = []

                    # Use RAG to find important files
                    orchestrator = RAG_orchestrator()
                    context, _ = await orchestrator.get_RAG("Find important files in the codebase")

                    # Extract file paths from context
                    import re
                    potential_files = re.findall(r'[\w\-./\\]+\.(py|js|ts|tsx|jsx)', context)
                    file_paths.extend([f for f in potential_files if os.path.exists(f)][:max_files])

            # Limit to max_files
            file_paths = file_paths[:max_files] if file_paths else []

            # Analyze each file
            for file_path in file_paths:
                if not os.path.exists(file_path):
                    continue

                results["files_analyzed"].append(file_path)

                # Extract imports based on file type
                imports = self._extract_imports(file_path)

                # Add to import graph
                results["import_graph"][file_path] = imports

                # Track files that are imported frequently
                for imp in imports:
                    if imp not in results["key_dependencies"]:
                        results["key_dependencies"][imp] = 0
                    results["key_dependencies"][imp] += 1

            # Sort key dependencies by frequency
            results["key_dependencies"] = dict(sorted(
                results["key_dependencies"].items(),
                key=lambda x: x[1],
                reverse=True
            )[:20])  # Keep top 20

            # Generate summary
            results["summary"] = f"Analyzed imports for {len(results['files_analyzed'])} files. " + \
                                f"Found {len(results['key_dependencies'])} key dependencies."

            # Update codebase context
            self.codebase_context["dependencies"] = {
                "key_dependencies": results["key_dependencies"],
                "import_graph_sample": {k: v for i, (k, v) in enumerate(results["import_graph"].items()) if i < 10},
                "summary": results["summary"]
            }

            # Save updated context to disk
            self.save_context_to_disk()

            return results

        except Exception as e:
            logger.error(f"Error analyzing imports: {str(e)}")
            return {
                "error": str(e),
                "files_analyzed": [],
                "import_graph": {},
                "key_dependencies": {},
                "summary": f"Error analyzing imports: {str(e)}"
            }

    def _extract_imports(self, file_path: str) -> List[str]:
        """
        Extract imports from a file based on its type.

        Args:
            file_path: Path to the file

        Returns:
            List of imported modules/packages
        """
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()

        imports = []

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            if ext == '.py':
                # Extract Python imports
                import re
                imports.extend(re.findall(r'^\s*import\s+(\w+)', content, re.MULTILINE))
                imports.extend(re.findall(r'^\s*from\s+(\w+)', content, re.MULTILINE))

            elif ext in ['.js', '.ts', '.jsx', '.tsx']:
                # Extract JavaScript/TypeScript imports
                import re
                imports.extend(re.findall(r'import.*from\s+[\'"](.+?)[\'"]', content))
                imports.extend(re.findall(r'require\([\'"](.+?)[\'"]\)', content))

        except Exception as e:
            logger.warning(f"Error extracting imports from {file_path}: {str(e)}")

        return imports

    async def identify_key_components(self, query: Optional[str] = None) -> Dict[str, Any]:
        """
        Identify key components, classes, and interfaces in the codebase.

        Args:
            query: Optional query to focus the search

        Returns:
            Dictionary with key component information
        """
        try:
            # Initialize results
            results = {
                "key_classes": [],
                "key_interfaces": [],
                "key_functions": [],
                "architectural_components": [],
                "summary": ""
            }

            # Use RAG to find key components
            orchestrator = RAG_orchestrator()
            search_query = query or "Find key classes, interfaces, and components in the codebase"
            context, _ = await orchestrator.get_RAG(search_query)

            # Extract key components using the model
            prompt = f"""
            Based on the following context from the codebase, identify:
            1. Key classes
            2. Key interfaces or abstract classes
            3. Key functions or methods
            4. Architectural components (services, controllers, etc.)

            Context:
            {context}

            Format your response as a structured list for each category.
            """

            # Use the model to extract structured information
            response = await self.model.generate_content(prompt)
            analysis = response.text if hasattr(response, 'text') else str(response)

            # Parse the response to extract components
            # This is a simplified parsing - in a real implementation, you'd want more robust parsing
            import re

            # Extract key classes
            class_section = re.search(r'Key classes:?(.*?)(?:Key interfaces|Key functions|Architectural components|$)',
                                     analysis, re.DOTALL | re.IGNORECASE)
            if class_section:
                classes = re.findall(r'[-*•]\s*(\w+)', class_section.group(1))
                results["key_classes"] = classes

            # Extract key interfaces
            interface_section = re.search(r'Key interfaces:?(.*?)(?:Key classes|Key functions|Architectural components|$)',
                                         analysis, re.DOTALL | re.IGNORECASE)
            if interface_section:
                interfaces = re.findall(r'[-*•]\s*(\w+)', interface_section.group(1))
                results["key_interfaces"] = interfaces

            # Extract key functions
            function_section = re.search(r'Key functions:?(.*?)(?:Key classes|Key interfaces|Architectural components|$)',
                                        analysis, re.DOTALL | re.IGNORECASE)
            if function_section:
                functions = re.findall(r'[-*•]\s*(\w+)', function_section.group(1))
                results["key_functions"] = functions

            # Extract architectural components
            arch_section = re.search(r'Architectural components:?(.*?)(?:Key classes|Key interfaces|Key functions|$)',
                                    analysis, re.DOTALL | re.IGNORECASE)
            if arch_section:
                components = re.findall(r'[-*•]\s*(\w+)', arch_section.group(1))
                results["architectural_components"] = components

            # Generate summary
            component_counts = [
                f"{len(results['key_classes'])} classes",
                f"{len(results['key_interfaces'])} interfaces",
                f"{len(results['key_functions'])} functions",
                f"{len(results['architectural_components'])} architectural components"
            ]
            results["summary"] = f"Identified {', '.join(component_counts)}."

            # Update codebase context
            self.codebase_context["key_components"] = {
                "classes": results["key_classes"],
                "interfaces": results["key_interfaces"],
                "functions": results["key_functions"],
                "architectural_components": results["architectural_components"],
                "summary": results["summary"]
            }

            # Save updated context to disk
            self.save_context_to_disk()

            return results

        except Exception as e:
            logger.error(f"Error identifying key components: {str(e)}")
            return {
                "error": str(e),
                "key_classes": [],
                "key_interfaces": [],
                "key_functions": [],
                "architectural_components": [],
                "summary": f"Error identifying key components: {str(e)}"
            }

    async def map_functionality(self, functionality: str) -> Dict[str, Any]:
        """
        Map a functionality description to specific code locations.

        Args:
            functionality: Description of the functionality to locate

        Returns:
            Dictionary with code locations and explanations
        """
        try:
            # Initialize results
            results = {
                "functionality": functionality,
                "primary_locations": [],
                "related_locations": [],
                "explanation": "",
                "summary": ""
            }

            # Use RAG to find relevant code
            orchestrator = RAG_orchestrator()
            context, _ = await orchestrator.get_RAG(f"Find code related to: {functionality}")

            # Use the model to analyze the context and map functionality
            prompt = f"""
            Based on the following context from the codebase, identify where the functionality "{functionality}" is implemented.

            Context:
            {context}

            Please provide:
            1. Primary code locations (files, classes, functions) that implement this functionality
            2. Related code locations that support or interact with this functionality
            3. A brief explanation of how the functionality is implemented

            Format your response as a structured list for each category.
            """

            # Use the model to extract structured information
            response = await self.model.generate_content(prompt)
            analysis = response.text if hasattr(response, 'text') else str(response)

            # Parse the response to extract locations
            import re

            # Extract primary locations
            primary_section = re.search(r'Primary code locations:?(.*?)(?:Related code locations|A brief explanation|$)',
                                       analysis, re.DOTALL | re.IGNORECASE)
            if primary_section:
                locations = re.findall(r'[-*•]\s*(.+?)(?:\n|$)', primary_section.group(1))
                results["primary_locations"] = [loc.strip() for loc in locations if loc.strip()]

            # Extract related locations
            related_section = re.search(r'Related code locations:?(.*?)(?:Primary code locations|A brief explanation|$)',
                                       analysis, re.DOTALL | re.IGNORECASE)
            if related_section:
                locations = re.findall(r'[-*•]\s*(.+?)(?:\n|$)', related_section.group(1))
                results["related_locations"] = [loc.strip() for loc in locations if loc.strip()]

            # Extract explanation
            explanation_section = re.search(r'(?:A brief explanation|explanation):?(.*?)(?:Primary code locations|Related code locations|$)',
                                          analysis, re.DOTALL | re.IGNORECASE)
            if explanation_section:
                results["explanation"] = explanation_section.group(1).strip()

            # Generate summary
            results["summary"] = f"Mapped functionality '{functionality}' to {len(results['primary_locations'])} primary locations and {len(results['related_locations'])} related locations."

            # Update codebase context with this functionality mapping
            if "functionality_map" not in self.codebase_context:
                self.codebase_context["functionality_map"] = {}

            self.codebase_context["functionality_map"][functionality] = {
                "primary_locations": results["primary_locations"],
                "related_locations": results["related_locations"][:5],  # Limit to avoid too much data
                "explanation": results["explanation"]
            }

            # Save updated context to disk
            self.save_context_to_disk()

            return results

        except Exception as e:
            logger.error(f"Error mapping functionality: {str(e)}")
            return {
                "error": str(e),
                "functionality": functionality,
                "primary_locations": [],
                "related_locations": [],
                "explanation": f"Error mapping functionality: {str(e)}",
                "summary": f"Error mapping functionality: {str(e)}"
            }

    def _summarize_codebase_context(self) -> str:
        """
        Create a summary of what we know about the codebase so far.

        Returns:
            A string summarizing the codebase context
        """
        summary_parts = []

        # Add directory structure summary
        if "directory_structure" in self.codebase_context:
            summary_parts.append(self.codebase_context["directory_structure"]["summary"])

        # Add dependencies summary
        if "dependencies" in self.codebase_context:
            summary_parts.append(self.codebase_context["dependencies"]["summary"])

        # Add key components summary
        if "key_components" in self.codebase_context:
            summary_parts.append(self.codebase_context["key_components"]["summary"])

        # Add functionality mappings
        if "functionality_map" in self.codebase_context:
            functionality_count = len(self.codebase_context["functionality_map"])
            if functionality_count > 0:
                summary_parts.append(f"Mapped {functionality_count} functionalities to specific code locations.")
                # Add a few examples
                examples = list(self.codebase_context["functionality_map"].keys())[:3]
                if examples:
                    summary_parts.append(f"Examples: {', '.join(examples)}")

        # If we have no context yet
        if not summary_parts:
            return "No information about the codebase structure has been gathered yet."

        return "\n".join(summary_parts)

    def _update_codebase_context(self, response_text: str, query: str) -> None:
        """
        Update the codebase context with new insights from the agent's response.

        Args:
            response_text: The agent's response text
            query: The original query
        """
        # This is a simplified implementation
        # In a real system, you'd want more sophisticated parsing and updating

        # Update last_updated timestamp
        self.codebase_context["last_updated"] = datetime.datetime.now().isoformat()

        # Extract potential architectural patterns
        if "patterns" not in self.codebase_context:
            self.codebase_context["patterns"] = []

        import re
        pattern_matches = re.findall(r'(?:pattern|architecture|design)\s+(?:called|known as)?\s+["\']?([A-Z][a-zA-Z\s]+?)["\']?[\s,.]',
                                    response_text)

        context_updated = False
        for pattern in pattern_matches:
            pattern = pattern.strip()
            if pattern and pattern not in self.codebase_context["patterns"]:
                self.codebase_context["patterns"].append(pattern)
                context_updated = True

        # Save context to disk if it was updated
        if context_updated:
            self.save_context_to_disk()
