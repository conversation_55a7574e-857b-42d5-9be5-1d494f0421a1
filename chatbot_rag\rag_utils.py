import datetime
import os                       # Folder handler
import streamlit as st  
import asyncio

from langchain_community.document_loaders import PyMuPDFLoader      # Load PDFs for chunks
from langchain_text_splitters import RecursiveCharacterTextSplitter # Create chunks

from langchain_ollama import OllamaEmbeddings                       # Get a model to create embedings from chunks
import faiss                                                        # DB dimensional
from langchain_community.vectorstores import FAISS                  # 
from langchain_community.docstore.in_memory import InMemoryDocstore #

# Libraries for prompting, chatting of LLM
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough
from langchain_core.prompts import ChatPromptTemplate

from langchain_ollama import ChatOllama

import logging                  # Log DEBUG
from typing import Dict, TypedDict, List    # Create variable number of arguments to methods

import uuid                     # Remap IDs of different databases
import json

class RAG_orchestrator_args(TypedDict):
    root_folder : str
    chunk_size : int
    chunk_overlap : int
    emb_model : str
    ollama_host : str
    checkbox_list: list[tuple[str,str]]
    
class RAG_orchestrator:
    """Manage the context that LLM will use to make the answer.
       The context will be captured by LLM according to the selected
       subjects from checkbox_list by user."""
    def __init__(self, **kwargs: RAG_orchestrator_args):
        """Initialize the context list with FAISS database options"""
        self.logger = logging.getLogger(self.__class__.__name__)    # To print the classname in debugger
        self.logger.debug(f" - Creating {self.__class__.__name__} object.")
        allowed_keys = {
                        "root_folder",
                        "chunk_size",
                        "chunk_overlap",
                        "emb_model",
                        "ollama_host",
                        "checkbox_list"
                        }
        # Verify if the input argument is valid
        for key in kwargs:
            if key not in allowed_keys:
                raise ValueError(f"Invalid argument: {key}")
            
        checkbox_list = [ # (label, subject)
            ("RAG C++ (books, docs, Codebase)", "C++"),
            ("RAG UML", "UML"),
            ("RAG Design Patterns", "DePat"),
            ("RAG git", "git"),
            ("RAG Linux CLI", "Linux_CLI"),
            ("RAG Underwater Acoustics", "UWA"), 
            ("Web Crawl Search", "WS")
        ]
        self.checkbox_list = kwargs.get("checkbox_list", checkbox_list)
        root_folder = os.path.join("chatbot1", "DB")
        root_folder = os.path.join(os.getcwd(), root_folder)
        self.logger.debug(" - (Document_Handler) root_folder: {root_folder}")
        self.doc_handler = Document_Handler(kwargs.get("root_folder", root_folder),
                                            kwargs.get("chunk_size", 1000),
                                            kwargs.get("chunk_overlap", 100))
        self.db_handler = DB_Handler(kwargs.get("emb_model", "nomic-embed-text"),
                                     kwargs.get("ollama_host", "http://localhost:11434/"))
        
        self.logger.debug(f" - checkbox_list: {self.checkbox_list}")
        
    def format_docs(self, docs):
        """Format documents to better readability"""
        self.logger.debug(f" - docs: {docs}")
        return "\n\n".join([doc.page_content for doc in docs])
    
    def update_database(self, subjects):
        """Update selected databases"""
        for subject in subjects:
            with st.spinner(f"Updating database of {subject}..."):
                self.doc_handler.search_pdfs(subject)
                self.doc_handler.load_pdfs()
                self.doc_handler.create_metadata(subject)
                chunks = self.doc_handler.create_chunks()
                self.logger.debug(f" - update_database: Docs searched, LLM chunks of {subject} created.")
        
                self.db_handler.add_docs(chunks=chunks)
                self.db_handler.save_database(subject=subject, db_path=self.doc_handler.root_folder)
                self.logger.debug(f"- Database of: {subject}, updated in: {self.doc_handler.root_folder}")      
    
    async def get_RAG(self, question, subjects):
        """Get the context from selected docs."""
        self.logger.debug(f".started")
        self.logger.debug(f" - subjects: {subjects}")
        self.logger.debug(f" - question: {question}")
        if subjects:    # subjects is not empty
            self.logger.debug(f" - db_path: {self.doc_handler.root_folder}")
            self.db_handler.load_database(selected_subjects=subjects, db_path=self.doc_handler.root_folder)
            context = self.db_handler.query(question)   # Get the context from the DB
            context = self.format_docs(context)         # Display the context in a fancy way
        else:
            context = ""
            self.logger.debug(f" - self.db_handler.retriever: {self.db_handler.retriever}")
            self.logger.debug(f" - self.format_docs: {self.format_docs}")
        
        # Testing a simple chain (TODO: need to be fixed. Where to put it?)
        rag_chain = self.db_handler.retriever|self.format_docs
        
        self.logger.debug(f".complete")
        self.logger.debug(f" - context: {context}")
        self.logger.debug(f" - rag_chain: {rag_chain}")
        return context, rag_chain
        
class Document_Handler:
    """It will provide a list of pdfs, docs (content in pages), and chunks (that will feed LLMs).
       Based on the root_folder, it will find documents and list all pdfs collected."""
    def __init__(self, root_folder, chunk_size, chunk_overlap):
        self.logger = logging.getLogger(self.__class__.__name__)    # To print the classname in debugger
        self.logger.debug(f" - Creating {self.__class__.__name__} object.")
        self.root_folder = root_folder
        self.chunks_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.pdfs = []
        self.docs = []
        self.chunks = []
        
        self.logger.debug(f" - root_folder: {self.root_folder}")
        self.logger.debug(f" - chunks_size: {self.chunks_size}")
        self.logger.debug(f" - chunks_overlap: {self.chunk_overlap}")
        self.logger.debug(f" - created")
    
    def search_pdfs(self, subject):
        """Walk through directories and collect all PDF files.
           Maybe it will take a while (TODO: there are more efficient methods, maybe merge).
           Modification: selected_subjects (list of strings) to subject (string)"""
        self.logger.debug(f".started")
        self.logger.debug(f" - self.root_folder: {self.root_folder}")
        self.logger.debug(f" - os.walk(self.root_folder): {list(os.walk(self.root_folder))}")
        
        # Debug the working directory and folder contents
        parent_folder = "D:\\Documents\\Python"
        self.logger.debug(f" - Current Working Directory: {os.getcwd()}")
        self.logger.debug(f" - Available folders in {parent_folder}: {os.listdir(parent_folder)}")
        
        # Check if the root folder exists
        if not os.path.exists(self.root_folder):
            logging.error(f" - Root folder does not exist: {self.root_folder}")
            return
        
        # Reset the pdfs for new subject
        self.pdfs = []
        
        # # Create a copy of list of strings selected_subjects
        # selected_subjects_ = []
        # if count_strings_in_list(selected_subjects) == 1:
        #     selected_subjects_.append(os.path.join(self.root_folder, selected_subjects))
        # elif count_strings_in_list(selected_subjects) > 1:
        #     for subject in selected_subjects:
        #         selected_subjects_.append(os.path.join(self.root_folder, subject))
        # self.logger.debug(f" - selected_subjects_: {selected_subjects_}")
        subject = [subject]
        
        self.logger.debug(f" - os.walk(os.path.join(os.getcwd(), self.root_folder)): {list(os.walk(os.path.join(os.getcwd(), self.root_folder)))}")
        self.logger.debug(f" - subject: {subject}")
        # Find the pdfs per subject
        for root, dirs, files in os.walk(os.path.join(os.getcwd(), self.root_folder)):
            self.logger.debug(f" - root: {root}, dirs: {dirs}, files: {files}")
            self.logger.debug(f" - list(map(os.path.normpath, subject)): {list(map(os.path.normpath, subject))}")
            dirs[:] = [d for d in dirs if d in subject]
            self.logger.debug(f" - dirs: {dirs}")
            for file in files:
                if file.endswith('.pdf'):
                    self.pdfs.append(os.path.join(root, file))
        self.logger.debug(f".complete. PDFs loaded is: {len(self.pdfs)}")
    
    def load_pdfs(self):
        """Load the pdfs pages for posterior create the chunks"""
        self.logger.debug(f".started")
        
        # Reset the docs for new subject
        self.docs = []
        
        for pdf in self.pdfs:
            loader = PyMuPDFLoader(pdf)
            pages = loader.load()
            
            self.docs.extend(pages)
        self.logger.debug(f".complete. The number of pages: {len(self.docs)}")
        
    def create_metadata(self, subject):
        """Add some metadata to the document database.
           The docs need to be already loaded with PyMuPDFLoader.
           This metadata will be used to filter documents to better RAG."""
        self.logger.debug(f".started")
        self.logger.debug(f" - subject: {subject}")
        for page in self.docs:
            page.metadata['subject'] = subject
            result = self.parse_pdf_metadata(page.metadata['file_path']) 
            page.metadata['type'] = result['doc_type']
            page.metadata['title'] = result['doc_name']
            
    def parse_pdf_metadata(self, file_path):
        """
        Extracts type and name from formatted PDF paths like:
        "DB\\UML\\[type]filename.pdf"
        """
        self.logger.debug(f".started")
        
        # Get just the filename with extension
        full_filename = os.path.basename(file_path)
        
        # Using string operations
        if ']' in full_filename:
            # Split type and name
            type_end = full_filename.index(']')
            doc_type = full_filename[1:type_end]  # Skip initial '['
            name_with_ext = full_filename[type_end+1:]
            name = os.path.splitext(name_with_ext)[0]
        else:
            # Fallback if no type specified
            doc_type = None
            name = os.path.splitext(full_filename)[0]
            
        self.logger.debug(f" - doc_type: {doc_type}; doc_name: {name}.")
        return {
            'doc_type': doc_type,
            'doc_name': name
        }
    
    def create_chunks(self):
        """Create the chunks that will feed the LLM from pdfs pages.
           Overlap is used to get the context properly between chunks."""
        self.logger.debug(f".started")
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size = self.chunks_size,
            chunk_overlap = self.chunk_overlap
        )
        self.chunks = text_splitter.split_documents(self.docs)
        self.logger.debug(f" - self.chunks[-1].metadata: {self.chunks[-1].metadata}")
        self.logger.debug(f".complete")
        
        return self.chunks
    
class DB_Handler:
    """Handles the Dimensional DataBase using FAISS (Facebook AI Similarity Search).
       Document Vector Embedding
       1. Operations of create embeddings,
       2. Create a vector_storage (and add documents/chunks to it),
       3. Save a DB by subject,
       4. Merge DBs indexes,
       4. Load a DB by subject/index,
       5. Query to do the Retrieval of relevant docs"""
       
    def __init__(self, emb_model, ollama_host):
        self.logger = logging.getLogger(self.__class__.__name__)    # To print the classname in debugger
        # model = "nomic-embed-text"
        # ollama_host = "http://localhost:11434"
        self.logger.debug(f" - Creating {self.__class__.__name__} object.")
        self.logger.debug(f" - emb_model: {emb_model}")
        self.logger.debug(f" - ollama_host: {ollama_host}")
        self.embeddings = OllamaEmbeddings(model=emb_model, base_url=ollama_host)
        self.logger.debug(f" - self.embeddings: {self.embeddings}")
        single_vector_of_model_embed = self.embeddings.embed_query("");     # to give the embedding size for a given model (the DB dimension)
        self.logger.debug(f" - self.embeddings: {single_vector_of_model_embed}")
        self.index = faiss.IndexFlatL2(len(single_vector_of_model_embed))   # index is model dependent (in dimension)
        self.logger.debug(f" - self.index: {self.index}")
        self.vector_store = FAISS(
            embedding_function = self.embeddings,
            index = self.index,
            docstore = InMemoryDocstore(),
            index_to_docstore_id = {}
        )
        self.retriever = self.vector_store.as_retriever(
            search_type = "mmr",
            search_kwargs = {'k': 10, 'fetch_k': 100, 'lambda_mult': 1}
        )
        
        self.logger.debug(f" - embeddings: {self.embeddings}")
        self.logger.debug(f" - vector_store: {self.vector_store}")
        self.logger.debug(f" - vector_store.index.ntotal: {self.vector_store.index.ntotal}")
        self.logger.debug(f" - DB_Handler created.")
        
    def add_docs(self, chunks):
        """ Add documents/chunks to make a FAISS DB"""
        self.logger.debug(f".started")
        self.vector_store.add_documents(documents=chunks)
    
        self.logger.debug(f".complete")
        
    def save_database(self, subject, db_path):
        """Update the database FAISS, using the subject selected by user.
        Modification: selected_subjects (list of strings) to subject (string)"""
        # self.add_docs(chunks=chunks)
        self.logger.debug(f".started")
        # for subject in subjects: # TODO: do it for each subject folder
        #     self.vector_store.save_local(os.path.join(db_path, subject), subject)
            
        #     self.logger.debug(f" - Saved subject: {subject}. The local was: {os.path.join(db_path, subject)}")
        
        self.vector_store.save_local(os.path.join(db_path, subject), subject)
        self.logger.debug(f" - Saved subject: {subject}. The local was: {os.path.join(db_path, subject)}")
        
        self.logger.debug(f".complete")
    
    def load_database(self, selected_subjects, db_path):
        """Load a saved DB .faiss and .pkl in respectively subject folder."""
        self.logger.debug(f".started")
        logging
        merged_vector_db = None
        for subject in selected_subjects:
            if merged_vector_db is None:
                merged_vector_db = FAISS.load_local(
                    os.path.join(db_path, subject),
                    index_name = subject,
                    embeddings = self.embeddings,
                    allow_dangerous_deserialization = True
                )
            else:
                db = FAISS.load_local(
                    os.path.join(db_path, subject),
                    index_name = subject,
                    embeddings = self.embeddings,
                    allow_dangerous_deserialization = True
                )
                merged_vector_db.merge_from(db)
            self.logger.debug(f" - Loaded the database: {subject}")
            
        self.vector_store = merged_vector_db
        self.logger.debug(f" - (merged) vector_store: {self.vector_store}")
        
        self.retriever = self.vector_store.as_retriever(
            search_type = "mmr",
            search_kwargs = {'k':5, 'fetch_k':100, 'lambda_mult':1}
        )
        self.logger.debug(f" - retriever: {self.retriever}")
        self.logger.debug(f".complete")
            
    def query(self, question):
        """Return the documents/chunks relevant to the question context."""
        # docs = self.vector_store.search(
        #     query = question,
        #     search_type = 'similarity'
        # )
        # return docs
        self.logger.debug(f".started")
        self.logger.debug(f" - question.template: {question.template}")
        docs = self.retriever.invoke(question.template)
        self.logger.debug(f".complete")
        
        return docs
    
# def count_strings_in_list(my_list):
#     if isinstance(my_list, str):
#         return 1  # It's a single string, treat it as one element
#     elif isinstance(my_list, list):
#         return len(my_list)  # Count elements in the list
#     else:
#         return 0  # Handle unexpected types

class DocumentProcessor:
    supported_formats = {
        'text': ['.txt', '.md', '.py', '.js', '.cpp', '.c', '.cc', '.h', '.hpp', '.rs'],
        'document': ['.pdf', '.doc', '.docx'],
        'presentation': ['.ppt', '.pptx'],
        'spreadsheet': ['.csv', '.xlsx']
    }
    
    def __init__(self, root_path: str):
        self.root_path = root_path
        self.files_metadata = []
    
    def process_directory(self) -> List[Dict]:
        """Recursively process all supported files in directory"""
        for root, _, files in os.walk(self.root_path):
            for file in files:
                ext = os.path.splitext(file)[1].lower()
                file_type = next((k for k, v in self.supported_formats.items() 
                                if ext in v), None)
                if file_type:
                    self.files_metadata.append({
                        'path': os.path.join(root, file),
                        'type': file_type,
                        'extension': ext,
                        'relative_path': os.path.relpath(root, self.root_path),
                        'created': os.path.getctime(os.path.join(root, file)),
                        'modified': os.path.getmtime(os.path.join(root, file))
                    })
        return self.files_metadata

class ChunkProcessor:
    def __init__(self, embedding_model: str):
        self.embeddings = OllamaEmbeddings(model=embedding_model)
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=100,
            length_function=len,
            is_separator_regex=False
        )
    
    def create_enhanced_chunks(self, content: str, metadata: Dict) -> List['Document']:
        """Create enhanced chunks with metadata from the content."""
        texts = self.text_splitter.split_text(content)
        chunks = []
        
        for i, text in enumerate(texts):
            chunk_metadata = {
                **metadata,
                'chunk_index': i,
                'embedding_model': self.embeddings.model,
                'chunk_timestamp': datetime.now().isoformat()
            }
            chunks.extend(self.text_splitter.create_documents(
                texts=[text],
                metadatas=[chunk_metadata]
            ))
        
        return chunks
