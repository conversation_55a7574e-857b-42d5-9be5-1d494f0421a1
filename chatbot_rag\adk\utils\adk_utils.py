"""
Utility functions for the ADK integration.
"""

import os
import logging
from typing import Dict, Any, Optional, Union

from google.adk.agents import Agent
from google.adk.sessions import Session
from google.adk.models import GeminiModel

from chatbot_rag.adk.utils.model_interface import (
    create_model_interface,
    ADKModelWrapper,
    ModelProvider,
    BaseModelInterface
)
from chatbot_rag.adk.utils.config_utils import get_default_model_info

# Configure logging
logger = logging.getLogger("adk_utils")

def initialize_model(
    provider: Optional[str] = None,
    model_name: Optional[str] = None
) -> Union[GeminiModel, ADKModelWrapper]:
    """
    Initialize a model for use with ADK.

    Args:
        provider: The model provider (google, anthropic, ollama, openai).
                 If None, uses the value from MODEL_PROVIDER environment variable or defaults from config.
        model_name: The name of the model to use.
                   If None, uses the value from MODEL_NAME environment variable or provider-specific defaults from config.

    Returns:
        An initialized model instance compatible with ADK.
    """
    try:
        # Get default model info from configuration if not provided
        if not provider or not model_name:
            default_info = get_default_model_info()
            provider = provider or default_info["provider"]
            model_name = model_name or default_info["model_name"]

        # Ensure provider is lowercase
        provider = provider.lower()

        # For backward compatibility, if provider is "google", use the original GeminiModel
        if provider == "google" and not os.environ.get("USE_MODEL_INTERFACE", ""):
            # Get API key from environment variable
            api_key = os.environ.get("GOOGLE_API_KEY")
            if not api_key:
                logger.warning("GOOGLE_API_KEY not found in environment variables. Using default configuration.")

            # Initialize the model
            model = GeminiModel(
                model_name=model_name,
                api_key=api_key,
                temperature=0.2,  # Lower temperature for more deterministic responses
                top_p=0.95,
                top_k=40,
                max_tokens=8192,
            )

            logger.info(f"Successfully initialized {model_name} model using native GeminiModel")
            return model

        # For all other providers, use our model interface
        logger.info(f"Initializing model with provider: {provider}, model: {model_name}")
        model_interface = create_model_interface(provider, model_name)

        # Wrap the model interface for ADK compatibility
        wrapped_model = ADKModelWrapper(model_interface)

        logger.info(f"Successfully initialized {model_interface.model_name} model using {provider} provider")
        return wrapped_model

    except Exception as e:
        logger.error(f"Error initializing model: {str(e)}")
        raise

def create_agent_session(agent: Agent, session_id: Optional[str] = None) -> Session:
    """
    Create a new session for an agent.

    Args:
        agent: The agent to create a session for.
        session_id: Optional session ID. If not provided, a new one will be generated.

    Returns:
        A new Session instance.
    """
    try:
        session = Session(agent=agent, session_id=session_id)
        logger.info(f"Created new session with ID: {session.session_id}")
        return session

    except Exception as e:
        logger.error(f"Error creating agent session: {str(e)}")
        raise

def format_agent_response(response: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format the agent response for the API.

    Args:
        response: The raw agent response.

    Returns:
        A formatted response dictionary.
    """
    try:
        # Extract the relevant information from the response
        formatted_response = {
            "answer": response.get("response", ""),
            "thinking": response.get("thinking", ""),
            "tool_calls": response.get("tool_calls", []),
            "session_id": response.get("session_id", ""),
        }

        # Add suggested edits if present
        if "suggested_edits" in response:
            formatted_response["suggestedEdits"] = response["suggested_edits"]

        return formatted_response

    except Exception as e:
        logger.error(f"Error formatting agent response: {str(e)}")
        return {
            "answer": f"Error formatting response: {str(e)}",
            "error": str(e)
        }
