"""
Code Analysis Agent implementation using Google's Agent Development Kit.
"""

import logging
from typing import Dict, Any, Optional

from google.adk.agents import Agent
from google.adk.models import GeminiModel

from chatbot_rag.adk.tools.code_tools import analyze_code_tool
from chatbot_rag.adk.tools.rag_tools import search_code_tool

# Configure logging
logger = logging.getLogger("code_analysis_agent")

class CodeAnalysisAgent:
    """
    Agent specialized in code analysis.
    """
    
    def __init__(self, model: Optional[GeminiModel] = None):
        """
        Initialize the Code Analysis Agent.
        
        Args:
            model: Optional GeminiModel instance. If not provided, a default model will be used.
        """
        self.agent = Agent(
            name="code_analysis_agent",
            model=model or "gemini-2.0-flash",
            description="I analyze code to identify patterns, potential bugs, and improvement opportunities.",
            instruction="""
            You are a code analysis expert. Your job is to analyze code and provide insights on:
            1. Code quality and patterns
            2. Potential bugs and issues
            3. Performance considerations
            4. Security vulnerabilities
            5. Improvement suggestions
            
            Always be specific and provide concrete examples and explanations.
            When you need more context about the codebase, use the search_code tool.
            """,
            tools=[analyze_code_tool, search_code_tool]
        )
        
        logger.info("Code Analysis Agent initialized")
    
    async def analyze(self, code: str, language: str = "python", context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Analyze the given code.
        
        Args:
            code: The code to analyze.
            language: The programming language of the code.
            context: Optional additional context.
            
        Returns:
            A dictionary containing the analysis results.
        """
        try:
            # Create a session for this analysis
            session = self.agent.create_session()
            
            # Prepare the input
            user_input = f"""
            Please analyze the following {language} code:
            
            ```{language}
            {code}
            ```
            
            Provide a detailed analysis including code quality, potential bugs, performance considerations, 
            and improvement suggestions.
            """
            
            # Run the agent
            response = await session.send_message(user_input)
            
            logger.info(f"Code analysis completed for {len(code)} characters of {language} code")
            
            # Format the response
            return {
                "analysis": response.text,
                "thinking": response.thinking,
                "tool_calls": response.tool_calls,
                "session_id": session.session_id
            }
            
        except Exception as e:
            logger.error(f"Error analyzing code: {str(e)}")
            return {
                "error": str(e),
                "analysis": f"Error analyzing code: {str(e)}"
            }
