import asyncio
from fastapi import FastAP<PERSON>, HTTPException
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Optional, List, AsyncGenerator, Dict, Any
import datetime
import logging
import uuid
import asyncio
import os

from chatbot_rag.core.cgrag_processor import CGRAGProcessor
from chatbot_rag.rag_utils import RAG_orchestrator
from langchain_ollama import ChatOllama
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate, HumanMessagePromptTemplate, AIMessagePromptTemplate
from langchain.callbacks.base import BaseCallbackHandler as LangChainCallbackHandler

from langfuse import Langfuse
from langfuse.callback import CallbackHandler

# Import ADK endpoints
from chatbot_rag.adk.api.adk_endpoints import router as adk_router

# Configure logging once in the main file
logging.basicConfig(
    level=logging.DEBUG,                    # Set to DEBUG to capture all logs
    format="%(asctime)s - %(levelname)s - %(filename)s - line %(lineno)d - %(name)s - %(funcName)s%(message)s",
    handlers=[
        logging.StreamHandler(),            # Output to console
    ]
)

# Create a logger for this module
logger = logging.getLogger("api")

# Create FastAPI app
app = FastAPI(title="RAG Chatbot API")

# Add CORS middleware to allow cross-origin requests
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

# Include the ADK router
app.include_router(adk_router)

# Initialize the RAG orchestrator
rag_orchestrator = RAG_orchestrator()

# Initialize the LLM
model = ChatOllama(
    model="deepseek-r1:8b",
    base_url="http://localhost:11434/"
)

# Initialize Langfuse for tracing
langfuse_callback = CallbackHandler(
    public_key="pk-lf-aab74272-3d0d-441e-b445-f36c859e7efc",
    secret_key="sk-lf-b2030b0b-15dc-4955-8f4f-400d44d6df2e",
    host="http://localhost:3000"
)
logging.info(" - Langfuse is running.")

# Define request and response models
class QuestionRequest(BaseModel):
    question: str
    context: Optional[str] = None
    selected_subjects: Optional[List[str]] = None

class FileEdit(BaseModel):
    fileName: str
    applied: bool = False

class QuestionResponse(BaseModel):
    answer: str
    suggestedEdits: Optional[List[FileEdit]] = None

@app.post("/ask")
async def ask_question(request: QuestionRequest):
    """
    Process a question using RAG and return an answer
    """
    # Generate a unique request ID to track this request through the system
    request_id = str(uuid.uuid4())

    try:
        # Log the incoming request
        logger.info(f"=== INCOMING REQUEST [{request_id}] ===")
        logger.info(f" - Received question: {request.question}")

        # Get RAG context
        try:
            context, rag_chain = await rag_orchestrator.get_RAG(
                request.question,
                request.selected_subjects or []
            )
            logger.debug(f" - RAG context retrieved successfully")
            logger.debug(f" - Context type: {type(context)}")
            logger.debug(f" - Context preview: {str(context)[:200]}...")
        except Exception as e:
            logger.error(f" - Error retrieving RAG context: {str(e)}")
            context = ""
            rag_chain = None

        # Process the query with CGRAGProcessor for enhanced retrieval
        try:
            processor = CGRAGProcessor(embedding_model="nomic-embed-text", llm=model, retriever=rag_chain)
            enhanced_query, _ = await processor.process_query(request.question)
        except Exception as e:
            # Continue with original query if enhancement fails
            enhanced_query = request.question
            logger.info(f" - Falling back to original query: {enhanced_query}")
            logger.debug(f" - Error in query enhancement: {str(e)}")

        # Create a prompt template
        try:
            # Create a template that includes both the query and context
            system_prompt = """
                You are an assistant for question-answering tasks. If you don't know the answer, just say that you don't know. Keep the answer concise. If possible, answer it in bullet points. Make sure your answer is relevant to the question and it is answered from the context only (when the context is not empty!). The context is based on pieces of retrieved document. When the context is empty, it indicates that user didn't select any database to do the RAG, so use your knowledge to answer the question.
                Question: {enhanced_query}
                Context: {context}
                Answer:
            """
            question_template = ChatPromptTemplate.from_template(system_prompt)

            # Set up the chain
            chain = (
                question_template
                | model
                | StrOutputParser()
            )

            # Invoke the chain
            response = chain.invoke(
                {
                "enhanced_query": enhanced_query,
                "context": context
                },
                config={"callbacks":[langfuse_callback]}
            )

            # Handle response which could be a string or a list
            if isinstance(response, str):
                logger.debug(f" - Generated response (str): {response[:100]}...")
            elif isinstance(response, list):
                logger.debug(f" - Generated response (list): {str(response)[:100]}...")
            else:
                logger.debug(f" - Generated response (type {type(response)}): {str(response)[:100]}...")

        except Exception as e:
            logger.error(f" - Error generating response: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error generating response: {str(e)}")

        # Extract suggested file edits (if any)
        suggested_edits = []

        # Convert response to string if it's not already
        response_str = response
        if isinstance(response, list):
            # If it's a list, join the elements or extract the relevant part
            # This depends on the structure of your list - adjust as needed
            if len(response) > 0 and isinstance(response[0], str):
                response_str = '\n'.join(response)
            else:
                response_str = str(response)
        elif not isinstance(response, str):
            response_str = str(response)

        # Create the response object
        response_obj = QuestionResponse(
            answer=response_str,
            suggestedEdits=suggested_edits
        )

        # Log the successful response
        logger.info(f"=== SUCCESS RESPONSE [{request_id}] ===")
        logger.info(f" - Response length: {len(response_str)}")
        logger.info(f"=== END SUCCESS [{request_id}] ===")

        return response_obj

    except Exception as e:
        # Log the error
        logger.error(f"=== ERROR RESPONSE [{request_id}] ===")
        logger.error(f" - HTTP Exception: {str(e)}")
        logger.error(f" - Status code: {500}")
        logger.error(f"=== END ERROR [{request_id}] ===")

        # Return an error response
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/subjects")
async def get_subjects():
    """Get available subjects for RAG"""
    logger.info(" - Subjects requested")
    return {
        "subjects": [
            {"label": "Python Documentation", "value": "PD"},
            {"label": "Web Crawl Search", "value": "WS"}
        ]
    }

# Define a custom callback handler for streaming
# It is good to debug the streaming process
class StreamingCallbackHandler(LangChainCallbackHandler):
    def __init__(self):
        super().__init__()
        self.tokens = []
        self.token_queue = asyncio.Queue()

    async def on_llm_new_token(self, token: str, **kwargs) -> None:
        # Log the token for debugging
        logger.debug(f" - Received token: {token}")
        await self.token_queue.put(token)
        self.tokens.append(token)

    async def on_llm_end(self, response, **kwargs) -> None:
        # Log the end of the stream
        logger.debug(f" - LLM stream ended, response type: {type(response)}")
        # Signal end of stream
        await self.token_queue.put(None)

    async def get_tokens(self) -> AsyncGenerator[str, None]:
        while True:
            token = await self.token_queue.get()
            if token is None:  # End of stream
                break
            yield token

@app.get("/ask/stream")
async def ask_question_stream(question: str, selected_subjects: Optional[List[str]] = None):
    """Streaming version of the ask endpoint"""
    # Generate a unique request ID to track this request through the system
    request_id = str(uuid.uuid4())

    async def generate_stream():
        try:
            # Log the incoming request
            logger.info(f"=== INCOMING STREAMING REQUEST [{request_id}] ===")
            logger.info(f"- Received question: {question}")

            # Get RAG context and chain
            context, rag_chain = await rag_orchestrator.get_RAG(
                question,
                selected_subjects or []
            )

            # Process the query with CGRAGProcessor for enhanced retrieval
            try:
                processor = CGRAGProcessor(embedding_model="nomic-embed-text", llm=model, retriever=rag_chain)
                enhanced_query, relevant_docs = await processor.process_query(question)
            except Exception:
                # Continue with original query if enhancement fails
                enhanced_query = question
                logger.info(f" - Falling back to original query: {enhanced_query}")

            # Create a streaming callback handler
            streaming_handler = StreamingCallbackHandler()

            # Create a template that includes both the query and context
            system_prompt = """
                You are an assistant for question-answering tasks. If you don't know the answer, just say that you don't know. Keep the answer concise. If possible, answer it in bullet points. Make sure your answer is relevant to the question and it is answered from the context only (when the context is not empty!). The context is based on pieces of retrieved document. When the context is empty, it indicates that user didn't select any database to do the RAG, so use your knowledge to answer the question.
                Question: {enhanced_query}
                Context: {context}
                Answer:
            """
            question_template = ChatPromptTemplate.from_template(system_prompt)

            # Set up the chain with streaming
            chain = (
                question_template
                | model
                | StrOutputParser()
            )

            # Start the chain in a background task
            asyncio.create_task(
                chain.ainvoke(
                    {
                        "enhanced_query": enhanced_query,
                        "context": context
                    },
                    config={"callbacks": [streaming_handler, langfuse_callback]}    # Can add more callbacks here. Obs: Langfuse supports streaming
                )
            )

            # Stream tokens as they become available
            logger.debug("Starting token stream")
            yield 'data: {"type":"start"}\n\n'

            async for token in streaming_handler.get_tokens():
                # Escape special characters in the token
                escaped_token = token.replace('\\', '\\\\').replace('"', '\\"').replace('\n', '\\n').replace('\r', '\\r')
                logger.debug(f" - Streaming token: {escaped_token}")
                yield f'data: {{"type":"token","content":"{escaped_token}"}}\n\n'
                await asyncio.sleep(0.01)  # Small delay to prevent overwhelming the client

            logger.debug(" - Token stream completed")
            yield 'data: {"type":"end"}\n\n'

        except Exception as e:
            logger.error(f" - Error in streaming response: {str(e)}")
            escaped_error_str = str(e).replace('"', '\\"')
            yield f'data: {{"type":"error","content":"Error generating response: {escaped_error_str}"}}\n\n'

    return StreamingResponse(
        generate_stream(),
        media_type="text/event-stream"
    )

@app.get("/health")
async def health_check():
    """Health check endpoint to verify the API is running"""
    logger.info("Health check requested")
    return {
        "status": "ok",
        "timestamp": datetime.datetime.now().isoformat(),
        "version": "1.0.0"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)

