{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Libraries to install in the environment:\n", "# %pip install -U langchain-community faiss-cpu langchain-huggingface pymupdf tiktoken langchain-ollama python-dotenv\n", "# %pip install -qU langchain-text-splitters"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "from dotenv import load_dotenv  # for Ollama environment to track the LLM output\n", "\n", "os.environ['KMP_DUPLICATE_LIB_OK'] = 'True'\n", "\n", "load_dotenv()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Project Folder: d:\\Documents\\Python\\chatbot1\n"]}], "source": ["project_folder =  os.getcwd()\n", "print(\"Project Folder:\", project_folder)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'LANGCHAIN_PROJECT'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[3], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[43mos\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43menviron\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mLANGCHAIN_PROJECT\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m\n", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\envs\\chatbot\\lib\\os.py:680\u001b[0m, in \u001b[0;36m_Environ.__getitem__\u001b[1;34m(self, key)\u001b[0m\n\u001b[0;32m    677\u001b[0m     value \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_data[\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mencodekey(key)]\n\u001b[0;32m    678\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m:\n\u001b[0;32m    679\u001b[0m     \u001b[38;5;66;03m# raise KeyError with the original key value\u001b[39;00m\n\u001b[1;32m--> 680\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mKeyError\u001b[39;00m(key) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m\n\u001b[0;32m    681\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdecodevalue(value)\n", "\u001b[1;31mKeyError\u001b[0m: 'LANGCHAIN_PROJECT'"]}], "source": ["os.environ['LANGCHAIN_PROJECT']"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DB ['C++', 'git', 'teste', 'teste2', 'test_merge', 'UML'] []\n", "DB\\C++ [] ['C++.faiss', 'C++.pkl', '[book]C++ Template Metaprogramming Concepts, Tools, and Techniques from Boost and Beyond by <PERSON>, Aleksey Gurt<PERSON>y.pdf', '[book]Functional_Programming_in_C++.pdf']\n", "DB\\git [] ['Git e GitHub para iniciantes - Tutorial completo - Full Cycle.pdf']\n", "DB\\teste [] ['teste.faiss', 'teste.pkl']\n", "DB\\teste2 [] ['teste2.faiss', 'teste2.pkl']\n", "DB\\test_merge [] ['test_merge.faiss', 'test_merge.pkl']\n", "DB\\UML [] ['UML.faiss', 'UML.pkl', '[book]2012_UML @ Classroom An Introduction to Object-Oriented Modeling.pdf']\n", "['<PERSON>\\\\C++\\\\[book]C++ Template Metaprogramming Concepts, Tools, and Techniques from Boost and Beyond by <PERSON>, Aleksey Gurtovoy.pdf', 'DB\\\\C++\\\\[book]Functional_Programming_in_C++.pdf', 'DB\\\\git\\\\Git e GitHub para iniciantes - Tutorial completo - Full Cycle.pdf', 'DB\\\\UML\\\\[book]2012_UML @ Classroom An Introduction to Object-Oriented Modeling.pdf']\n"]}], "source": ["from langchain_community.document_loaders import PyMuPDFLoader\n", "\n", "pdfs = []\n", "excluded_dirs = {os.path.join('rag-dataset', '.git'), os.path.join('rag-dataset', 'finance')}\n", "# excluded_dirs = {'rag-dataset\\\\.git', 'rag-dataset\\\\finance'}\n", "\n", "for root, dirs, files in os.walk(\"DB\"):\n", "    print(root, dirs, files)\n", "    # if root not in excluded_dirs:\n", "    dirs[:] = [d for d in dirs if os.path.join(root, d) not in excluded_dirs]\n", "    for file in files:\n", "        if file.endswith('.pdf'):\n", "            pdfs.append(os.path.join(root, file))\n", "print(pdfs)\n", "# loader = PyMuPDFLoader(\"./rag-dataset/gym supplements\")\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["docs = []\n", "for pdf in pdfs:\n", "    loader = PyMuPDFLoader(pdf)\n", "    pages = loader.load()\n", "\n", "    docs.extend(pages)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'producer': 'GPL Ghostscript 9.26', 'creator': 'UnknownApplication', 'creationdate': '2020-05-01T18:53:08+00:00', 'source': '<PERSON>\\\\UML\\\\[book]2012_UML @ Classroom An Introduction to Object-Oriented Modeling.pdf', 'file_path': '<PERSON>\\\\UML\\\\[book]2012_UML @ Classroom An Introduction to Object-Oriented Modeling.pdf', 'total_pages': 215, 'format': 'PDF 1.4', 'title': 'Untitled', 'author': '', 'subject': '', 'keywords': '', 'moddate': '2023-08-01T19:34:24-03:00', 'trapped': '', 'page': 214}\n"]}], "source": ["print(docs[-1].metadata)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'producer': 'GPL Ghostscript 9.26', 'creator': 'UnknownApplication', 'creationdate': '2020-05-01T18:53:08+00:00', 'source': '<PERSON>\\\\UML\\\\[book]2012_UML @ Classroom An Introduction to Object-Oriented Modeling.pdf', 'file_path': '<PERSON>\\\\UML\\\\[book]2012_UML @ Classroom An Introduction to Object-Oriented Modeling.pdf', 'total_pages': 215, 'format': 'PDF 1.4', 'title': 'Untitled', 'author': '', 'subject': 'UML', 'keywords': '', 'moddate': '2023-08-01T19:34:24-03:00', 'trapped': '', 'page': 214, 'type': 'book'}\n"]}], "source": ["docs[-1].metadata[\"subject\"] = \"UML\"\n", "docs[-1].metadata[\"type\"] = \"book\"      #Will create a feature list 'type' and add a value \"book\" \n", "print(docs[-1].metadata)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def parse_pdf_metadata(file_path):\n", "    \"\"\"\n", "    Extracts type and name from formatted PDF paths like:\n", "    \"DB\\\\UML\\\\[type]filename.pdf\"\n", "    \"\"\"\n", "    # Get just the filename with extension\n", "    full_filename = os.path.basename(file_path)\n", "    \n", "    # Using string operations\n", "    if ']' in full_filename:\n", "        # Split type and name\n", "        type_end = full_filename.index(']')\n", "        doc_type = full_filename[1:type_end]  # Skip initial '['\n", "        name_with_ext = full_filename[type_end+1:]\n", "        name = os.path.splitext(name_with_ext)[0]\n", "    else:\n", "        # Fallback if no type specified\n", "        doc_type = None\n", "        name = os.path.splitext(full_filename)[0]\n", "    \n", "    # Return both versions for comparison\n", "    return {\n", "        'type_doc': doc_type,\n", "        'name_doc': name\n", "    }"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Type: book\n", "Name: 2012_UML @ Classroom An Introduction to Object-Oriented Modeling\n"]}], "source": ["file_path = r\"DB\\UML\\[book]2012_UML @ Classroom An Introduction to Object-Oriented Modeling.pdf\"\n", "result = parse_pdf_metadata(file_path)\n", "\n", "print(f\"Type: {result['type_doc']}\")  # Output: book\n", "print(f\"Name: {result['name_doc']}\")  \n", "# Output: 2012_UML @ Classroom An Introduction to Object-Oriented Modeling"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Document Chunking"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "\n", "text_splitter = RecursiveCharacterTextSplitter(\n", "    chunk_size=1000,\n", "    chunk_overlap=100\n", "    )\n", "chunks = text_splitter.split_documents(docs)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4339 981\n"]}, {"data": {"text/plain": ["(968, 294)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["import tiktoken\n", "\n", "encoding = tiktoken.encoding_for_model(\"gpt-4o-mini\")\n", "\n", "print(len(docs[0].page_content), len(chunks[0].page_content))\n", "len(encoding.encode(docs[0].page_content)), len(encoding.encode(chunks[0].page_content))\n", "## OBS: LLM context > LLM Chunks => Needs overlaping\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Document Vector Embedding"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from langchain_ollama import OllamaEmbeddings\n", "import faiss\n", "from langchain_community.vectorstores import FAISS\n", "from langchain_community.docstore.in_memory import InMemoryDocstore"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["embeddings = OllamaEmbeddings(model=\"nomic-embed-text\", base_url=\"http://localhost:11434\")\n", "\n", "single_vector = embeddings.embed_query(\"\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["768"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["len(single_vector)"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"data": {"text/plain": ["(0, 768)"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["index = faiss.IndexFlatL2(len(single_vector))\n", "index.ntotal, index.d"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["vector_store = FAISS(\n", "    embedding_function=embeddings,\n", "    index=index,\n", "    docstore=InMemoryDocstore(),\n", "    index_to_docstore_id={}\n", ")"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["<langchain_community.vectorstores.faiss.FAISS at 0x27b63c57700>"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["vector_store"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(chunks)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["help(vector_store)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ids = vector_store.add_documents(documents=chunks)\n", "len(ids)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vector_store.index_to_docstore_id"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["vector_store2.index_to_docstore_id"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["### Store Vector Database\n", "db_name = \"C++\"\n", "db_name2 = \"UML\"\n", "# vector_store.save_local(os.path.join(\"DB\", db_name),db_name)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1819\n", "768\n"]}], "source": ["### Load Vector Database\n", "vector_store = FAISS.load_local(\n", "    os.path.join(\"DB\", db_name),\n", "    index_name=db_name,\n", "    embeddings=embeddings,\n", "    allow_dangerous_deserialization=True\n", "    )\n", "print(len(vector_store.index_to_docstore_id))\n", "print(vector_store.index.d)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2344\n", "768\n", "DB\\UML\n"]}], "source": ["### Load Vector Database 2\n", "vector_store2 = FAISS.load_local(\n", "    os.path.join(\"DB\", db_name2),\n", "    index_name=db_name2,\n", "    embeddings=embeddings,\n", "    allow_dangerous_deserialization=True\n", "    )\n", "print(len(vector_store2.index_to_docstore_id))\n", "print(vector_store2.index.d)\n", "print(os.path.join(\"DB\", db_name2))\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["import uuid\n", "\n", "def remap_ids(vector_db):\n", "    \"\"\"Generate new UUIDs for all documents in a FAISS database\"\"\"\n", "    new_index_to_id = {}\n", "    new_docstore = {}\n", "    \n", "    # Generate new IDs and build new mappings\n", "    for idx, old_id in vector_db.index_to_docstore_id.items():\n", "        doc = vector_db.docstore.search(old_id)\n", "        new_id = str(uuid.uuid4())  # Generate new unique ID\n", "        new_index_to_id[idx] = new_id\n", "        new_docstore[new_id] = doc\n", "    \n", "    # Update the vector database\n", "    vector_db.docstore._dict = new_docstore\n", "    vector_db.index_to_docstore_id = new_index_to_id\n", "    return vector_db\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4163\n", "768\n"]}], "source": ["### MERGE\n", "merged_vector_db = remap_ids(vector_store2)\n", "merged_vector_db.merge_from(vector_store)\n", "\n", "merged_vector_db.save_local(os.path.join(\"DB\", \"test_merge\"),\"test_merge\")\n", "\n", "print(len(merged_vector_db.index_to_docstore_id))\n", "print(merged_vector_db.index.d)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Retrieval the relevant docs"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Primary template\n", "The declaration of a template that is not a specialization (second meaning above) is called the primary template. We can think of the \n", "primary template as covering the general case, while specializations cover various special cases.\n", "Instantiation\n", "The moment the compiler needs to know much more about a template than what its arguments are—the names of its members or the\n", " \n", "This document was created by an unregistered ChmMagic, please go to http://www.bisenter.com to register it. Thanks\n", ".\n", "{'producer': 'CHM to PDF converter 1.2', 'creator': '', 'creationdate': 'D:20060119195537', 'source': 'D:\\\\Documents\\\\Python\\\\chatbot1\\\\DB\\\\C++\\\\C++ Template Metaprogramming Concepts, Tools, and Techniques from Boost and Beyond by <PERSON>, <PERSON><PERSON><PERSON> (z-lib.org).pdf', 'file_path': 'D:\\\\Documents\\\\Python\\\\chatbot1\\\\DB\\\\C++\\\\C++ Template Metaprogramming Concepts, Tools, and Techniques from Boost and Beyond by <PERSON>, <PERSON><PERSON><PERSON> (z-lib.org).pdf', 'total_pages': 387, 'format': 'PDF 1.3', 'title': '', 'author': '', 'subject': '', 'keywords': '', 'moddate': '', 'trapped': '', 'page': 53}\n", "53\n", "\n", "\n", "\n", "{\n", "        template <\n", "          class A1, class A2 = void_, ... class Am = void_>\n", " \n", "This document was created by an unregistered ChmMagic, please go to http://www.bisenter.com to register it. Thanks\n", ".\n", "{'producer': 'CHM to PDF converter 1.2', 'creator': '', 'creationdate': 'D:20060119195537', 'source': 'D:\\\\Documents\\\\Python\\\\chatbot1\\\\DB\\\\C++\\\\C++ Template Metaprogramming Concepts, Tools, and Techniques from Boost and Beyond by <PERSON>, <PERSON><PERSON><PERSON> (z-lib.org).pdf', 'file_path': 'D:\\\\Documents\\\\Python\\\\chatbot1\\\\DB\\\\C++\\\\C++ Template Metaprogramming Concepts, Tools, and Techniques from Boost and Beyond by <PERSON>, <PERSON><PERSON><PERSON> (z-lib.org).pdf', 'total_pages': 387, 'format': 'PDF 1.3', 'title': '', 'author': '', 'subject': '', 'keywords': '', 'moddate': '', 'trapped': '', 'page': 78}\n", "78\n", "\n", "\n", "\n", "9.6. (Member) Function Pointers as Template Arguments\n", "Integral constants are not the only kind of non-type template parameters. In fact, almost any kind of value that can be determined at \n", "compile time is allowed, including:\n", "Pointers and references to specific functions\n", "Pointers and references to statically stored data\n", "Pointers to member functions\n", "And pointers to data members\n", "We can achieve dramatic efficiency gains by using these kinds of template parameters. When our earlier compose_fg class template is \n", "used on two function pointers, it is always at least as large as the pointers themselves: It needs to store the values. When a function \n", "pointer is passed as a parameter, however, no storage is needed at all.\n", "To illustrate this technique, let's build a new composing function object template:\n", "    template <class R, class F, F f, class G, G g>\n", "    struct compose_fg2\n", "    {\n", "        typedef R result_type;\n", "        template <class T>\n", "        R operator()(T const& x) const\n", "        {\n", "{'producer': 'CHM to PDF converter 1.2', 'creator': '', 'creationdate': 'D:20060119195537', 'source': 'D:\\\\Documents\\\\Python\\\\chatbot1\\\\DB\\\\C++\\\\C++ Template Metaprogramming Concepts, Tools, and Techniques from Boost and Beyond by <PERSON>, <PERSON><PERSON><PERSON> (z-lib.org).pdf', 'file_path': 'D:\\\\Documents\\\\Python\\\\chatbot1\\\\DB\\\\C++\\\\C++ Template Metaprogramming Concepts, Tools, and Techniques from Boost and Beyond by <PERSON>, <PERSON><PERSON><PERSON> (z-lib.org).pdf', 'total_pages': 387, 'format': 'PDF 1.3', 'title': '', 'author': '', 'subject': '', 'keywords': '', 'moddate': '', 'trapped': '', 'page': 231}\n", "231\n", "\n", "\n", "\n", "template <class T>\n", "   struct derived\n", "     : other <T>::template base<int>\n", "   {};\n", "Note that, unlike the typename keyword, template is required even on class template names that denote base classes.\n", "B.2.2.2 template Allowed (But Not Required)\n", "As long as it actually precedes a member template id, template is optional anywhere in a template. For instance:\n", "   template <class T>\n", "   struct other\n", "   {\n", "      template <class T> struct base;\n", "   };\n", "   template <class T>\n", "   struct derived1\n", "     : other<int>::base<T>               // OK\n", "   {};\n", "   template <class T>\n", "   struct derived2\n", "     : other <int>::template base<T>    // also OK\n", "   {};\n", "This document was created by an unregistered ChmMagic, please go to http://www.bisenter.com to register it. Thanks\n", "{'producer': 'CHM to PDF converter 1.2', 'creator': '', 'creationdate': 'D:20060119195537', 'source': 'D:\\\\Documents\\\\Python\\\\chatbot1\\\\DB\\\\C++\\\\C++ Template Metaprogramming Concepts, Tools, and Techniques from Boost and Beyond by <PERSON>, <PERSON><PERSON><PERSON> (z-lib.org).pdf', 'file_path': 'D:\\\\Documents\\\\Python\\\\chatbot1\\\\DB\\\\C++\\\\C++ Template Metaprogramming Concepts, Tools, and Techniques from Boost and Beyond by <PERSON>, <PERSON><PERSON><PERSON> (z-lib.org).pdf', 'total_pages': 387, 'format': 'PDF 1.3', 'title': '', 'author': '', 'subject': '', 'keywords': '', 'moddate': '', 'trapped': '', 'page': 357}\n", "357\n", "\n", "\n", "\n"]}], "source": ["question = \"What are Templates?\"\n", "docs = merged_vector_db.search(\n", "        query=question,\n", "        search_type='similarity'\n", "        )\n", "# Sorry, but I want to know if there is a function at the Python library 'langchain_core' that would give me the list of inputs in the variable 'prompt'.\n", "# formatted_prompt = f\"{prompt}\"  # This will not work directly, but you need to pass the context and question properly.\n", "for doc in docs:\n", "    print(doc.page_content)\n", "    print(doc.metadata)\n", "    print(doc.metadata[\"page\"])\n", "    print(\"\\n\\n\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# question = \"What is used to gain muscle mass?\"\n", "retriever = vector_store.as_retriever(\n", "    search_type = \"mmr\", \n", "    search_kwargs={'k': 3, 'fetch_k': 100, 'lambda_mult': 1}\n", ")\n", "# retriever = vector_store2.as_retriever(\n", "#     search_type = \"mmr\", \n", "#     search_kwargs={'k': 3, 'fetch_k': 100, 'lambda_mult': 1}\n", "# )"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Primary template\n", "The declaration of a template that is not a specialization (second meaning above) is called the primary template. We can think of the \n", "primary template as covering the general case, while specializations cover various special cases.\n", "Instantiation\n", "The moment the compiler needs to know much more about a template than what its arguments are—the names of its members or the\n", " \n", "This document was created by an unregistered ChmMagic, please go to http://www.bisenter.com to register it. Thanks\n", ".\n", "\n", "\n", "\n", "{\n", "        template <\n", "          class A1, class A2 = void_, ... class Am = void_>\n", " \n", "This document was created by an unregistered ChmMagic, please go to http://www.bisenter.com to register it. Thanks\n", ".\n", "\n", "\n", "\n", "9.6. (Member) Function Pointers as Template Arguments\n", "Integral constants are not the only kind of non-type template parameters. In fact, almost any kind of value that can be determined at \n", "compile time is allowed, including:\n", "Pointers and references to specific functions\n", "Pointers and references to statically stored data\n", "Pointers to member functions\n", "And pointers to data members\n", "We can achieve dramatic efficiency gains by using these kinds of template parameters. When our earlier compose_fg class template is \n", "used on two function pointers, it is always at least as large as the pointers themselves: It needs to store the values. When a function \n", "pointer is passed as a parameter, however, no storage is needed at all.\n", "To illustrate this technique, let's build a new composing function object template:\n", "    template <class R, class F, F f, class G, G g>\n", "    struct compose_fg2\n", "    {\n", "        typedef R result_type;\n", "        template <class T>\n", "        R operator()(T const& x) const\n", "        {\n", "\n", "\n", "\n"]}], "source": ["question = \"What are Templates?\"\n", "docs = retriever.invoke(question)\n", "\n", "for doc in docs:\n", "    print(doc.page_content)\n", "    print(\"\\n\\n\")"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["# question = \"What is used to gain muscle mass?\"\n", "question = \"What are the benefits of BCAA supplements?\"\n", "docs = retriever.invoke(question)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Retrieval Augmented Generation (RAG) with LLAMA 3.2 on OLLAMA"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [], "source": ["from langchain import hub\n", "from langchain_core.output_parsers import StrOutputParser\n", "from langchain_core.runnables import RunnablePassthrough\n", "from langchain_core.prompts import ChatPromptTemplate\n", "\n", "from langchain_ollama import ChatOllama"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='<think>\\n\\n</think>\\n\\nHello! How can I assist you today? 😊', additional_kwargs={}, response_metadata={'model': 'deepseek-r1:8b', 'created_at': '2025-02-26T00:54:11.313418Z', 'done': True, 'done_reason': 'stop', 'total_duration': 3220327000, 'load_duration': 2599128000, 'prompt_eval_count': 4, 'prompt_eval_duration': 157000000, 'eval_count': 16, 'eval_duration': 462000000, 'message': Message(role='assistant', content='', images=None, tool_calls=None)}, id='run-8039c5d0-7c1e-4215-a71b-18202fbd88f5-0', usage_metadata={'input_tokens': 4, 'output_tokens': 16, 'total_tokens': 20})"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["# model = ChatOllama(model=\"llama3.2:1b\", base_url=\"http://localhost:11434/\")\n", "model = ChatOllama(model=\"deepseek-r1:8b\", base_url=\"http://localhost:11434/\")\n", "model.invoke(\"hi\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Failed to get info from https://api.smith.langchain.com: LangSmithConnectionError('Connection error caused failure to GET /info in LangSmith API. Please confirm your internet connection. ConnectionError(MaxRetryError(\\'HTTPSConnectionPool(host=\\\\\\'api.smith.langchain.com\\\\\\', port=443): Max retries exceeded with url: /info (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001DE57FFBDF0>: Failed to resolve \\\\\\'api.smith.langchain.com\\\\\\' ([Errno 11001] getaddrinfo failed)\"))\\'))\\nContent-Length: None\\nAPI Key: lsv2_********************************************c6')\n"]}, {"ename": "LangSmithConnectionError", "evalue": "Connection error caused failure to GET /commits/rlm/rag-prompt/latest in LangSmith API. Please confirm your internet connection. ConnectionError(MaxRetryError('HTTPSConnectionPool(host=\\'api.smith.langchain.com\\', port=443): Max retries exceeded with url: /commits/rlm/rag-prompt/latest (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001DE58058760>: Failed to resolve \\'api.smith.langchain.com\\' ([Errno 11001] getaddrinfo failed)\"))'))\nContent-Length: None\nAPI Key: lsv2_********************************************c6", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\envs\\chatbot\\lib\\site-packages\\urllib3\\connection.py:198\u001b[0m, in \u001b[0;36mHTTPConnection._new_conn\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    197\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 198\u001b[0m     sock \u001b[38;5;241m=\u001b[39m \u001b[43mconnection\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcreate_connection\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    199\u001b[0m \u001b[43m        \u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_dns_host\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mport\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    200\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    201\u001b[0m \u001b[43m        \u001b[49m\u001b[43msource_address\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msource_address\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    202\u001b[0m \u001b[43m        \u001b[49m\u001b[43msocket_options\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msocket_options\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    203\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    204\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m socket\u001b[38;5;241m.\u001b[39mgaierror \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\envs\\chatbot\\lib\\site-packages\\urllib3\\util\\connection.py:60\u001b[0m, in \u001b[0;36mcreate_connection\u001b[1;34m(address, timeout, source_address, socket_options)\u001b[0m\n\u001b[0;32m     58\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m LocationParseError(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mhost\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m, label empty or too long\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[1;32m---> 60\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m res \u001b[38;5;129;01min\u001b[39;00m \u001b[43msocket\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgetaddrinfo\u001b[49m\u001b[43m(\u001b[49m\u001b[43mhost\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mport\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfamily\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msocket\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mSOCK_STREAM\u001b[49m\u001b[43m)\u001b[49m:\n\u001b[0;32m     61\u001b[0m     af, socktype, proto, canonname, sa \u001b[38;5;241m=\u001b[39m res\n", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\envs\\chatbot\\lib\\socket.py:967\u001b[0m, in \u001b[0;36mgetaddrinfo\u001b[1;34m(host, port, family, type, proto, flags)\u001b[0m\n\u001b[0;32m    966\u001b[0m addrlist \u001b[38;5;241m=\u001b[39m []\n\u001b[1;32m--> 967\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m res \u001b[38;5;129;01min\u001b[39;00m \u001b[43m_socket\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mgetaddrinfo\u001b[49m\u001b[43m(\u001b[49m\u001b[43mhost\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mport\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mfamily\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mtype\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mproto\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mflags\u001b[49m\u001b[43m)\u001b[49m:\n\u001b[0;32m    968\u001b[0m     af, socktype, proto, canonname, sa \u001b[38;5;241m=\u001b[39m res\n", "\u001b[1;31mgaierror\u001b[0m: [Errno 11001] getaddrinfo failed", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31mNameResolutionError\u001b[0m                       Traceback (most recent call last)", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\envs\\chatbot\\lib\\site-packages\\urllib3\\connectionpool.py:787\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[1;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, preload_content, decode_content, **response_kw)\u001b[0m\n\u001b[0;32m    786\u001b[0m \u001b[38;5;66;03m# Make the request on the HTTPConnection object\u001b[39;00m\n\u001b[1;32m--> 787\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_make_request(\n\u001b[0;32m    788\u001b[0m     conn,\n\u001b[0;32m    789\u001b[0m     method,\n\u001b[0;32m    790\u001b[0m     url,\n\u001b[0;32m    791\u001b[0m     timeout\u001b[38;5;241m=\u001b[39mtimeout_obj,\n\u001b[0;32m    792\u001b[0m     body\u001b[38;5;241m=\u001b[39mbody,\n\u001b[0;32m    793\u001b[0m     headers\u001b[38;5;241m=\u001b[39mheaders,\n\u001b[0;32m    794\u001b[0m     chunked\u001b[38;5;241m=\u001b[39mchunked,\n\u001b[0;32m    795\u001b[0m     retries\u001b[38;5;241m=\u001b[39mretries,\n\u001b[0;32m    796\u001b[0m     response_conn\u001b[38;5;241m=\u001b[39mresponse_conn,\n\u001b[0;32m    797\u001b[0m     preload_content\u001b[38;5;241m=\u001b[39mpreload_content,\n\u001b[0;32m    798\u001b[0m     decode_content\u001b[38;5;241m=\u001b[39mdecode_content,\n\u001b[0;32m    799\u001b[0m     \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mresponse_kw,\n\u001b[0;32m    800\u001b[0m )\n\u001b[0;32m    802\u001b[0m \u001b[38;5;66;03m# Everything went great!\u001b[39;00m\n", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\envs\\chatbot\\lib\\site-packages\\urllib3\\connectionpool.py:488\u001b[0m, in \u001b[0;36mHTTPConnectionPool._make_request\u001b[1;34m(self, conn, method, url, body, headers, retries, timeout, chunked, response_conn, preload_content, decode_content, enforce_content_length)\u001b[0m\n\u001b[0;32m    487\u001b[0m         new_e \u001b[38;5;241m=\u001b[39m _wrap_proxy_error(new_e, conn\u001b[38;5;241m.\u001b[39mproxy\u001b[38;5;241m.\u001b[39mscheme)\n\u001b[1;32m--> 488\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m new_e\n\u001b[0;32m    490\u001b[0m \u001b[38;5;66;03m# conn.request() calls http.client.*.request, not the method in\u001b[39;00m\n\u001b[0;32m    491\u001b[0m \u001b[38;5;66;03m# urllib3.request. It also calls makefile (recv) on the socket.\u001b[39;00m\n", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\envs\\chatbot\\lib\\site-packages\\urllib3\\connectionpool.py:464\u001b[0m, in \u001b[0;36mHTTPConnectionPool._make_request\u001b[1;34m(self, conn, method, url, body, headers, retries, timeout, chunked, response_conn, preload_content, decode_content, enforce_content_length)\u001b[0m\n\u001b[0;32m    463\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 464\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_validate_conn\u001b[49m\u001b[43m(\u001b[49m\u001b[43mconn\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    465\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (SocketTimeout, BaseSSLError) \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\envs\\chatbot\\lib\\site-packages\\urllib3\\connectionpool.py:1093\u001b[0m, in \u001b[0;36mHTTPSConnectionPool._validate_conn\u001b[1;34m(self, conn)\u001b[0m\n\u001b[0;32m   1092\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m conn\u001b[38;5;241m.\u001b[39mis_closed:\n\u001b[1;32m-> 1093\u001b[0m     \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   1095\u001b[0m \u001b[38;5;66;03m# TODO revise this, see https://github.com/urllib3/urllib3/issues/2791\u001b[39;00m\n", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\envs\\chatbot\\lib\\site-packages\\urllib3\\connection.py:704\u001b[0m, in \u001b[0;36mHTTPSConnection.connect\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    703\u001b[0m sock: socket\u001b[38;5;241m.\u001b[39msocket \u001b[38;5;241m|\u001b[39m ssl\u001b[38;5;241m.\u001b[39mSSLSocket\n\u001b[1;32m--> 704\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msock \u001b[38;5;241m=\u001b[39m sock \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_new_conn\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    705\u001b[0m server_hostname: \u001b[38;5;28mstr\u001b[39m \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhost\n", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\envs\\chatbot\\lib\\site-packages\\urllib3\\connection.py:205\u001b[0m, in \u001b[0;36mHTTPConnection._new_conn\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    204\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m socket\u001b[38;5;241m.\u001b[39mgaierror \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[1;32m--> 205\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m NameResolutionError(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mhost, \u001b[38;5;28mself\u001b[39m, e) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01me\u001b[39;00m\n\u001b[0;32m    206\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m SocketTimeout \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[1;31mNameResolutionError\u001b[0m: <urllib3.connection.HTTPSConnection object at 0x000001DE58058760>: Failed to resolve 'api.smith.langchain.com' ([Errno 11001] getaddrinfo failed)", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31mMaxRetryError\u001b[0m                             Traceback (most recent call last)", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\envs\\chatbot\\lib\\site-packages\\requests\\adapters.py:667\u001b[0m, in \u001b[0;36mHTTPAdapter.send\u001b[1;34m(self, request, stream, timeout, verify, cert, proxies)\u001b[0m\n\u001b[0;32m    666\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 667\u001b[0m     resp \u001b[38;5;241m=\u001b[39m \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43murlopen\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    668\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    669\u001b[0m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    670\u001b[0m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    671\u001b[0m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    672\u001b[0m \u001b[43m        \u001b[49m\u001b[43mredirect\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    673\u001b[0m \u001b[43m        \u001b[49m\u001b[43massert_same_host\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    674\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    675\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    676\u001b[0m \u001b[43m        \u001b[49m\u001b[43mretries\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmax_retries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    677\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    678\u001b[0m \u001b[43m        \u001b[49m\u001b[43mchunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    679\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    681\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (ProtocolError, \u001b[38;5;167;01mOSError\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\envs\\chatbot\\lib\\site-packages\\urllib3\\connectionpool.py:871\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[1;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, preload_content, decode_content, **response_kw)\u001b[0m\n\u001b[0;32m    868\u001b[0m     log\u001b[38;5;241m.\u001b[39mwarning(\n\u001b[0;32m    869\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mRetrying (\u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[38;5;124m) after connection broken by \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m, retries, err, url\n\u001b[0;32m    870\u001b[0m     )\n\u001b[1;32m--> 871\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39murlopen(\n\u001b[0;32m    872\u001b[0m         method,\n\u001b[0;32m    873\u001b[0m         url,\n\u001b[0;32m    874\u001b[0m         body,\n\u001b[0;32m    875\u001b[0m         headers,\n\u001b[0;32m    876\u001b[0m         retries,\n\u001b[0;32m    877\u001b[0m         redirect,\n\u001b[0;32m    878\u001b[0m         assert_same_host,\n\u001b[0;32m    879\u001b[0m         timeout\u001b[38;5;241m=\u001b[39mtimeout,\n\u001b[0;32m    880\u001b[0m         pool_timeout\u001b[38;5;241m=\u001b[39mpool_timeout,\n\u001b[0;32m    881\u001b[0m         release_conn\u001b[38;5;241m=\u001b[39mrelease_conn,\n\u001b[0;32m    882\u001b[0m         chunked\u001b[38;5;241m=\u001b[39mchunked,\n\u001b[0;32m    883\u001b[0m         body_pos\u001b[38;5;241m=\u001b[39mbody_pos,\n\u001b[0;32m    884\u001b[0m         preload_content\u001b[38;5;241m=\u001b[39mpreload_content,\n\u001b[0;32m    885\u001b[0m         decode_content\u001b[38;5;241m=\u001b[39mdecode_content,\n\u001b[0;32m    886\u001b[0m         \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mresponse_kw,\n\u001b[0;32m    887\u001b[0m     )\n\u001b[0;32m    889\u001b[0m \u001b[38;5;66;03m# Handle redirect?\u001b[39;00m\n", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\envs\\chatbot\\lib\\site-packages\\urllib3\\connectionpool.py:871\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[1;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, preload_content, decode_content, **response_kw)\u001b[0m\n\u001b[0;32m    868\u001b[0m     log\u001b[38;5;241m.\u001b[39mwarning(\n\u001b[0;32m    869\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mRetrying (\u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[38;5;124m) after connection broken by \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m, retries, err, url\n\u001b[0;32m    870\u001b[0m     )\n\u001b[1;32m--> 871\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39murlopen(\n\u001b[0;32m    872\u001b[0m         method,\n\u001b[0;32m    873\u001b[0m         url,\n\u001b[0;32m    874\u001b[0m         body,\n\u001b[0;32m    875\u001b[0m         headers,\n\u001b[0;32m    876\u001b[0m         retries,\n\u001b[0;32m    877\u001b[0m         redirect,\n\u001b[0;32m    878\u001b[0m         assert_same_host,\n\u001b[0;32m    879\u001b[0m         timeout\u001b[38;5;241m=\u001b[39mtimeout,\n\u001b[0;32m    880\u001b[0m         pool_timeout\u001b[38;5;241m=\u001b[39mpool_timeout,\n\u001b[0;32m    881\u001b[0m         release_conn\u001b[38;5;241m=\u001b[39mrelease_conn,\n\u001b[0;32m    882\u001b[0m         chunked\u001b[38;5;241m=\u001b[39mchunked,\n\u001b[0;32m    883\u001b[0m         body_pos\u001b[38;5;241m=\u001b[39mbody_pos,\n\u001b[0;32m    884\u001b[0m         preload_content\u001b[38;5;241m=\u001b[39mpreload_content,\n\u001b[0;32m    885\u001b[0m         decode_content\u001b[38;5;241m=\u001b[39mdecode_content,\n\u001b[0;32m    886\u001b[0m         \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mresponse_kw,\n\u001b[0;32m    887\u001b[0m     )\n\u001b[0;32m    889\u001b[0m \u001b[38;5;66;03m# Handle redirect?\u001b[39;00m\n", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\envs\\chatbot\\lib\\site-packages\\urllib3\\connectionpool.py:871\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[1;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, preload_content, decode_content, **response_kw)\u001b[0m\n\u001b[0;32m    868\u001b[0m     log\u001b[38;5;241m.\u001b[39mwarning(\n\u001b[0;32m    869\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mRetrying (\u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[38;5;124m) after connection broken by \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m, retries, err, url\n\u001b[0;32m    870\u001b[0m     )\n\u001b[1;32m--> 871\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39murlopen(\n\u001b[0;32m    872\u001b[0m         method,\n\u001b[0;32m    873\u001b[0m         url,\n\u001b[0;32m    874\u001b[0m         body,\n\u001b[0;32m    875\u001b[0m         headers,\n\u001b[0;32m    876\u001b[0m         retries,\n\u001b[0;32m    877\u001b[0m         redirect,\n\u001b[0;32m    878\u001b[0m         assert_same_host,\n\u001b[0;32m    879\u001b[0m         timeout\u001b[38;5;241m=\u001b[39mtimeout,\n\u001b[0;32m    880\u001b[0m         pool_timeout\u001b[38;5;241m=\u001b[39mpool_timeout,\n\u001b[0;32m    881\u001b[0m         release_conn\u001b[38;5;241m=\u001b[39mrelease_conn,\n\u001b[0;32m    882\u001b[0m         chunked\u001b[38;5;241m=\u001b[39mchunked,\n\u001b[0;32m    883\u001b[0m         body_pos\u001b[38;5;241m=\u001b[39mbody_pos,\n\u001b[0;32m    884\u001b[0m         preload_content\u001b[38;5;241m=\u001b[39mpreload_content,\n\u001b[0;32m    885\u001b[0m         decode_content\u001b[38;5;241m=\u001b[39mdecode_content,\n\u001b[0;32m    886\u001b[0m         \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mresponse_kw,\n\u001b[0;32m    887\u001b[0m     )\n\u001b[0;32m    889\u001b[0m \u001b[38;5;66;03m# Handle redirect?\u001b[39;00m\n", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\envs\\chatbot\\lib\\site-packages\\urllib3\\connectionpool.py:841\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[1;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, preload_content, decode_content, **response_kw)\u001b[0m\n\u001b[0;32m    839\u001b[0m     new_e \u001b[38;5;241m=\u001b[39m ProtocolError(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mConnection aborted.\u001b[39m\u001b[38;5;124m\"\u001b[39m, new_e)\n\u001b[1;32m--> 841\u001b[0m retries \u001b[38;5;241m=\u001b[39m \u001b[43mretries\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mincrement\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    842\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merror\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mnew_e\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_pool\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m_stacktrace\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msys\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mexc_info\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[43m]\u001b[49m\n\u001b[0;32m    843\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    844\u001b[0m retries\u001b[38;5;241m.\u001b[39msleep()\n", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\envs\\chatbot\\lib\\site-packages\\urllib3\\util\\retry.py:519\u001b[0m, in \u001b[0;36mRetry.increment\u001b[1;34m(self, method, url, response, error, _pool, _stacktrace)\u001b[0m\n\u001b[0;32m    518\u001b[0m     reason \u001b[38;5;241m=\u001b[39m error \u001b[38;5;129;01mor\u001b[39;00m ResponseError(cause)\n\u001b[1;32m--> 519\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m MaxRetryError(_pool, url, reason) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01m<PERSON>son\u001b[39;00m  \u001b[38;5;66;03m# type: ignore[arg-type]\u001b[39;00m\n\u001b[0;32m    521\u001b[0m log\u001b[38;5;241m.\u001b[39mdebug(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mIncremented Retry for (url=\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m): \u001b[39m\u001b[38;5;132;01m%r\u001b[39;00m\u001b[38;5;124m\"\u001b[39m, url, new_retry)\n", "\u001b[1;31mMaxRetryError\u001b[0m: HTTPSConnectionPool(host='api.smith.langchain.com', port=443): Max retries exceeded with url: /commits/rlm/rag-prompt/latest (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001DE58058760>: Failed to resolve 'api.smith.langchain.com' ([Errno 11001] getaddrinfo failed)\"))", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[1;31mConnectionError\u001b[0m                           <PERSON><PERSON> (most recent call last)", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\envs\\chatbot\\lib\\site-packages\\langsmith\\client.py:734\u001b[0m, in \u001b[0;36mClient.request_with_retries\u001b[1;34m(self, method, pathname, request_kwargs, stop_after_attempt, retry_on, to_ignore, handle_response, _context, **kwargs)\u001b[0m\n\u001b[0;32m    733\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m ls_utils\u001b[38;5;241m.\u001b[39mfilter_logs(_urllib3_logger, logging_filters):\n\u001b[1;32m--> 734\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msession\u001b[38;5;241m.\u001b[39mrequest(\n\u001b[0;32m    735\u001b[0m         method,\n\u001b[0;32m    736\u001b[0m         (\n\u001b[0;32m    737\u001b[0m             \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mapi_url \u001b[38;5;241m+\u001b[39m pathname\n\u001b[0;32m    738\u001b[0m             \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m pathname\u001b[38;5;241m.\u001b[39mstartswith(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhttp\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m    739\u001b[0m             \u001b[38;5;28;01melse\u001b[39;00m pathname\n\u001b[0;32m    740\u001b[0m         ),\n\u001b[0;32m    741\u001b[0m         stream\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[0;32m    742\u001b[0m         \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mrequest_kwargs,\n\u001b[0;32m    743\u001b[0m     )\n\u001b[0;32m    744\u001b[0m ls_utils\u001b[38;5;241m.\u001b[39mraise_for_status_with_text(response)\n", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\envs\\chatbot\\lib\\site-packages\\requests\\sessions.py:589\u001b[0m, in \u001b[0;36mSession.request\u001b[1;34m(self, method, url, params, data, headers, cookies, files, auth, timeout, allow_redirects, proxies, hooks, stream, verify, cert, json)\u001b[0m\n\u001b[0;32m    588\u001b[0m send_kwargs\u001b[38;5;241m.\u001b[39mupdate(settings)\n\u001b[1;32m--> 589\u001b[0m resp \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msend(prep, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39msend_kwargs)\n\u001b[0;32m    591\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m resp\n", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\envs\\chatbot\\lib\\site-packages\\requests\\sessions.py:703\u001b[0m, in \u001b[0;36mSession.send\u001b[1;34m(self, request, **kwargs)\u001b[0m\n\u001b[0;32m    702\u001b[0m \u001b[38;5;66;03m# Send the request\u001b[39;00m\n\u001b[1;32m--> 703\u001b[0m r \u001b[38;5;241m=\u001b[39m adapter\u001b[38;5;241m.\u001b[39msend(request, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n\u001b[0;32m    705\u001b[0m \u001b[38;5;66;03m# Total elapsed time of the request (approximately)\u001b[39;00m\n", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\envs\\chatbot\\lib\\site-packages\\requests\\adapters.py:700\u001b[0m, in \u001b[0;36mHTTPAdapter.send\u001b[1;34m(self, request, stream, timeout, verify, cert, proxies)\u001b[0m\n\u001b[0;32m    698\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m SSLError(e, request\u001b[38;5;241m=\u001b[39mrequest)\n\u001b[1;32m--> 700\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mConnectionError\u001b[39;00m(e, request\u001b[38;5;241m=\u001b[39mrequest)\n\u001b[0;32m    702\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m ClosedPoolError \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[1;31mConnectionError\u001b[0m: HTTPSConnectionPool(host='api.smith.langchain.com', port=443): Max retries exceeded with url: /commits/rlm/rag-prompt/latest (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001DE58058760>: Failed to resolve 'api.smith.langchain.com' ([Errno 11001] getaddrinfo failed)\"))", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[1;31mLangSmithConnectionError\u001b[0m                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[7], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m prompt \u001b[38;5;241m=\u001b[39m \u001b[43mhub\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpull\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mrlm/rag-prompt\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\envs\\chatbot\\lib\\site-packages\\langchain\\hub.py:114\u001b[0m, in \u001b[0;36mpull\u001b[1;34m(owner_repo_commit, include_model, api_url, api_key)\u001b[0m\n\u001b[0;32m    112\u001b[0m \u001b[38;5;66;03m# Then it's lang<PERSON>\u001b[39;00m\n\u001b[0;32m    113\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(client, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mpull_prompt\u001b[39m\u001b[38;5;124m\"\u001b[39m):\n\u001b[1;32m--> 114\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[43mclient\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpull_prompt\u001b[49m\u001b[43m(\u001b[49m\u001b[43mowner_repo_commit\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minclude_model\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43minclude_model\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m    115\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m response\n\u001b[0;32m    117\u001b[0m \u001b[38;5;66;03m# Then it's langcha<PERSON><PERSON>b\u001b[39;00m\n", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\envs\\chatbot\\lib\\site-packages\\langsmith\\client.py:6553\u001b[0m, in \u001b[0;36mClient.pull_prompt\u001b[1;34m(self, prompt_identifier, include_model)\u001b[0m\n\u001b[0;32m   6549\u001b[0m     \u001b[38;5;129m@contextlib\u001b[39m\u001b[38;5;241m.\u001b[39mcontextmanager\n\u001b[0;32m   6550\u001b[0m     \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21msuppress_langchain_beta_warning\u001b[39m():\n\u001b[0;32m   6551\u001b[0m         \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m-> 6553\u001b[0m prompt_object \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mpull_prompt_commit\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   6554\u001b[0m \u001b[43m    \u001b[49m\u001b[43mprompt_identifier\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minclude_model\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43minclude_model\u001b[49m\n\u001b[0;32m   6555\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   6556\u001b[0m \u001b[38;5;28;01mwith\u001b[39;00m suppress_langchain_beta_warning():\n\u001b[0;32m   6557\u001b[0m     prompt \u001b[38;5;241m=\u001b[39m loads(json\u001b[38;5;241m.\u001b[39mdumps(prompt_object\u001b[38;5;241m.\u001b[39mmanifest))\n", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\envs\\chatbot\\lib\\site-packages\\langsmith\\client.py:6452\u001b[0m, in \u001b[0;36mClient.pull_prompt_commit\u001b[1;34m(self, prompt_identifier, include_model)\u001b[0m\n\u001b[0;32m   6438\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"Pull a prompt object from the LangSmith API.\u001b[39;00m\n\u001b[0;32m   6439\u001b[0m \n\u001b[0;32m   6440\u001b[0m \u001b[38;5;124;03mArgs:\u001b[39;00m\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m   6447\u001b[0m \u001b[38;5;124;03m    ValueError: If no commits are found for the prompt.\u001b[39;00m\n\u001b[0;32m   6448\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[0;32m   6449\u001b[0m owner, prompt_name, commit_hash \u001b[38;5;241m=\u001b[39m ls_utils\u001b[38;5;241m.\u001b[39mparse_prompt_identifier(\n\u001b[0;32m   6450\u001b[0m     prompt_identifier\n\u001b[0;32m   6451\u001b[0m )\n\u001b[1;32m-> 6452\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest_with_retries\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   6453\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mGET\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[0;32m   6454\u001b[0m \u001b[43m    \u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m   6455\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;124;43mf\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m/commits/\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43mowner\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[38;5;124;43m/\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43mprompt_name\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[38;5;124;43m/\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[43mcommit_hash\u001b[49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[38;5;124;43m\"\u001b[39;49m\n\u001b[0;32m   6456\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;124;43mf\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;132;43;01m{\u001b[39;49;00m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43m?include_model=true\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;250;43m \u001b[39;49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[38;5;250;43m \u001b[39;49m\u001b[43minclude_model\u001b[49m\u001b[38;5;250;43m \u001b[39;49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[38;5;250;43m \u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;132;43;01m}\u001b[39;49;00m\u001b[38;5;124;43m\"\u001b[39;49m\n\u001b[0;32m   6457\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m   6458\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m   6459\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m ls_schemas\u001b[38;5;241m.\u001b[39mPromptCommit(\n\u001b[0;32m   6460\u001b[0m     \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39m{\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mowner\u001b[39m\u001b[38;5;124m\"\u001b[39m: owner, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mrepo\u001b[39m\u001b[38;5;124m\"\u001b[39m: prompt_name, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mresponse\u001b[38;5;241m.\u001b[39mjson()}\n\u001b[0;32m   6461\u001b[0m )\n", "File \u001b[1;32mc:\\ProgramData\\anaconda3\\envs\\chatbot\\lib\\site-packages\\langsmith\\client.py:828\u001b[0m, in \u001b[0;36mClient.request_with_retries\u001b[1;34m(self, method, pathname, request_kwargs, stop_after_attempt, retry_on, to_ignore, handle_response, _context, **kwargs)\u001b[0m\n\u001b[0;32m    825\u001b[0m     filler \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m*\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m*\u001b[39m (\u001b[38;5;28mmax\u001b[39m(\u001b[38;5;241m0\u001b[39m, \u001b[38;5;28mlen\u001b[39m(api_key) \u001b[38;5;241m-\u001b[39m \u001b[38;5;241m7\u001b[39m))\n\u001b[0;32m    826\u001b[0m     masked_api_key \u001b[38;5;241m=\u001b[39m \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mprefix\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;132;01m{\u001b[39;00mfiller\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;132;01m{\u001b[39;00msuffix\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m--> 828\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m ls_utils\u001b[38;5;241m.\u001b[***************************(\n\u001b[0;32m    829\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mConnection error caused failure to \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmethod\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpathname\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    830\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m in LangSmith API. \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mrecommendation\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    831\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mrepr\u001b[39m(e)\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    832\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124mContent-Length: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mcontent_length\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    833\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124mAPI Key: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mmasked_api_key\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    834\u001b[0m         \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m_context\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m    835\u001b[0m     ) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;21;01me\u001b[39;00m\n\u001b[0;32m    836\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m e:\n\u001b[0;32m    837\u001b[0m     args \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(e\u001b[38;5;241m.\u001b[39margs)\n", "\u001b[1;31mLangSmithConnectionError\u001b[0m: Connection error caused failure to GET /commits/rlm/rag-prompt/latest in LangSmith API. Please confirm your internet connection. ConnectionError(MaxRetryError('HTTPSConnectionPool(host=\\'api.smith.langchain.com\\', port=443): Max retries exceeded with url: /commits/rlm/rag-prompt/latest (Caused by NameResolutionError(\"<urllib3.connection.HTTPSConnection object at 0x000001DE58058760>: Failed to resolve \\'api.smith.langchain.com\\' ([Errno 11001] getaddrinfo failed)\"))'))\nContent-Length: None\nAPI Key: lsv2_********************************************c6"]}], "source": ["prompt = hub.pull(\"rlm/rag-prompt\")"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [], "source": ["prompt = \"\"\" \n", "    You are an assistant for question-answering tasks. Use the following pieces of retrieved context to answer the question. If you don't know the answer, just say that you don't know. Keep the answer concise. If possible, answer it in bullet points. Make sure your answer is relevant to the question and it is answered from the context only.\n", "    Question: {question} \n", "    Context: {context} \n", "    Answer:\n", "\"\"\"\n", "prompt = ChatPromptTemplate.from_template(prompt)"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [{"data": {"text/plain": ["ChatPromptTemplate(input_variables=['context', 'question'], input_types={}, partial_variables={}, messages=[HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['context', 'question'], input_types={}, partial_variables={}, template=\" \\n    You are an assistant for question-answering tasks. Use the following pieces of retrieved context to answer the question. If you don't know the answer, just say that you don't know. Keep the answer concise. If possible, answer it in bullet points. Make sure your answer is relevant to the question and it is answered from the context only.\\n    Question: {question} \\n    Context: {context} \\n    Answer:\\n\"), additional_kwargs={})])"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["prompt"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Foods 2024, 13, 1424\n", "2 of 21\n", "and sports industry, evidence suggests that creatine can benefit not only athletes but also\n", "the elderly and the general population [6]. Branched-chain amino acids (BCAA) also offer\n", "a plethora of benefits for consumers. As explained by <PERSON><PERSON> et al. [7], BCAAs are stored\n", "directly in muscles and serve as the raw materials needed to build new muscle. This\n", "contributes to the overall process of strengthening muscles and alleviating post-workout\n", "soreness. Consumers often integrate these supplements into their routines with the aim of\n", "optimizing the outcomes they wish to achieve and support overall well-being [1].\n", "The sports supplement industry makes for an impressive market share of nearly\n", "$12 billion in the health/wellness portfolio and is projected to be worth $24.4 billion\n", "by 2025. In a study by <PERSON> [4], which investigated 20 published studies about the\n", "prevalence, methods, and reasons for supplement consumption, it is evident from the\n", "\n", "Attribution (CC BY) license (https://\n", "creativecommons.org/licenses/by/\n", "4.0/).\n", "Department of Nutrition and Nutritional Value of Food, National Institute of Public Health NIH—National\n", "Research Institute, Chocimska St. 24, 00-791 Warsaw, Poland; <EMAIL>\n", "Abstract: Dietary supplements are products containing nutrients sold in various medicinal forms,\n", "and their widespread use may stem from the conviction that a preparation that looks like a drug\n", "must have therapeutic properties. The aim of this scoping review is to present what is known\n", "about the effects of using selected dietary supplements in the context of chronic diseases, as well\n", "as the risks associated with their use. The literature shows that the taking of vitamin and mineral\n", "supplements by healthy people neither lowers their risk of cardiovascular diseases nor prevents\n", "the development of malignancies. Many scientiﬁc societies recognize that omega-3 fatty acids\n", "\n", "health. The most commonly used supplements were multivitamin/mineral supplements, \n", "calcium supplements, and omega-3/fish oil (3). About a quarter of the supplements were \n", "used based on advice by health care providers. Thus, most decisions to use supplements are \n", "made by the consumers themselves.\n", "Despite their popularity, the health benefits of dietary supplements are questionable. Lack of \n", "vitamins will certainly cause deficiency diseases such as scurvy, beriberi, pellagra, and \n", "rickets. However, the vitamin content of normal well-balanced diets is sufficient to avoid \n", "these diseases. Studies aimed at determining effects of supplements often give conflicting \n", "results. There currently doesn’t seem to be any scientific consensus on whether vitamins (4) \n", "or any other dietary supplements prevent disease or have health benefits in well-nourished \n", "individuals.\n", "The intake of dietary supplements is generally safe, but not totally without risk. The current\n"]}], "source": ["## RAG Chain\n", "def format_docs(docs):\n", "    return \"\\n\\n\".join([doc.page_content for doc in docs])\n", "\n", "print(format_docs(docs))"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["---\n", "retriever: tags=['FAISS', 'OllamaEmbeddings'] vectorstore=<langchain_community.vectorstores.faiss.FAISS object at 0x0000023F4B7844C0> search_type='mmr' search_kwargs={'k': 3, 'fetch_k': 100, 'lambda_mult': 1}\n", "---\n", "format_docs: <function format_docs at 0x0000023EB2392EF0>\n", "---\n", "retriever|format_docs: first=VectorStoreRetriever(tags=['FAISS', 'OllamaEmbeddings'], vectorstore=<langchain_community.vectorstores.faiss.FAISS object at 0x0000023F4B7844C0>, search_type='mmr', search_kwargs={'k': 3, 'fetch_k': 100, 'lambda_mult': 1}) middle=[] last=RunnableLambda(format_docs)\n", "---\n", "RunnablePassthrough: \n", "---\n", "prompt: input_variables=['context', 'question'] input_types={} partial_variables={} messages=[HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['context', 'question'], input_types={}, partial_variables={}, template=\" \\n    You are an assistant for question-answering tasks. Use the following pieces of retrieved context to answer the question. If you don't know the answer, just say that you don't know. Keep the answer concise. If possible, answer it in bullet points. Make sure your answer is relevant to the question and it is answered from the context only.\\n    Question: {question} \\n    Context: {context} \\n    Answer:\\n\"), additional_kwargs={})]\n", "---\n", "rag_chain: first={\n", "  context: VectorStoreRetriever(tags=['FAISS', 'OllamaEmbeddings'], vectorstore=<langchain_community.vectorstores.faiss.FAISS object at 0x0000023F4B7844C0>, search_type='mmr', search_kwargs={'k': 3, 'fetch_k': 100, 'lambda_mult': 1})\n", "           | RunnableLambda(format_docs),\n", "  question: RunnablePassthrough()\n", "} middle=[ChatPromptTemplate(input_variables=['context', 'question'], input_types={}, partial_variables={}, messages=[HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['context', 'question'], input_types={}, partial_variables={}, template=\" \\n    You are an assistant for question-answering tasks. Use the following pieces of retrieved context to answer the question. If you don't know the answer, just say that you don't know. Keep the answer concise. If possible, answer it in bullet points. Make sure your answer is relevant to the question and it is answered from the context only.\\n    Question: {question} \\n    Context: {context} \\n    Answer:\\n\"), additional_kwargs={})]), ChatOllama(model='deepseek-r1:8b', base_url='http://localhost:11434/')] last=StrOutputParser()\n"]}], "source": ["\n", "# rag_chain = (\n", "#     {\"context\": retriever|format_docs, \"question\": RunnablePassthrough()}\n", "#     | prompt\n", "#     | model\n", "#     | StrOutputParser()\n", "# )\n", "context = retriever.invoke(\"\")\n", "print(\"---\")\n", "print(f\"retriever: {retriever}\")\n", "print(\"---\")\n", "print(f\"format_docs: {format_docs}\")\n", "print(\"---\")\n", "print(f\"retriever|format_docs: {retriever|format_docs}\")\n", "print(\"---\")\n", "print(f\"RunnablePassthrough: {RunnablePassthrough()}\")\n", "print(\"---\")\n", "print(f\"prompt: {prompt}\")\n", "#ChatPromptTemplate(input_variables=['context', 'question'], input_types={}, partial_variables={}, messages=[HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['context', 'question'], input_types={}, partial_variables={}, template=\" \\n    You are an assistant for question-answering tasks. Use the following pieces of retrieved context to answer the question. If you don't know the answer, just say that you don't know. Keep the answer concise. If possible, answer it in bullet points. Make sure your answer is relevant to the question and it is answered from the context only.\\n    Question: {question} \\n    Context: {context} \\n    Answer:\\n\"), additional_kwargs={})])\n", "#ChatPromptTemplate(input_variables=['context', 'promp_text', 'question'], input_types={}, partial_variables={}, messages=[HumanMessagePromptTemplate(prompt=PromptTemplate(input_variables=['context', 'promp_text', 'question'], input_types={}, partial_variables={}, template='\\n        {promp_text}\\n        Question: {question}\\n        Context: {context}\\n        Answer:\\n    '), additional_kwargs={})])\n", "print(\"---\")\n", "\n", "rag_chain = (\n", "    {\"context\": retriever|format_docs, \"question\": RunnablePassthrough()}\n", "    | prompt\n", "    | model\n", "    | StrOutputParser()\n", ")\n", "print(f\"rag_chain: {rag_chain}\")"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Vec is none\n"]}], "source": ["vec = []\n", "if not vec:\n", "    print(\"Vec is empty\")\n", "else:\n", "    print(\"You wrong\")"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<think>\n", "Okay, so I need to figure out how Earth can gain mass based on the provided context. The context talks about supplements containing DMAA, Ephedra extracts, and other compounds related to weight loss and performance enhancement. It mentions studies showing these supplements can increase metabolism, heart rate, etc., but it's all about human health effects, not about Earth gaining mass.\n", "\n", "I don't see anything in the context that relates to Earth's mass gain. The context is focused on chemicals in supplements and their effects on people, not on geological or astronomical processes affecting Earth's mass. So, I don't think the answer lies within this text. It seems like a trick question because the context doesn't address Earth at all.\n", "</think>\n", "\n", "The provided context discusses various weight-loss supplements containing substances like DMAA and Ephedra extracts, focusing on their effects on human health. There is no information related to how Earth gains mass in the given context.\n", "\n", "Answer: Earth's mass gain isn't addressed in the provided context.\n"]}], "source": ["# question = \"What is used to gain muscle mass?\"\n", "# question = \"What are the benefits of BCAA supplements?\"\n", "# question = \"And do you know if creatine and BCAA could be ingested at the same time?\"\n", "# question = \"Are you right about it?\"\n", "question = \"How can Earth gain mass\"\n", "\n", "output = rag_chain.invoke(question)\n", "\n", "print(output)"]}], "metadata": {"kernelspec": {"display_name": "chatbot", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 2}