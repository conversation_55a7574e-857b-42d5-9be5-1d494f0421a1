import * as vscode from 'vscode';
import axios from 'axios';
import { EventSourcePolyfill } from 'event-source-polyfill';
import { SettingsManager } from './settingsManager';

/**
 * Interface for agent context
 */
export interface AgentContext {
    code?: string;
    language?: string;
    fileName?: string;
    selectedText?: string;
    fullText?: string;
    goal?: string;
    [key: string]: any;
}

/**
 * Interface for agent response
 */
export interface AgentResponse {
    answer: string;
    thinking?: string;
    tool_calls?: any[];
    suggestedEdits?: any[];
    session_id?: string;
    error?: string;
}

/**
 * Controller for interacting with the ADK agents
 */
export class AgentController {
    private _apiUrl: string;
    private _webSocket: WebSocket | null = null;

    /**
     * Create a new AgentController
     * @param context VS Code extension context
     */
    constructor(private readonly context: vscode.ExtensionContext) {
        const settings = SettingsManager.getSettings();
        this._apiUrl = settings.apiUrl;
    }

    /**
     * Run the main agent with a query
     * @param query The user's query
     * @param context Additional context for the agent
     * @returns The agent's response
     */
    public async runMainAgent(query: string, context?: AgentContext): Promise<AgentResponse> {
        return this.runAgent(query, context);
    }

    /**
     * Run a specific agent action
     * @param action The action to run (analyze, refactor, etc.)
     * @param context Context for the action
     * @returns The agent's response
     */
    public async runAgentAction(action: string, context: AgentContext): Promise<AgentResponse> {
        const query = `Please ${action} the provided code.`;
        return this.runAgent(query, context);
    }

    /**
     * Run an agent with a query and context
     * @param query The user's query
     * @param context Additional context for the agent
     * @param agentName Optional specific agent to use
     * @returns The agent's response
     */
    private async runAgent(query: string, context?: AgentContext, agentName?: string): Promise<AgentResponse> {
        try {
            const response = await axios.post(`${this._apiUrl}/adk/agent/run`, {
                query,
                context,
                agent_name: agentName
            });

            return response.data;
        } catch (error) {
            console.error('Error running agent:', error);
            return {
                answer: `Error: ${error instanceof Error ? error.message : String(error)}`,
                error: String(error)
            };
        }
    }

    /**
     * Connect to the agent WebSocket for real-time communication
     * @param onMessage Callback for handling messages
     */
    public connectWebSocket(onMessage: (data: any) => void): void {
        if (this._webSocket) {
            this._webSocket.close();
        }

        const wsUrl = `ws://${this._apiUrl.replace('http://', '')}/adk/agent/ws`;
        this._webSocket = new WebSocket(wsUrl);

        this._webSocket.onopen = () => {
            console.log('Agent WebSocket connected');
        };

        this._webSocket.onmessage = (event) => {
            const data = JSON.parse(event.data);
            onMessage(data);
        };

        this._webSocket.onerror = (error) => {
            console.error('WebSocket error:', error);
        };

        this._webSocket.onclose = () => {
            console.log('WebSocket closed');
            this._webSocket = null;
        };
    }

    /**
     * Send a message via WebSocket
     * @param query The user's query
     * @param context Additional context
     * @param agentName Optional specific agent to use
     */
    public sendWebSocketMessage(query: string, context?: AgentContext, agentName?: string): void {
        if (this._webSocket && this._webSocket.readyState === WebSocket.OPEN) {
            this._webSocket.send(JSON.stringify({
                query,
                context,
                agent_name: agentName
            }));
        } else {
            throw new Error('WebSocket is not connected');
        }
    }

    /**
     * Stream an agent response
     * @param query The user's query
     * @param onToken Callback for handling tokens
     * @param onThinking Callback for handling thinking
     * @param onError Callback for handling errors
     * @param onComplete Callback for handling completion
     */
    public streamAgentResponse(
        query: string,
        context?: AgentContext,
        onToken?: (token: string) => void,
        onThinking?: (thinking: string) => void,
        onError?: (error: string) => void,
        onComplete?: () => void
    ): void {
        const url = `${this._apiUrl}/adk/agent/stream?query=${encodeURIComponent(query)}`;
        const eventSource = new EventSourcePolyfill(url);

        eventSource.onmessage = (event) => {
            const data = JSON.parse(event.data);

            switch (data.type) {
                case 'token':
                    if (onToken) onToken(data.content);
                    break;
                case 'thinking':
                    if (onThinking) onThinking(data.content);
                    break;
                case 'error':
                    if (onError) onError(data.content);
                    eventSource.close();
                    break;
                case 'end':
                    if (onComplete) onComplete();
                    eventSource.close();
                    break;
            }
        };

        eventSource.onerror = (error) => {
            console.error('EventSource error:', error);
            if (onError) onError(`Connection error: ${error}`);
            eventSource.close();
        };
    }

    /**
     * Helper method to analyze code
     * @param code The code to analyze
     * @param language The programming language
     * @returns Analysis results
     */
    public async analyzeCode(code: string, language: string): Promise<AgentResponse> {
        return this.runAgentAction('analyze', { code, language });
    }

    /**
     * Helper method to refactor code
     * @param code The code to refactor
     * @param language The programming language
     * @param goal The refactoring goal
     * @returns Refactored code
     */
    public async refactorCode(code: string, language: string, goal: string): Promise<AgentResponse> {
        return this.runAgentAction('refactor', { code, language, goal });
    }

    /**
     * Helper method to generate documentation
     * @param code The code to document
     * @param language The programming language
     * @returns Generated documentation
     */
    public async generateDocumentation(code: string, language: string): Promise<AgentResponse> {
        return this.runAgentAction('document', { code, language });
    }

    /**
     * Helper method to generate tests
     * @param code The code to test
     * @param language The programming language
     * @returns Generated tests
     */
    public async generateTests(code: string, language: string): Promise<AgentResponse> {
        return this.runAgentAction('test', { code, language });
    }

    /**
     * Use the Codebase Understanding Agent to analyze the codebase structure
     * @param query The question about the codebase
     * @param context Additional context
     * @returns Analysis of the codebase structure
     */
    public async understandCodebase(query: string, context?: AgentContext): Promise<AgentResponse> {
        try {
            const response = await axios.post(`${this._apiUrl}/adk/codebase/understand`, {
                query,
                context
            });

            return response.data;
        } catch (error) {
            console.error('Error understanding codebase:', error);
            return {
                answer: `Error: ${error instanceof Error ? error.message : String(error)}`,
                error: String(error)
            };
        }
    }
}
