import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { FileManager } from './fileManager';

/**
 * Represents an indexed code entity
 */
export interface CodeEntity {
    name: string;
    type: string;
    filePath: string;
    position: vscode.Position;
    symbolKind?: vscode.SymbolKind;
    parentName?: string;
    documentation?: string;
}

/**
 * Handles code indexing for faster searches and better suggestions
 */
export class CodeIndexer {
    private static instance: CodeIndexer;
    private fileManager: FileManager;
    private entityIndex: Map<string, CodeEntity[]>;
    private fileIndex: Map<string, CodeEntity[]>;
    private indexingPromise: Promise<void> | null = null;
    private isIndexing: boolean = false;
    private lastIndexTime: number = 0;
    private indexVersion: number = 0;
    private readonly indexCacheFile: string = 'code-index-cache.json';
    private readonly indexCachePath: string;
    private readonly indexExpirationTime: number = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
    private readonly excludePatterns: string[] = [
        '**/node_modules/**',
        '**/dist/**',
        '**/build/**',
        '**/out/**',
        '**/.git/**',
        '**/.vscode/**'
    ];

    private constructor(context: vscode.ExtensionContext) {
        this.fileManager = FileManager.getInstance();
        this.entityIndex = new Map<string, CodeEntity[]>();
        this.fileIndex = new Map<string, CodeEntity[]>();
        this.indexCachePath = path.join(context.globalStorageUri.fsPath, this.indexCacheFile);
        
        // Create the directory if it doesn't exist
        const dir = path.dirname(this.indexCachePath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        
        // Load the index from cache if it exists
        this.loadIndexFromCache();
        
        // Set up file watchers to keep the index up to date
        this.setupFileWatchers();
    }

    /**
     * Get the singleton instance of CodeIndexer
     */
    public static getInstance(context: vscode.ExtensionContext): CodeIndexer {
        if (!CodeIndexer.instance) {
            CodeIndexer.instance = new CodeIndexer(context);
        }
        return CodeIndexer.instance;
    }

    /**
     * Index the workspace
     * @param force Force reindexing even if the index is recent
     * @returns A promise that resolves when indexing is complete
     */
    public async indexWorkspace(force: boolean = false): Promise<void> {
        // Check if indexing is already in progress
        if (this.isIndexing && this.indexingPromise) {
            return this.indexingPromise;
        }
        
        // Check if the index is recent enough
        const now = Date.now();
        if (!force && this.lastIndexTime > 0 && now - this.lastIndexTime < this.indexExpirationTime) {
            return Promise.resolve();
        }
        
        // Start indexing
        this.isIndexing = true;
        this.indexVersion++;
        const currentVersion = this.indexVersion;
        
        this.indexingPromise = new Promise<void>(async (resolve) => {
            try {
                // Show progress indicator
                await vscode.window.withProgress({
                    location: vscode.ProgressLocation.Notification,
                    title: 'Indexing workspace for code validation',
                    cancellable: true
                }, async (progress, token) => {
                    // Clear the existing index
                    this.entityIndex.clear();
                    this.fileIndex.clear();
                    
                    // Get all files in the workspace
                    const workspaceRoot = this.fileManager.getWorkspaceRoot();
                    if (!workspaceRoot) {
                        return;
                    }
                    
                    // Find all files
                    const files = await vscode.workspace.findFiles(
                        '**/*.*',
                        `{${this.excludePatterns.join(',')}}`
                    );
                    
                    // Update progress
                    progress.report({ message: `Found ${files.length} files to index` });
                    
                    // Process files in batches to avoid UI freezing
                    const batchSize = 20;
                    for (let i = 0; i < files.length; i += batchSize) {
                        // Check if indexing was cancelled or a new indexing operation started
                        if (token.isCancellationRequested || currentVersion !== this.indexVersion) {
                            break;
                        }
                        
                        const batch = files.slice(i, i + batchSize);
                        await Promise.all(batch.map(file => this.indexFile(file.fsPath)));
                        
                        // Update progress
                        progress.report({
                            message: `Indexed ${Math.min(i + batchSize, files.length)} of ${files.length} files`,
                            increment: (batchSize / files.length) * 100
                        });
                    }
                    
                    // Save the index to cache
                    this.saveIndexToCache();
                    
                    // Update the last index time
                    this.lastIndexTime = Date.now();
                });
            } catch (error) {
                console.error(`Error indexing workspace: ${error}`);
            } finally {
                this.isIndexing = false;
                resolve();
            }
        });
        
        return this.indexingPromise;
    }

    /**
     * Index a single file
     * @param filePath The file path to index
     */
    private async indexFile(filePath: string): Promise<void> {
        try {
            // Skip files that are too large
            const stats = fs.statSync(filePath);
            if (stats.size > 1024 * 1024) { // Skip files larger than 1MB
                return;
            }
            
            // Get the file extension
            const ext = path.extname(filePath).toLowerCase();
            
            // Skip binary files and files we don't care about
            if (this.isBinaryFile(ext) || !this.isCodeFile(ext)) {
                return;
            }
            
            // Get document symbols
            const document = await vscode.workspace.openTextDocument(filePath);
            const symbols = await vscode.commands.executeCommand<vscode.DocumentSymbol[]>(
                'vscode.executeDocumentSymbolProvider',
                document.uri
            );
            
            if (!symbols || symbols.length === 0) {
                return;
            }
            
            // Process symbols
            const entities: CodeEntity[] = [];
            this.processSymbols(symbols, filePath, '', entities);
            
            // Add to file index
            this.fileIndex.set(filePath, entities);
            
            // Add to entity index
            for (const entity of entities) {
                const existing = this.entityIndex.get(entity.name) || [];
                existing.push(entity);
                this.entityIndex.set(entity.name, existing);
            }
        } catch (error) {
            // Skip files that can't be indexed
            console.error(`Error indexing file ${filePath}: ${error}`);
        }
    }

    /**
     * Process document symbols recursively
     * @param symbols The document symbols
     * @param filePath The file path
     * @param parentName The parent symbol name
     * @param entities The array to add entities to
     */
    private processSymbols(
        symbols: vscode.DocumentSymbol[],
        filePath: string,
        parentName: string,
        entities: CodeEntity[]
    ): void {
        for (const symbol of symbols) {
            // Create an entity for this symbol
            const entity: CodeEntity = {
                name: symbol.name,
                type: this.getEntityTypeFromSymbolKind(symbol.kind),
                filePath,
                position: symbol.range.start,
                symbolKind: symbol.kind,
                parentName: parentName || undefined,
                documentation: symbol.detail || undefined
            };
            
            // Add to entities
            entities.push(entity);
            
            // Process children recursively
            if (symbol.children && symbol.children.length > 0) {
                this.processSymbols(symbol.children, filePath, symbol.name, entities);
            }
        }
    }

    /**
     * Get entity type from symbol kind
     * @param kind The symbol kind
     * @returns The entity type
     */
    private getEntityTypeFromSymbolKind(kind: vscode.SymbolKind): string {
        switch (kind) {
            case vscode.SymbolKind.Class:
                return 'class';
            case vscode.SymbolKind.Interface:
                return 'interface';
            case vscode.SymbolKind.Enum:
                return 'enum';
            case vscode.SymbolKind.Function:
            case vscode.SymbolKind.Method:
                return 'function';
            case vscode.SymbolKind.Variable:
            case vscode.SymbolKind.Property:
                return 'variable';
            case vscode.SymbolKind.Constructor:
                return 'constructor';
            case vscode.SymbolKind.Module:
            case vscode.SymbolKind.Namespace:
                return 'module';
            case vscode.SymbolKind.TypeParameter:
            case vscode.SymbolKind.Struct:
                return 'type';
            default:
                return 'unknown';
        }
    }

    /**
     * Check if a file is a binary file based on its extension
     * @param extension The file extension
     * @returns True if the file is a binary file
     */
    private isBinaryFile(extension: string): boolean {
        const binaryExtensions = [
            '.exe', '.dll', '.obj', '.bin', '.dat', '.jpg', '.jpeg', '.png',
            '.gif', '.bmp', '.ico', '.pdf', '.zip', '.tar', '.gz', '.7z',
            '.rar', '.mp3', '.mp4', '.avi', '.mov', '.wmv', '.flv'
        ];
        return binaryExtensions.includes(extension);
    }

    /**
     * Check if a file is a code file based on its extension
     * @param extension The file extension
     * @returns True if the file is a code file
     */
    private isCodeFile(extension: string): boolean {
        const codeExtensions = [
            '.ts', '.js', '.tsx', '.jsx', '.java', '.py', '.c', '.cpp', '.h',
            '.hpp', '.cs', '.go', '.rb', '.php', '.html', '.css', '.scss',
            '.less', '.json', '.xml', '.yaml', '.yml', '.md', '.txt', '.sh',
            '.bat', '.ps1', '.sql', '.swift', '.kt', '.rs', '.dart'
        ];
        return codeExtensions.includes(extension);
    }

    /**
     * Set up file watchers to keep the index up to date
     */
    private setupFileWatchers(): void {
        // Watch for file changes
        const fileWatcher = vscode.workspace.createFileSystemWatcher('**/*.*');
        
        // When a file is created or changed, index it
        fileWatcher.onDidCreate(async (uri) => {
            await this.indexFile(uri.fsPath);
        });
        
        fileWatcher.onDidChange(async (uri) => {
            await this.indexFile(uri.fsPath);
        });
        
        // When a file is deleted, remove it from the index
        fileWatcher.onDidDelete((uri) => {
            const filePath = uri.fsPath;
            
            // Remove from file index
            if (this.fileIndex.has(filePath)) {
                const entities = this.fileIndex.get(filePath) || [];
                
                // Remove entities from entity index
                for (const entity of entities) {
                    const existing = this.entityIndex.get(entity.name) || [];
                    const filtered = existing.filter(e => e.filePath !== filePath);
                    
                    if (filtered.length > 0) {
                        this.entityIndex.set(entity.name, filtered);
                    } else {
                        this.entityIndex.delete(entity.name);
                    }
                }
                
                // Remove from file index
                this.fileIndex.delete(filePath);
            }
        });
    }

    /**
     * Save the index to cache
     */
    private saveIndexToCache(): void {
        try {
            // Convert the index to a serializable format
            const serializedIndex = {
                version: this.indexVersion,
                timestamp: Date.now(),
                entityIndex: Array.from(this.entityIndex.entries()),
                fileIndex: Array.from(this.fileIndex.entries())
            };
            
            // Write to file
            fs.writeFileSync(this.indexCachePath, JSON.stringify(serializedIndex));
        } catch (error) {
            console.error(`Error saving index to cache: ${error}`);
        }
    }

    /**
     * Load the index from cache
     */
    private loadIndexFromCache(): void {
        try {
            // Check if the cache file exists
            if (!fs.existsSync(this.indexCachePath)) {
                return;
            }
            
            // Read the cache file
            const data = fs.readFileSync(this.indexCachePath, 'utf8');
            const serializedIndex = JSON.parse(data);
            
            // Check if the cache is valid
            if (!serializedIndex.version || !serializedIndex.timestamp) {
                return;
            }
            
            // Check if the cache is expired
            const now = Date.now();
            if (now - serializedIndex.timestamp > this.indexExpirationTime) {
                return;
            }
            
            // Restore the index
            this.indexVersion = serializedIndex.version;
            this.lastIndexTime = serializedIndex.timestamp;
            
            // Restore entity index
            this.entityIndex = new Map<string, CodeEntity[]>(serializedIndex.entityIndex);
            
            // Restore file index
            this.fileIndex = new Map<string, CodeEntity[]>(serializedIndex.fileIndex);
        } catch (error) {
            console.error(`Error loading index from cache: ${error}`);
        }
    }

    /**
     * Find entities by name
     * @param name The entity name to find
     * @param type Optional entity type to filter by
     * @returns An array of matching entities
     */
    public findEntitiesByName(name: string, type?: string): CodeEntity[] {
        // Ensure the workspace is indexed
        if (this.entityIndex.size === 0) {
            this.indexWorkspace();
            return [];
        }
        
        // Get entities with exact name match
        const exactMatches = this.entityIndex.get(name) || [];
        
        // Filter by type if specified
        const filteredExact = type
            ? exactMatches.filter(entity => entity.type === type)
            : exactMatches;
        
        // If we have exact matches, return them
        if (filteredExact.length > 0) {
            return filteredExact;
        }
        
        // Otherwise, try fuzzy matching
        const fuzzyMatches: CodeEntity[] = [];
        
        for (const [entityName, entities] of this.entityIndex.entries()) {
            if (entityName.includes(name) || name.includes(entityName)) {
                const filtered = type
                    ? entities.filter(entity => entity.type === type)
                    : entities;
                
                fuzzyMatches.push(...filtered);
            }
        }
        
        return fuzzyMatches;
    }

    /**
     * Find entities in a file
     * @param filePath The file path
     * @param type Optional entity type to filter by
     * @returns An array of entities in the file
     */
    public findEntitiesInFile(filePath: string, type?: string): CodeEntity[] {
        // Ensure the workspace is indexed
        if (this.fileIndex.size === 0) {
            this.indexWorkspace();
            return [];
        }
        
        // Get entities in the file
        const entities = this.fileIndex.get(filePath) || [];
        
        // Filter by type if specified
        return type
            ? entities.filter(entity => entity.type === type)
            : entities;
    }

    /**
     * Get all indexed files
     * @returns An array of file paths
     */
    public getIndexedFiles(): string[] {
        return Array.from(this.fileIndex.keys());
    }

    /**
     * Get all indexed entities
     * @returns An array of entities
     */
    public getAllEntities(): CodeEntity[] {
        const entities: CodeEntity[] = [];
        
        for (const entityList of this.entityIndex.values()) {
            entities.push(...entityList);
        }
        
        return entities;
    }

    /**
     * Get the number of indexed files
     * @returns The number of indexed files
     */
    public getIndexedFileCount(): number {
        return this.fileIndex.size;
    }

    /**
     * Get the number of indexed entities
     * @returns The number of indexed entities
     */
    public getIndexedEntityCount(): number {
        let count = 0;
        
        for (const entityList of this.entityIndex.values()) {
            count += entityList.length;
        }
        
        return count;
    }

    /**
     * Check if the workspace is indexed
     * @returns True if the workspace is indexed
     */
    public isWorkspaceIndexed(): boolean {
        return this.fileIndex.size > 0;
    }

    /**
     * Check if indexing is in progress
     * @returns True if indexing is in progress
     */
    public isIndexingInProgress(): boolean {
        return this.isIndexing;
    }

    /**
     * Get the last index time
     * @returns The last index time as a Date object
     */
    public getLastIndexTime(): Date {
        return new Date(this.lastIndexTime);
    }
}

