import * as vscode from 'vscode';
import { v4 as uuidv4 } from 'uuid';

/**
 * Represents a message in a chat session
 */
export interface ChatMessage {
    id: string;
    content: string;
    isUser: boolean;
    timestamp: Date;
    fileEdits?: {
        fileName: string;
        applied: boolean;
        edits?: any[];
    }[];
}

/**
 * Represents a chat session with multiple messages
 */
export interface ChatSession {
    id: string;
    title: string;
    createdAt: Date;
    updatedAt: Date;
    messages: ChatMessage[];
    isActive: boolean;
    messageCount?: number; // Added for UI display
}

/**
 * Legacy chat history item format for migration
 */
interface LegacyChatHistoryItem {
    question: string;
    answer: string;
    timestamp: Date;
    fileEdits?: {
        fileName: string;
        applied: boolean;
        edits?: any[];
    }[];
    messageId?: string;
    title?: string;
}

/**
 * Manages chat history sessions and messages
 */
export class ChatHistoryManager {
    private context: vscode.ExtensionContext;
    private sessions: ChatSession[] = [];
    private activeSessionId: string | null = null;
    private readonly STORAGE_KEY = 'chatSessions';
    private readonly ACTIVE_SESSION_KEY = 'activeSession';
    private readonly LEGACY_STORAGE_KEY = 'chatHistory';

    /**
     * Creates a new ChatHistoryManager
     * @param context The extension context
     */
    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.loadSessions();
    }

    /**
     * Load sessions from storage
     */
    private loadSessions(): void {
        // Load sessions from storage
        this.sessions = this.context.globalState.get<ChatSession[]>(this.STORAGE_KEY, []);
        this.activeSessionId = this.context.globalState.get<string>(this.ACTIVE_SESSION_KEY, '');

        // If no sessions exist, check for legacy data to migrate
        if (this.sessions.length === 0) {
            this.migrateLegacyData();
        }

        // If no active session, set the most recent one as active
        if (!this.activeSessionId && this.sessions.length > 0) {
            // Sort sessions by updatedAt and set the most recent one as active
            const sortedSessions = [...this.sessions].sort(
                (a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
            );
            this.activeSessionId = sortedSessions[0].id;
            this.updateActiveSession(this.activeSessionId);
        }
    }

    /**
     * Migrate legacy chat history data to the new format
     */
    private async migrateLegacyData(): Promise<void> {
        const legacyHistory = this.context.globalState.get<LegacyChatHistoryItem[]>(this.LEGACY_STORAGE_KEY, []);

        if (legacyHistory.length === 0) {
            return;
        }

        console.log(`Migrating ${legacyHistory.length} legacy chat history items`);

        // Group legacy items by date (simple heuristic to group related Q&A pairs)
        const groupedByDate: { [key: string]: LegacyChatHistoryItem[] } = {};

        for (const item of legacyHistory) {
            // Use the date part as the key (ignoring time)
            const dateKey = new Date(item.timestamp).toDateString();

            if (!groupedByDate[dateKey]) {
                groupedByDate[dateKey] = [];
            }

            groupedByDate[dateKey].push(item);
        }

        // Create a session for each group
        for (const [dateKey, items] of Object.entries(groupedByDate)) {
            // Sort items by timestamp
            const sortedItems = items.sort(
                (a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
            );

            // Use the first item's title or question as the session title
            const firstItem = sortedItems[0];
            const sessionTitle = firstItem.title ||
                                (firstItem.question.length > 30 ?
                                    firstItem.question.substring(0, 27) + '...' :
                                    firstItem.question);

            // Create a new session
            const sessionId = uuidv4();
            const session: ChatSession = {
                id: sessionId,
                title: sessionTitle,
                createdAt: new Date(firstItem.timestamp),
                updatedAt: new Date(sortedItems[sortedItems.length - 1].timestamp),
                messages: [],
                isActive: false,
                messageCount: 0 // Will be updated after adding messages
            };

            // Add messages to the session
            for (const item of sortedItems) {
                // Add user question
                session.messages.push({
                    id: uuidv4(),
                    content: item.question,
                    isUser: true,
                    timestamp: new Date(item.timestamp)
                });

                // Add assistant answer
                session.messages.push({
                    id: item.messageId || uuidv4(),
                    content: item.answer,
                    isUser: false,
                    timestamp: new Date(item.timestamp),
                    fileEdits: item.fileEdits
                });
            }

            // Update message count
            session.messageCount = session.messages.length;

            this.sessions.push(session);
        }

        // Set the most recent session as active
        if (this.sessions.length > 0) {
            const sortedSessions = [...this.sessions].sort(
                (a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
            );
            this.activeSessionId = sortedSessions[0].id;
            sortedSessions[0].isActive = true;
        }

        // Save the migrated sessions
        await this.saveSessions();

        // Clear the legacy data (optional - you might want to keep it as backup)
        // await this.context.globalState.update(this.LEGACY_STORAGE_KEY, []);

        console.log(`Migration complete. Created ${this.sessions.length} sessions.`);
    }

    /**
     * Save sessions to storage
     */
    private async saveSessions(): Promise<void> {
        await this.context.globalState.update(this.STORAGE_KEY, this.sessions);
        await this.context.globalState.update(this.ACTIVE_SESSION_KEY, this.activeSessionId);
    }

    /**
     * Create a new chat session
     * @param title Optional title for the session
     * @returns The ID of the new session
     */
    public async createSession(title?: string): Promise<string> {
        // Deactivate current active session
        if (this.activeSessionId) {
            const activeSession = this.sessions.find(s => s.id === this.activeSessionId);
            if (activeSession) {
                activeSession.isActive = false;
            }
        }

        // Create a new session
        const sessionId = uuidv4();
        const defaultTitle = `Chat ${this.sessions.length + 1}`;

        const session: ChatSession = {
            id: sessionId,
            title: title || defaultTitle,
            createdAt: new Date(),
            updatedAt: new Date(),
            messages: [],
            isActive: true,
            messageCount: 0
        };

        this.sessions.push(session);
        this.activeSessionId = sessionId;

        await this.saveSessions();
        return sessionId;
    }

    /**
     * Get the active session
     * @returns The active session or null if none exists
     */
    public getActiveSession(): ChatSession | null {
        if (!this.activeSessionId) {
            return null;
        }

        return this.sessions.find(s => s.id === this.activeSessionId) || null;
    }

    /**
     * Set the active session
     * @param sessionId The ID of the session to activate
     * @returns True if successful, false otherwise
     */
    public async setActiveSession(sessionId: string): Promise<boolean> {
        const sessionIndex = this.sessions.findIndex(s => s.id === sessionId);
        if (sessionIndex === -1) {
            return false;
        }

        // Deactivate current active session
        if (this.activeSessionId) {
            const activeSession = this.sessions.find(s => s.id === this.activeSessionId);
            if (activeSession) {
                activeSession.isActive = false;
            }
        }

        // Activate the new session
        this.sessions[sessionIndex].isActive = true;
        this.activeSessionId = sessionId;

        await this.saveSessions();
        return true;
    }

    /**
     * Update the active session ID without changing session properties
     * @param sessionId The ID of the active session
     */
    private async updateActiveSession(sessionId: string): Promise<void> {
        this.activeSessionId = sessionId;
        await this.context.globalState.update(this.ACTIVE_SESSION_KEY, this.activeSessionId);
    }

    /**
     * Add a message to a session
     * @param content The message content
     * @param isUser Whether the message is from the user
     * @param fileEdits Optional file edits associated with the message
     * @param messageId Optional message ID (for assistant messages)
     * @param sessionId Optional session ID (defaults to active session)
     * @returns The ID of the message
     */
    public async addMessage(
        content: string,
        isUser: boolean,
        fileEdits?: { fileName: string; applied: boolean; edits?: any[] }[],
        messageId?: string,
        sessionId?: string
    ): Promise<string> {
        const targetSessionId = sessionId || this.activeSessionId;

        // If no active session and no session ID provided, create a new session
        if (!targetSessionId) {
            const newSessionId = await this.createSession();
            return this.addMessage(content, isUser, fileEdits, messageId, newSessionId);
        }

        const sessionIndex = this.sessions.findIndex(s => s.id === targetSessionId);
        if (sessionIndex === -1) {
            throw new Error(`Session with ID ${targetSessionId} not found`);
        }

        // Create the message
        const message: ChatMessage = {
            id: messageId || uuidv4(),
            content,
            isUser,
            timestamp: new Date(),
            fileEdits
        };

        // Add the message to the session
        this.sessions[sessionIndex].messages.push(message);

        // Update the session's updatedAt timestamp and message count
        this.sessions[sessionIndex].updatedAt = new Date();
        this.sessions[sessionIndex].messageCount = this.sessions[sessionIndex].messages.length;

        await this.saveSessions();
        return message.id;
    }

    /**
     * Get all sessions
     * @returns Array of chat sessions
     */
    public getSessions(): ChatSession[] {
        return [...this.sessions];
    }

    /**
     * Get a session by ID
     * @param sessionId The session ID
     * @returns The session or null if not found
     */
    public getSession(sessionId: string): ChatSession | null {
        return this.sessions.find(s => s.id === sessionId) || null;
    }

    /**
     * Get messages for a session
     * @param sessionId The session ID (defaults to active session)
     * @returns Array of messages or empty array if session not found
     */
    public getMessages(sessionId?: string): ChatMessage[] {
        const targetSessionId = sessionId || this.activeSessionId;
        if (!targetSessionId) {
            return [];
        }

        const session = this.sessions.find(s => s.id === targetSessionId);
        return session ? [...session.messages] : [];
    }

    /**
     * Rename a session
     * @param sessionId The session ID
     * @param newTitle The new title
     * @returns True if successful, false otherwise
     */
    public async renameSession(sessionId: string, newTitle: string): Promise<boolean> {
        const sessionIndex = this.sessions.findIndex(s => s.id === sessionId);
        if (sessionIndex === -1) {
            return false;
        }

        this.sessions[sessionIndex].title = newTitle;
        await this.saveSessions();
        return true;
    }

    /**
     * Delete a session
     * @param sessionId The session ID
     * @returns True if successful, false otherwise
     */
    public async deleteSession(sessionId: string): Promise<boolean> {
        const sessionIndex = this.sessions.findIndex(s => s.id === sessionId);
        if (sessionIndex === -1) {
            return false;
        }

        // Remove the session
        this.sessions.splice(sessionIndex, 1);

        // If the deleted session was active, set a new active session
        if (this.activeSessionId === sessionId) {
            if (this.sessions.length > 0) {
                // Set the most recent session as active
                const sortedSessions = [...this.sessions].sort(
                    (a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
                );
                this.activeSessionId = sortedSessions[0].id;
                sortedSessions[0].isActive = true;
            } else {
                this.activeSessionId = null;
            }
        }

        await this.saveSessions();
        return true;
    }

    /**
     * Clear all sessions
     * @returns True if successful
     */
    public async clearSessions(): Promise<boolean> {
        this.sessions = [];
        this.activeSessionId = null;
        await this.saveSessions();
        return true;
    }

    /**
     * Export sessions to a JSON string
     * @returns JSON string of all sessions
     */
    public exportSessions(): string {
        return JSON.stringify(this.sessions);
    }

    /**
     * Import sessions from a JSON string
     * @param json JSON string of sessions
     * @returns True if successful, false otherwise
     */
    public async importSessions(json: string): Promise<boolean> {
        try {
            const importedSessions = JSON.parse(json) as ChatSession[];

            // Validate the imported data
            if (!Array.isArray(importedSessions)) {
                return false;
            }

            // Add the imported sessions
            this.sessions = [...this.sessions, ...importedSessions];

            // If no active session, set the most recent imported one as active
            if (!this.activeSessionId && importedSessions.length > 0) {
                const sortedSessions = [...importedSessions].sort(
                    (a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
                );
                this.activeSessionId = sortedSessions[0].id;

                // Update the active status
                const activeSession = this.sessions.find(s => s.id === this.activeSessionId);
                if (activeSession) {
                    activeSession.isActive = true;
                }
            }

            await this.saveSessions();
            return true;
        } catch (error) {
            console.error('Error importing sessions:', error);
            return false;
        }
    }

    /**
     * Update a message in a session
     * @param messageId The message ID
     * @param content The new content
     * @param sessionId Optional session ID (defaults to active session)
     * @returns True if successful, false otherwise
     */
    public async updateMessage(messageId: string, content: string, sessionId?: string): Promise<boolean> {
        const targetSessionId = sessionId || this.activeSessionId;
        if (!targetSessionId) {
            return false;
        }

        const sessionIndex = this.sessions.findIndex(s => s.id === targetSessionId);
        if (sessionIndex === -1) {
            return false;
        }

        const messageIndex = this.sessions[sessionIndex].messages.findIndex(m => m.id === messageId);
        if (messageIndex === -1) {
            return false;
        }

        this.sessions[sessionIndex].messages[messageIndex].content = content;
        this.sessions[sessionIndex].updatedAt = new Date();

        await this.saveSessions();
        return true;
    }

    /**
     * Update file edit status in a message
     * @param messageId The message ID
     * @param fileName The file name
     * @param applied Whether the edit was applied
     * @param sessionId Optional session ID (defaults to active session)
     * @returns True if successful, false otherwise
     */
    public async updateFileEditStatus(
        messageId: string,
        fileName: string,
        applied: boolean,
        sessionId?: string
    ): Promise<boolean> {
        const targetSessionId = sessionId || this.activeSessionId;
        if (!targetSessionId) {
            return false;
        }

        const sessionIndex = this.sessions.findIndex(s => s.id === targetSessionId);
        if (sessionIndex === -1) {
            return false;
        }

        const messageIndex = this.sessions[sessionIndex].messages.findIndex(m => m.id === messageId);
        if (messageIndex === -1) {
            return false;
        }

        const message = this.sessions[sessionIndex].messages[messageIndex];
        if (!message.fileEdits) {
            return false;
        }

        const fileEditIndex = message.fileEdits.findIndex(e => e.fileName === fileName);
        if (fileEditIndex === -1) {
            return false;
        }

        message.fileEdits[fileEditIndex].applied = applied;

        await this.saveSessions();
        return true;
    }
}

