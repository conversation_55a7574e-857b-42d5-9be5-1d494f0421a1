/* Code block styling */
.code-block {
    margin: 10px 0;
    border-radius: 6px;
    overflow: hidden;
    border: 1px solid var(--vscode-editor-lineHighlightBorder);
    background-color: var(--vscode-editor-background);
    position: relative;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    /* Add transition for scrollbar hover effects */
    transition: all 0.3s ease;
}

.code-block-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 10px;
    background-color: var(--vscode-editorGroupHeader-tabsBackground);
    border-bottom: 1px solid var(--vscode-editor-lineHighlightBorder);
    font-family: var(--vscode-editor-font-family);
    font-size: 12px;
}

.code-block-title {
    display: flex;
    align-items: center;
    gap: 8px;
}

.code-block-collapse {
    background: none;
    border: none;
    color: var(--vscode-foreground);
    cursor: pointer;
    padding: 0;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
}

.code-block-filename {
    font-weight: bold;
    color: var(--vscode-foreground);
    cursor: pointer;
}

.code-block-path {
    color: var(--vscode-descriptionForeground);
    font-style: italic;
    margin-left: 4px;
}

.code-block-stats {
    display: flex;
    align-items: center;
    gap: 8px;
}

.code-block-added {
    color: #4CAF50;
}

.code-block-removed {
    color: #F44336;
}

.code-block-actions {
    display: flex;
    gap: 8px;
}

.code-block-action-button {
    background: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: none;
    border-radius: 3px;
    padding: 2px 6px;
    font-size: 11px;
    cursor: pointer;
}

.code-block-action-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.code-block-content {
    max-height: 300px;
    overflow: auto;
    position: relative;
    display: flex;
    flex-direction: column;
}

/* Scrollbar styling for code blocks */
/* Default state - transparent scrollbars */
.code-block-content::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

.code-block-content::-webkit-scrollbar-corner {
    background: transparent;
}

.code-block-content::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 0;
}

.code-block-content::-webkit-scrollbar-thumb {
    background: rgba(128, 128, 128, 0.2); /* Very transparent */
    border-radius: 4px;
    border: 2px solid transparent;
    min-height: 40px;
    transition: background 0.3s ease;
}

/* Hover state - visible scrollbars */
.code-block:hover .code-block-content::-webkit-scrollbar-thumb,
.code-block.scrolling .code-block-content::-webkit-scrollbar-thumb {
    background: var(--vscode-scrollbarSlider-background);
    border: 2px solid var(--vscode-editor-background);
}

.code-block-content::-webkit-scrollbar-thumb:hover {
    background: var(--vscode-scrollbarSlider-hoverBackground) !important;
}

.code-block-content::-webkit-scrollbar-thumb:active {
    background: var(--vscode-scrollbarSlider-activeBackground) !important;
}

/* Also apply scrollbar styling to pre element */
.code-block-content pre::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

.code-block-content pre::-webkit-scrollbar-corner {
    background: transparent;
}

.code-block-content pre::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 0;
}

.code-block-content pre::-webkit-scrollbar-thumb {
    background: rgba(128, 128, 128, 0.2); /* Very transparent */
    border-radius: 4px;
    border: 2px solid transparent;
    min-height: 40px;
    transition: background 0.3s ease;
}

/* Hover state for pre scrollbars */
.code-block:hover .code-block-content pre::-webkit-scrollbar-thumb,
.code-block.scrolling .code-block-content pre::-webkit-scrollbar-thumb {
    background: var(--vscode-scrollbarSlider-background);
    border: 2px solid var(--vscode-editor-background);
}

.code-block-content pre::-webkit-scrollbar-thumb:hover {
    background: var(--vscode-scrollbarSlider-hoverBackground) !important;
}

.code-block-content pre::-webkit-scrollbar-thumb:active {
    background: var(--vscode-scrollbarSlider-activeBackground) !important;
}

.code-block-content pre {
    margin: 0;
    padding: 10px;
    font-family: var(--vscode-editor-font-family);
    font-size: 13px;
    line-height: 1.5;
    tab-size: 4;
    flex: 1;
    overflow: auto;
    width: 100%;
    box-sizing: border-box;
    min-height: 100%;
}

.code-block-content code {
    font-family: inherit;
    display: block;
    min-width: 100%;
    white-space: pre;
}

.code-block-resize-handle {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 14px;
    height: 14px;
    cursor: nwse-resize;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
    opacity: 0.6;
    transition: opacity 0.2s ease;
}

.code-block-resize-handle:hover {
    opacity: 1;
}

.code-block-resize-handle::after {
    content: '';
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 0 10px 10px;
    border-color: transparent transparent var(--vscode-descriptionForeground) transparent;
}

/* Line highlighting */
.line-added {
    background-color: rgba(80, 200, 120, 0.2);
}

.line-removed {
    background-color: rgba(255, 100, 100, 0.2);
    text-decoration: line-through;
}

.line-modified {
    background-color: rgba(255, 220, 100, 0.2);
}

/* Code token styling */
.code-token {
    color: var(--vscode-textLink-foreground);
    text-decoration: underline;
    cursor: pointer;
}

.code-token:hover {
    color: var(--vscode-textLink-activeForeground);
}

/* Terminal styling */
.terminal-block {
    background-color: var(--vscode-terminal-background, #1e1e1e);
    color: var(--vscode-terminal-foreground, #cccccc);
}

/* Resizing state */
.code-block.resizing {
    user-select: none;
    pointer-events: none;
}

.code-block.resizing .code-block-content {
    transition: none;
    border: 1px dashed var(--vscode-focusBorder);
}

.terminal-block .code-block-header {
    background-color: var(--vscode-terminal-border, #333333);
}

.terminal-block pre {
    color: inherit;
}

/* Notification styling */
.code-block-notification {
    position: absolute;
    bottom: 10px;
    right: 10px;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 100;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    animation: fadeIn 0.3s ease-in-out;
}

.code-block-notification.success {
    background-color: #4CAF50;
    color: white;
}

.code-block-notification.error {
    background-color: #F44336;
    color: white;
}

.code-block-notification.info {
    background-color: #2196F3;
    color: white;
}

.code-block-notification.warning {
    background-color: #FF9800;
    color: white;
}

.code-block-notification.fade-out {
    animation: fadeOut 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(10px); }
}
