import * as vscode from 'vscode';
import { <PERSON>down<PERSON>enderer } from './markdownRenderer';
import { SettingsManager } from './settingsManager';
import { loadTemplate } from './utils/templateLoader';
import { CodeTokenHandler } from './codeTokenHandler';

interface ChatMessage {
    content: string;
    isUser: boolean;
    timestamp: number;
    id: string;
    fileEdits?: {
        fileName: string;
        applied: boolean;
    }[];
}

export class RagChatViewProvider implements vscode.WebviewViewProvider {
    private _view?: vscode.WebviewView;
    private _chatHistory: ChatMessage[] = [];
    private _messageHandler?: (message: any) => Promise<void>;

    constructor(private readonly _extensionUri: vscode.Uri) {
        // Listen for configuration changes
        vscode.workspace.onDidChangeConfiguration(e => {
            if (e.affectsConfiguration('ragAssistant') && this._view) {
                // Reload the webview when settings change
                this._view.webview.html = this._getHtmlForWebview(this._view.webview);
            }
        });
    }

    // Method to post a message to the webview
    public postMessage(message: any): void {
        if (this._view) {
            this._view.webview.postMessage(message);
        }
    }

    // Method to update settings in the webview
    public updateSettings(): void {
        if (this._view) {
            const settings = SettingsManager.getSettings();
            this.postMessage({
                type: 'updateSettings',
                settings: {
                    markdownSupport: settings.markdownSupport,
                    syntaxHighlighting: settings.syntaxHighlighting
                }
            });
        }
    }

    // Method to set the message handler
    public setMessageHandler(handler: (message: any) => Promise<void>): void {
        console.log('Setting message handler');
        this._messageHandler = handler;

        // If the view is already resolved, set up the message handler
        if (this._view) {
            console.log('View already exists, setting up message handler');
            // We don't need to set up the handler here as it will be set up in resolveWebviewView
            // This avoids duplicate handlers
        }
    }

    public resolveWebviewView(
        webviewView: vscode.WebviewView,
        _context: vscode.WebviewViewResolveContext,
        _token: vscode.CancellationToken,
    ) {
        console.log('Resolving webview view');
        this._view = webviewView;

        try {
            // Initialize webview
            webviewView.webview.options = {
                enableScripts: true,
                localResourceRoots: [this._extensionUri]
            };

            console.log('Setting webview HTML');
            webviewView.webview.html = this._getHtmlForWebview(webviewView.webview);

            // Set up message handler if one is registered
            if (this._messageHandler) {
                console.log('Setting up message handler in resolveWebviewView');
                webviewView.webview.onDidReceiveMessage(async (message) => {
                    console.log('Received message from webview:', message);
                    try {
                        await this._messageHandler!(message);
                    } catch (error) {
                        console.error('Error handling message:', error);
                        // Send error back to webview
                        this.postMessage({
                            type: 'error',
                            message: `Error handling message: ${error instanceof Error ? error.message : 'Unknown error'}`
                        });
                    }
                });
            } else {
                console.warn('No message handler registered');
            }

            // Load existing chat history
            console.log('Loading chat history, items:', this._chatHistory.length);
            this._chatHistory.forEach(msg => {
                this.addMessage(msg.content, msg.isUser);
            });

            // Update settings in the webview
            this.updateSettings();

            console.log('Webview view resolved successfully');
        } catch (error) {
            console.error('Error resolving webview view:', error);
        }
    }

    public addMessage(message: string, isUser: boolean, fileEdits?: { fileName: string; applied: boolean }[], customId?: string) {
        console.log(`Adding message: ${isUser ? 'User' : 'Assistant'} - ${message.substring(0, 50)}...`);

        if (!this._view) {
            console.warn('No view available to add message');
            return;
        }

        try {
            const timestamp = Date.now();
            const id = customId || `msg-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

            // Add to local chat history
            this._chatHistory.push({
                content: message,
                isUser,
                timestamp,
                id,
                fileEdits
            });

            console.log(`Sending message to webview: ${id}`);

            // Send message to webview
            this._view.webview.postMessage({
                type: 'addMessage',
                message,
                isUser,
                timestamp,
                id,
                fileEdits
            });

            console.log('Message sent to webview successfully');
            return id;
        } catch (error) {
            console.error('Error adding message:', error);
            return undefined;
        }
    }

    public clearChat() {
        if (!this._view) {
            return;
        }
        this._chatHistory = [];
        this._view.webview.postMessage({ type: 'clearChat' });
    }

    public clearMessages() {
        if (!this._view) {
            return;
        }
        this._chatHistory = [];
        this._view.webview.postMessage({ type: 'clearMessages' });
    }

    public updateMessage(messageId: string, content: string) {
        console.log(`Updating message: ${messageId} with content: ${content.substring(0, 50)}...`);

        if (!this._view) {
            console.warn('No view available to update message');
            return;
        }

        try {
            // Update the message in local chat history
            const messageIndex = this._chatHistory.findIndex(msg => msg.id === messageId);
            if (messageIndex !== -1) {
                this._chatHistory[messageIndex].content = content;
            }

            // Send update to webview
            this._view.webview.postMessage({
                type: 'updateMessage',
                id: messageId,
                content: content
            });

            console.log('Message update sent to webview successfully');
        } catch (error) {
            console.error('Error updating message:', error);
        }
    }

    private _getHtmlForWebview(webview: vscode.Webview) {
        // Use webview to create proper URIs for resources
        const scriptUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this._extensionUri, 'media', 'main.js')
        );
        const styleUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this._extensionUri, 'media', 'style.css')
        );
        const codeBlocksScriptUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this._extensionUri, 'media', 'codeBlocks.js')
        );
        const codeBlocksStyleUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this._extensionUri, 'media', 'codeBlocks.css')
        );
        const codeTokensScriptUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this._extensionUri, 'media', 'codeTokens.js')
        );
        const codeTokensStyleUri = webview.asWebviewUri(
            vscode.Uri.joinPath(this._extensionUri, 'media', 'codeTokens.css')
        );

        try {
            // Prepare replacements for the template
            const replacements = {
                styleUri: styleUri.toString(),
                scriptUri: scriptUri.toString(),
                codeBlocksStyleUri: codeBlocksStyleUri.toString(),
                codeBlocksScriptUri: codeBlocksScriptUri.toString(),
                codeTokensStyleUri: codeTokensStyleUri.toString(),
                codeTokensScriptUri: codeTokensScriptUri.toString(),
                markdownRendererScript: MarkdownRenderer.getRendererScript(),
                // Code token functions and styles are loaded directly from media directory
                markdownEnabled: MarkdownRenderer.isMarkdownEnabled().toString(),
                syntaxHighlightingEnabled: MarkdownRenderer.isSyntaxHighlightingEnabled().toString()
            };

            // Load and process the template
            const templatePath = vscode.Uri.joinPath(this._extensionUri, 'src', 'templates', 'chatView.html').fsPath;
            return loadTemplate(templatePath, replacements);
        } catch (error) {
            console.error('Error loading template:', error);

            // Fallback to a simple HTML if template loading fails
            return `<!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>RAG Assistant</title>
                <style>
                    body { font-family: sans-serif; padding: 20px; }
                    .error { color: red; }
                </style>
            </head>
            <body>
                <div class="error">
                    <h3>Error Loading Chat Interface</h3>
                    <p>There was a problem loading the chat interface. Please try reloading the extension.</p>
                    <p>Error details: ${error instanceof Error ? error.message : 'Unknown error'}</p>
                </div>
            </body>
            </html>`;
        }
    }
}