import * as vscode from 'vscode';
import * as diff from 'diff';
import { FileManager } from './fileManager';

/**
 * Represents a line change in a file
 */
export interface LineChange {
    type: 'added' | 'removed' | 'unchanged';
    content: string;
    lineNumber: number;
    originalLineNumber?: number;
}

/**
 * Represents a file diff with line-by-line changes
 */
export interface FileDiffInfo {
    filePath: string;
    originalContent: string;
    newContent: string;
    lineChanges: LineChange[];
    addedLines: number;
    removedLines: number;
}

/**
 * Manages file diffs and change visualization
 */
export class DiffManager {
    private static instance: DiffManager;
    private fileManager: FileManager;
    private diffHistory: Map<string, FileDiffInfo>;

    private constructor() {
        this.fileManager = FileManager.getInstance();
        this.diffHistory = new Map<string, FileDiffInfo>();
    }

    /**
     * Get the singleton instance of DiffManager
     */
    public static getInstance(): DiffManager {
        if (!DiffManager.instance) {
            DiffManager.instance = new DiffManager();
        }
        return DiffManager.instance;
    }

    /**
     * Generate a diff between original and new content
     * @param filePath The file path
     * @param originalContent The original file content
     * @param newContent The new file content
     * @returns A FileDiffInfo object with line-by-line changes
     */
    public generateDiff(filePath: string, originalContent: string, newContent: string): FileDiffInfo {
        // Generate line-by-line diff
        const changes = diff.diffLines(originalContent, newContent);
        
        // Process changes into LineChange objects
        const lineChanges: LineChange[] = [];
        let originalLineNumber = 0;
        let newLineNumber = 0;
        let addedLines = 0;
        let removedLines = 0;
        
        for (const change of changes) {
            const lines = change.value.split('\n');
            // Remove the last empty line if the change ends with a newline
            if (lines[lines.length - 1] === '') {
                lines.pop();
            }
            
            if (change.added) {
                // Added lines
                for (const line of lines) {
                    lineChanges.push({
                        type: 'added',
                        content: line,
                        lineNumber: newLineNumber++
                    });
                    addedLines++;
                }
            } else if (change.removed) {
                // Removed lines
                for (const line of lines) {
                    lineChanges.push({
                        type: 'removed',
                        content: line,
                        lineNumber: originalLineNumber++,
                        originalLineNumber: originalLineNumber - 1
                    });
                    removedLines++;
                }
            } else {
                // Unchanged lines
                for (const line of lines) {
                    lineChanges.push({
                        type: 'unchanged',
                        content: line,
                        lineNumber: newLineNumber++,
                        originalLineNumber: originalLineNumber++
                    });
                }
            }
        }
        
        // Create the FileDiffInfo object
        const diffInfo: FileDiffInfo = {
            filePath,
            originalContent,
            newContent,
            lineChanges,
            addedLines,
            removedLines
        };
        
        // Store in history
        this.diffHistory.set(filePath, diffInfo);
        
        return diffInfo;
    }
    
    /**
     * Get HTML representation of a diff
     * @param diffInfo The FileDiffInfo object
     * @returns HTML string representing the diff
     */
    public getDiffHtml(diffInfo: FileDiffInfo): string {
        let html = `<div class="diff-container">
            <div class="diff-header">
                <div class="diff-file-path">${diffInfo.filePath}</div>
                <div class="diff-stats">
                    <span class="diff-added">+${diffInfo.addedLines}</span>
                    <span class="diff-removed">-${diffInfo.removedLines}</span>
                </div>
            </div>
            <div class="diff-content">
                <table class="diff-table">
                    <tbody>`;
        
        // Add line numbers and content
        for (const change of diffInfo.lineChanges) {
            const lineClass = change.type === 'added' ? 'diff-line-added' : 
                              change.type === 'removed' ? 'diff-line-removed' : 
                              'diff-line-unchanged';
            
            const linePrefix = change.type === 'added' ? '+' : 
                               change.type === 'removed' ? '-' : 
                               ' ';
            
            const originalLineNumber = change.originalLineNumber !== undefined ? 
                                      change.originalLineNumber + 1 : 
                                      '';
            
            const newLineNumber = change.type !== 'removed' ? 
                                 change.lineNumber + 1 : 
                                 '';
            
            html += `
                <tr class="${lineClass}">
                    <td class="diff-line-number">${originalLineNumber}</td>
                    <td class="diff-line-number">${newLineNumber}</td>
                    <td class="diff-line-prefix">${linePrefix}</td>
                    <td class="diff-line-content">${this.escapeHtml(change.content)}</td>
                </tr>`;
        }
        
        html += `
                    </tbody>
                </table>
            </div>
        </div>`;
        
        return html;
    }
    
    /**
     * Show a diff in a webview panel
     * @param diffInfo The FileDiffInfo object
     * @returns A promise that resolves to true if the user wants to apply the changes, false otherwise
     */
    public async showDiffPanel(diffInfo: FileDiffInfo): Promise<boolean> {
        // Create a webview panel
        const panel = vscode.window.createWebviewPanel(
            'diffView',
            `Diff: ${diffInfo.filePath}`,
            vscode.ViewColumn.One,
            {
                enableScripts: true,
                localResourceRoots: []
            }
        );
        
        // Set the HTML content
        panel.webview.html = this.getDiffPanelHtml(diffInfo);
        
        // Handle messages from the webview
        return new Promise<boolean>((resolve) => {
            panel.webview.onDidReceiveMessage(
                message => {
                    if (message.command === 'apply') {
                        panel.dispose();
                        resolve(true);
                    } else if (message.command === 'cancel') {
                        panel.dispose();
                        resolve(false);
                    }
                }
            );
        });
    }
    
    /**
     * Get HTML for the diff panel
     * @param diffInfo The FileDiffInfo object
     * @returns HTML string for the diff panel
     */
    private getDiffPanelHtml(diffInfo: FileDiffInfo): string {
        return `<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>File Diff</title>
            <style>
                body {
                    font-family: var(--vscode-editor-font-family);
                    font-size: var(--vscode-editor-font-size);
                    color: var(--vscode-foreground);
                    background-color: var(--vscode-editor-background);
                    padding: 0;
                    margin: 0;
                }
                
                .diff-container {
                    display: flex;
                    flex-direction: column;
                    height: 100vh;
                }
                
                .diff-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 10px;
                    background-color: var(--vscode-editorGroupHeader-tabsBackground);
                    border-bottom: 1px solid var(--vscode-editor-lineHighlightBorder);
                }
                
                .diff-file-path {
                    font-weight: bold;
                }
                
                .diff-stats {
                    display: flex;
                    gap: 10px;
                }
                
                .diff-added {
                    color: #4CAF50;
                }
                
                .diff-removed {
                    color: #F44336;
                }
                
                .diff-content {
                    flex: 1;
                    overflow: auto;
                    padding: 10px;
                }
                
                .diff-table {
                    width: 100%;
                    border-collapse: collapse;
                    font-family: var(--vscode-editor-font-family);
                    font-size: var(--vscode-editor-font-size);
                }
                
                .diff-line-number {
                    width: 40px;
                    text-align: right;
                    padding: 0 5px;
                    color: var(--vscode-editorLineNumber-foreground);
                    border-right: 1px solid var(--vscode-editor-lineHighlightBorder);
                    user-select: none;
                }
                
                .diff-line-prefix {
                    width: 20px;
                    text-align: center;
                    user-select: none;
                }
                
                .diff-line-content {
                    padding-left: 10px;
                    white-space: pre;
                }
                
                .diff-line-added {
                    background-color: rgba(80, 200, 120, 0.2);
                }
                
                .diff-line-added .diff-line-prefix {
                    color: #4CAF50;
                }
                
                .diff-line-removed {
                    background-color: rgba(255, 100, 100, 0.2);
                }
                
                .diff-line-removed .diff-line-prefix {
                    color: #F44336;
                }
                
                .diff-actions {
                    display: flex;
                    justify-content: flex-end;
                    gap: 10px;
                    padding: 10px;
                    background-color: var(--vscode-editorGroupHeader-tabsBackground);
                    border-top: 1px solid var(--vscode-editor-lineHighlightBorder);
                }
                
                .diff-action-button {
                    padding: 6px 12px;
                    border: none;
                    border-radius: 3px;
                    cursor: pointer;
                }
                
                .diff-apply-button {
                    background-color: var(--vscode-button-background);
                    color: var(--vscode-button-foreground);
                }
                
                .diff-cancel-button {
                    background-color: var(--vscode-button-secondaryBackground);
                    color: var(--vscode-button-secondaryForeground);
                }
            </style>
        </head>
        <body>
            <div class="diff-container">
                <div class="diff-header">
                    <div class="diff-file-path">${diffInfo.filePath}</div>
                    <div class="diff-stats">
                        <span class="diff-added">+${diffInfo.addedLines}</span>
                        <span class="diff-removed">-${diffInfo.removedLines}</span>
                    </div>
                </div>
                <div class="diff-content">
                    <table class="diff-table">
                        <tbody>
                            ${this.getDiffTableRows(diffInfo)}
                        </tbody>
                    </table>
                </div>
                <div class="diff-actions">
                    <button class="diff-action-button diff-cancel-button" onclick="cancel()">Cancel</button>
                    <button class="diff-action-button diff-apply-button" onclick="apply()">Apply Changes</button>
                </div>
            </div>
            
            <script>
                const vscode = acquireVsCodeApi();
                
                function apply() {
                    vscode.postMessage({ command: 'apply' });
                }
                
                function cancel() {
                    vscode.postMessage({ command: 'cancel' });
                }
            </script>
        </body>
        </html>`;
    }
    
    /**
     * Get HTML table rows for the diff
     * @param diffInfo The FileDiffInfo object
     * @returns HTML string for the diff table rows
     */
    private getDiffTableRows(diffInfo: FileDiffInfo): string {
        let html = '';
        
        for (const change of diffInfo.lineChanges) {
            const lineClass = change.type === 'added' ? 'diff-line-added' : 
                              change.type === 'removed' ? 'diff-line-removed' : 
                              'diff-line-unchanged';
            
            const linePrefix = change.type === 'added' ? '+' : 
                               change.type === 'removed' ? '-' : 
                               ' ';
            
            const originalLineNumber = change.originalLineNumber !== undefined ? 
                                      change.originalLineNumber + 1 : 
                                      '';
            
            const newLineNumber = change.type !== 'removed' ? 
                                 change.lineNumber + 1 : 
                                 '';
            
            html += `
                <tr class="${lineClass}">
                    <td class="diff-line-number">${originalLineNumber}</td>
                    <td class="diff-line-number">${newLineNumber}</td>
                    <td class="diff-line-prefix">${linePrefix}</td>
                    <td class="diff-line-content">${this.escapeHtml(change.content)}</td>
                </tr>`;
        }
        
        return html;
    }
    
    /**
     * Escape HTML special characters
     * @param text The text to escape
     * @returns The escaped text
     */
    private escapeHtml(text: string): string {
        const map: { [key: string]: string } = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, m => map[m]);
    }
    
    /**
     * Get the diff history for a file
     * @param filePath The file path
     * @returns The FileDiffInfo object or undefined if not found
     */
    public getDiffHistory(filePath: string): FileDiffInfo | undefined {
        return this.diffHistory.get(filePath);
    }
    
    /**
     * Clear the diff history
     */
    public clearDiffHistory(): void {
        this.diffHistory.clear();
    }
    
    /**
     * Get line changes for a file
     * @param originalContent The original file content
     * @param newContent The new file content
     * @returns An object with added and removed line counts
     */
    public getLineChanges(originalContent: string, newContent: string): { added: number; removed: number } {
        const changes = diff.diffLines(originalContent, newContent);
        
        let added = 0;
        let removed = 0;
        
        for (const change of changes) {
            if (change.added) {
                // Count non-empty lines
                added += change.value.split('\n').filter((line: string) => line.trim().length > 0).length;
            } else if (change.removed) {
                // Count non-empty lines
                removed += change.value.split('\n').filter((line: string) => line.trim().length > 0).length;
            }
        }
        
        return { added, removed };
    }
}

