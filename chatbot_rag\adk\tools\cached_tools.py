"""
Cached tool implementations for the ADK integration.

This module provides cached versions of common tools to improve performance.
"""

import logging
from typing import Dict, Any, List, Optional, Union
import os
import json

from google.adk.tools import Tool

from chatbot_rag.adk.utils.tool_cache import cached_tool
from chatbot_rag.rag_utils import RAG_orchestrator

# Configure logging
logger = logging.getLogger("cached_tools")

@cached_tool(ttl=3600)  # Cache for 1 hour
async def search_code_cached(query: str, max_results: int = 5) -> Dict[str, Any]:
    """
    Search the codebase for relevant code snippets.
    
    Args:
        query: The search query
        max_results: Maximum number of results to return
        
    Returns:
        Dictionary with search results
    """
    try:
        # Use the RAG orchestrator to search the codebase
        orchestrator = RAG_orchestrator()
        context, sources = await orchestrator.get_RAG(query)
        
        # Format the results
        results = {
            "query": query,
            "context": context,
            "sources": sources[:max_results] if sources else [],
            "count": len(sources) if sources else 0
        }
        
        return results
    except Exception as e:
        logger.error(f"Error searching code: {str(e)}")
        return {
            "error": str(e),
            "query": query,
            "context": f"Error searching code: {str(e)}",
            "sources": [],
            "count": 0
        }

# Create a Tool object for the cached search code function
search_code_cached_tool = Tool(
    name="search_code",
    description="Search the codebase for relevant code snippets",
    function=search_code_cached,
    parameters={
        "type": "object",
        "properties": {
            "query": {
                "type": "string",
                "description": "The search query"
            },
            "max_results": {
                "type": "integer",
                "description": "Maximum number of results to return",
                "default": 5
            }
        },
        "required": ["query"]
    }
)

@cached_tool(ttl=86400)  # Cache for 24 hours
async def get_file_structure_cached(directory: Optional[str] = None, max_depth: int = 3) -> Dict[str, Any]:
    """
    Get the file structure of the workspace or a specific directory.
    
    Args:
        directory: Optional directory path. If not provided, uses the workspace root.
        max_depth: Maximum depth to traverse
        
    Returns:
        Dictionary with file structure information
    """
    try:
        # Get the workspace root
        workspace_root = os.getcwd()
        
        # Use the provided directory or the workspace root
        root_dir = os.path.join(workspace_root, directory) if directory else workspace_root
        
        # Check if the directory exists
        if not os.path.exists(root_dir) or not os.path.isdir(root_dir):
            return {
                "error": f"Directory not found: {root_dir}",
                "directory": directory or ".",
                "files": [],
                "count": 0
            }
        
        # Get the file structure
        files = []
        
        for root, dirs, filenames in os.walk(root_dir):
            # Calculate the current depth
            current_depth = root.replace(root_dir, '').count(os.sep)
            
            # Skip if we've reached the maximum depth
            if current_depth > max_depth:
                continue
            
            # Add files
            for filename in filenames:
                file_path = os.path.join(root, filename)
                rel_path = os.path.relpath(file_path, workspace_root)
                
                files.append({
                    "path": rel_path,
                    "name": filename,
                    "type": "file"
                })
            
            # Add directories
            for dirname in dirs:
                dir_path = os.path.join(root, dirname)
                rel_path = os.path.relpath(dir_path, workspace_root)
                
                files.append({
                    "path": rel_path,
                    "name": dirname,
                    "type": "directory"
                })
        
        return {
            "directory": directory or ".",
            "files": files,
            "count": len(files)
        }
    except Exception as e:
        logger.error(f"Error getting file structure: {str(e)}")
        return {
            "error": str(e),
            "directory": directory or ".",
            "files": [],
            "count": 0
        }

# Create a Tool object for the cached get file structure function
get_file_structure_cached_tool = Tool(
    name="get_file_structure",
    description="Get the file structure of the workspace or a specific directory",
    function=get_file_structure_cached,
    parameters={
        "type": "object",
        "properties": {
            "directory": {
                "type": "string",
                "description": "Optional directory path. If not provided, uses the workspace root."
            },
            "max_depth": {
                "type": "integer",
                "description": "Maximum depth to traverse",
                "default": 3
            }
        }
    }
)

@cached_tool(ttl=3600)  # Cache for 1 hour
async def read_file_cached(file_path: str) -> Dict[str, Any]:
    """
    Read a file from the workspace.
    
    Args:
        file_path: Path to the file
        
    Returns:
        Dictionary with file content
    """
    try:
        # Check if the file exists
        if not os.path.exists(file_path):
            return {
                "error": f"File not found: {file_path}",
                "file_path": file_path,
                "content": None
            }
        
        # Read the file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        return {
            "file_path": file_path,
            "content": content,
            "size": len(content)
        }
    except Exception as e:
        logger.error(f"Error reading file: {str(e)}")
        return {
            "error": str(e),
            "file_path": file_path,
            "content": None
        }

# Create a Tool object for the cached read file function
read_file_cached_tool = Tool(
    name="read_file",
    description="Read a file from the workspace",
    function=read_file_cached,
    parameters={
        "type": "object",
        "properties": {
            "file_path": {
                "type": "string",
                "description": "Path to the file"
            }
        },
        "required": ["file_path"]
    }
)
