import streamlit as st  # UI for Local Chatbot API
import asyncio

from langchain_ollama import ChatOllama
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import SystemMessagePromptTemplate, HumanMessagePromptTemplate, AIMessagePromptTemplate
from langchain_core.prompts import ChatPromptTemplate

from langfuse import Langfuse
from langfuse.callback import <PERSON>back<PERSON>andler
import uuid
from rag_utils import RAG_orchestrator

import logging          # Log DEBUG




# Configure logging once in the main file
logging.basicConfig(
    level=logging.DEBUG,                    # Set to DEBUG to capture all logs
    format="%(asctime)s - %(levelname)s - %(filename)s - line %(lineno)d - %(name)s - %(funcName)s%(message)s",
    handlers=[
        logging.FileHandler("app.log"),     # Save logs to a file
        logging.StreamHandler()             # Also print logs to the console
    ]
)

logging.info(" - Application is running.")

### LANGFUSE TO DEBUG
langfuse_callback = CallbackHandler(
    public_key="pk-lf-aab74272-3d0d-441e-b445-f36c859e7efc",
    secret_key="sk-lf-b2030b0b-15dc-4955-8f4f-400d44d6df2e",
    host="http://localhost:3000"
)
logging.info(" - Langfuse is running.")

# trace_id = str(uuid.uuid4())
# ollama_trace = langfuse.trace(id=trace_id, name="chatbot")

# LLM Model
model = ChatOllama(
    model="deepseek-r1:8b",
    base_url="http://localhost:11434/"
    )
logging.info(" - Ollama with DeepSeek r1 is running.")

def generate_response(chat_history, RAG_orch, system_prompt, selected_subjects):
    # Feed the chat history in the proper format
    logging.info(".started")
    chat_template = ChatPromptTemplate.from_messages(chat_history)

    # Capturing the last question (in chat_history) made by HumanMessage
    latest_question = chat_history[-1].prompt if isinstance(chat_history[-1], HumanMessagePromptTemplate) else ""
    # latest_question = str(latest_question)      # Ensure latest_question is a string
    
    logging.debug(f" - latest_question: {latest_question}")
    logging.debug(f" - selected_subjects: {selected_subjects}")
    
    # Get RAG context and chain
    context, rag_chain = asyncio.run(RAG_orch.get_RAG(latest_question, selected_subjects))
    
    logging.debug(f" - context: {context}")
    logging.debug(f" - rag_chain: {rag_chain}")

    # chain = (
    #             {"context": rag_chain, "question": latest_question}
    #             | system_prompt
    #             | model
    #             | StrOutputParser()
    #         )
    chain = (
                chat_template 
                | model 
                | StrOutputParser()
            )
    logging.debug(f" - chain: {chain}")
    # Capture the response from the complete chain
    # latest_question = chat_history[-1].prompt if isinstance(chat_history[-1], HumanMessagePromptTemplate) else ""
    response = chain.invoke(
        {"question": latest_question, "context": context},
        config={"callbacks":[langfuse_callback]}
    )
    
    logging.debug(".complete")
    logging.debug(f" - Response: {response}")
    
    return response

# user message in 'user' key
# AI message in 'assistant' key
def get_history(system_prompt):
    logging.info(".started")
    # Insert the system prompt in the chat history.
    chat_history = [system_prompt]

    # Insert the user questions and AI responses in the chat history (saved in streamlit session state).
    for chat in st.session_state['chat_history']:
        question = HumanMessagePromptTemplate.from_template(chat['user'])
        chat_history.append(question)

        ai_message = AIMessagePromptTemplate.from_template(template=chat['assistent'])
        chat_history.append(ai_message)

    logging.debug(".complete")
    logging.debug(f" - chat_history: {chat_history}")
    return chat_history

# Checkboxes callback
def checkbox_callback(option):
    # Toggle the checkbox state in session state
    st.session_state.checkboxes_state[option] = not st.session_state.checkboxes_state.get(option, False)
    if st.session_state.checkboxes_state[option]:
        st.session_state.selected_subjects.append(option)
        logging.debug(f" - selected the subject: {option}")
    elif option in st.session_state.selected_subjects:
        st.session_state.selected_subjects.remove(option)
        logging.debug(f" - deselected the subject: {option}")
    logging.debug(f" - Checkbox '{option}' changed! Current state: {st.session_state.checkboxes_state[option]}")

# App main
def main():
    logging.debug(".started")
    # Creating the chat history, checkboxes_state and RAG_orchestrator if it doesn't exist yet.
    if "chat_history" not in st.session_state:
        st.session_state['chat_history'] = []
    if "checkboxes_state" not in st.session_state:
        st.session_state.checkboxes_state = {}
        st.session_state.selected_subjects = []
    if "RAG_orch" not in st.session_state:
        logging.debug(" - Creating the RAG Orchestrator and the checkbox_list.")
        st.session_state.RAG_orch = RAG_orchestrator()
        st.session_state.checkbox_list = st.session_state.RAG_orch.checkbox_list
    if "system_prompt" not in st.session_state:
        logging.debug(" - Creating the prompt.")
        st.session_state.system_prompt = """
            You are an assistant for question-answering tasks. If you don't know the answer, just say that you don't know. Keep the answer concise. If possible, answer it in bullet points. Make sure your answer is relevant to the question and it is answered from the context only (when the context is not empty!). The context is based on pieces of retrieved document. When the context is empty, it indicates that user didn't select any database to do the RAG, so use your knowledge to answer the question.
            Question: {question}
            Context: {context}
            Answer:
        """
        # st.session_state.system_prompt = SystemMessagePromptTemplate.from_template(st.session_state.system_prompt)
        st.session_state.system_prompt = ChatPromptTemplate.from_template(st.session_state.system_prompt)
        logging.debug(f" - st.session_state.system_prompt: {st.session_state.system_prompt}")
        
    st.title("DeepSeek Chatbot")    #(Ollama and Langchain)
    st.write("Write your question.")   

    # LLM Prompt
    for label, subject in st.session_state.checkbox_list:
        st.checkbox(label=label, key=subject, on_change=checkbox_callback, args=(subject, ))
    logging.debug(f" - st.session_state.selected_subjects: {st.session_state.selected_subjects}")
    
    with st.form("prompt-form"):
        logging.debug(" - Creating the prompt-form.")
        # prompt_area = st.text_area("Prompt and Fine-Tuning parameters.")           
        # DeepSeek doesn't have temperature parameter
        submit = st.form_submit_button("Update database")
        
        logging.debug(" - Created the prompt-form.")
    
    # Conditional of parameter settings
    if submit:       
        if st.session_state.selected_subjects:
            st.session_state.RAG_orch.update_database(st.session_state.selected_subjects)
        logging.info(" - Cycle of update database finished.")

    # Chatbot question area and submit button
    
    with st.form("llm-form"):
        logging.debug(" - Creating the llm-form.")
        text = st.text_area("Enter your question here.")
        submit = st.form_submit_button("Submit")
    logging.debug(" - Created the llm-form.")
    
    if submit and text:
        logging.debug(" - Submited and texted. Getting the spinner.")
        with st.spinner("Generating response..."):
            logging.debug(f" - with st.spinner: started")
            
            # Fix bug of questioning a string that could modify internal variables
            text = text.replace("{", "{{")
            text = text.replace("}", "}}")

            question = HumanMessagePromptTemplate.from_template(text)
            logging.debug(f" - with st.spinner - question: {question}")
            
            # Get the chat history (from session state)
            chat_history = get_history(st.session_state.system_prompt)
            logging.debug(f" - with st.spinner - question: {question}")
            
            chat_history.append(question)

            response = generate_response(chat_history, st.session_state.RAG_orch, st.session_state.system_prompt, st.session_state.selected_subjects)
            # Fix bug of questioning a string that could modify internal variables: Langchain uses '{}' to parse prompt variables
            response = response.replace("{", "{{").replace("}", "}}")

            st.session_state['chat_history'].append({'user': text, 'assistent': response})
        logging.info(" - Cycle of question-answering finished.")

    st.write('## Chat History')
    for chat in reversed(st.session_state['chat_history']):
        st.write(f"**:adult: User**: {chat['user']}")
        st.write(f"**🤖 Assistant**: {chat['assistent'].replace('{{', '{').replace('}}', '}')}")    # Show normal code response
        st.write("---")
    logging.debug(".complete")

if __name__ == "__main__":
    logging.info(" - In the main...")
    main()