"""
API endpoints for cache management.

This module provides endpoints for cache invalidation and management.
"""

import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from chatbot_rag.adk.utils.tool_cache import get_tool_cache
from chatbot_rag.adk.utils.cache_invalidation import get_invalidation_manager

# Configure logging
logger = logging.getLogger("cache_endpoints")

# Create router
router = APIRouter(prefix="/cache", tags=["cache"])

# Define request and response models
class FileChangeRequest(BaseModel):
    file_path: str
    change_type: str

class DirectoryChangeRequest(BaseModel):
    directory_path: str

class RefactoringRequest(BaseModel):
    file_paths: List[str]

class CacheResponse(BaseModel):
    status: str
    message: str
    invalidated_tools: Optional[List[str]] = None

@router.post("/invalidate", response_model=CacheResponse)
async def invalidate_cache(request: FileChangeRequest):
    """
    Invalidate cache entries for a file change.
    """
    try:
        logger.info(f"=== CACHE INVALIDATION REQUEST ===")
        logger.info(f" - File: {request.file_path}")
        logger.info(f" - Change type: {request.change_type}")
        
        # Get the invalidation manager
        invalidation_manager = get_invalidation_manager()
        
        # Invalidate cache based on change type
        if request.change_type == "change":
            invalidation_manager.invalidate_for_file_change(request.file_path)
        elif request.change_type == "create":
            invalidation_manager.invalidate_for_file_creation(request.file_path)
        elif request.change_type == "delete":
            invalidation_manager.invalidate_for_file_deletion(request.file_path)
        else:
            logger.warning(f"Unknown change type: {request.change_type}")
            
        return {
            "status": "success",
            "message": f"Cache invalidated for {request.file_path}",
            "invalidated_tools": list(invalidation_manager.get_invalidated_tools())
        }
    except Exception as e:
        logger.error(f"Error invalidating cache: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error invalidating cache: {str(e)}")

@router.post("/invalidate-directory", response_model=CacheResponse)
async def invalidate_directory_cache(request: DirectoryChangeRequest):
    """
    Invalidate cache entries for a directory change.
    """
    try:
        logger.info(f"=== DIRECTORY CACHE INVALIDATION REQUEST ===")
        logger.info(f" - Directory: {request.directory_path}")
        
        # Get the invalidation manager
        invalidation_manager = get_invalidation_manager()
        
        # Invalidate cache for directory
        invalidation_manager.invalidate_for_directory_change(request.directory_path)
        
        return {
            "status": "success",
            "message": f"Cache invalidated for directory {request.directory_path}",
            "invalidated_tools": list(invalidation_manager.get_invalidated_tools())
        }
    except Exception as e:
        logger.error(f"Error invalidating directory cache: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error invalidating directory cache: {str(e)}")

@router.post("/invalidate-refactoring", response_model=CacheResponse)
async def invalidate_refactoring_cache(request: RefactoringRequest):
    """
    Invalidate cache entries for a refactoring operation.
    """
    try:
        logger.info(f"=== REFACTORING CACHE INVALIDATION REQUEST ===")
        logger.info(f" - Files: {request.file_paths}")
        
        # Get the invalidation manager
        invalidation_manager = get_invalidation_manager()
        
        # Invalidate cache for refactoring
        invalidation_manager.invalidate_for_refactoring(request.file_paths)
        
        return {
            "status": "success",
            "message": f"Cache invalidated for refactoring of {len(request.file_paths)} files",
            "invalidated_tools": list(invalidation_manager.get_invalidated_tools())
        }
    except Exception as e:
        logger.error(f"Error invalidating refactoring cache: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error invalidating refactoring cache: {str(e)}")

@router.post("/invalidate-all", response_model=CacheResponse)
async def invalidate_all_caches():
    """
    Invalidate all cache entries.
    """
    try:
        logger.info(f"=== INVALIDATE ALL CACHES REQUEST ===")
        
        # Get the tool cache
        tool_cache = get_tool_cache()
        
        # Invalidate all caches
        tool_cache.invalidate()
        
        return {
            "status": "success",
            "message": "All caches invalidated"
        }
    except Exception as e:
        logger.error(f"Error invalidating all caches: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error invalidating all caches: {str(e)}")

@router.get("/status", response_model=Dict[str, Any])
async def get_cache_status():
    """
    Get the current cache status.
    """
    try:
        logger.info(f"=== CACHE STATUS REQUEST ===")
        
        # Get the tool cache
        tool_cache = get_tool_cache()
        
        # Get cache statistics
        stats = tool_cache.get_statistics()
        
        return {
            "status": "success",
            "cache_enabled": tool_cache.is_enabled(),
            "cache_size": stats["size"],
            "hit_rate": stats["hit_rate"],
            "miss_rate": stats["miss_rate"],
            "tool_stats": stats["tool_stats"]
        }
    except Exception as e:
        logger.error(f"Error getting cache status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting cache status: {str(e)}")
