"""
Documentation Agent implementation using Google's Agent Development Kit.
"""

import logging
from typing import Dict, Any, Optional

from google.adk.agents import Agent
from google.adk.models import GeminiModel

from chatbot_rag.adk.tools.code_tools import generate_docs_tool
from chatbot_rag.adk.tools.rag_tools import search_code_tool

# Configure logging
logger = logging.getLogger("documentation_agent")

class DocumentationAgent:
    """
    Agent specialized in generating code documentation.
    """
    
    def __init__(self, model: Optional[GeminiModel] = None):
        """
        Initialize the Documentation Agent.
        
        Args:
            model: Optional GeminiModel instance. If not provided, a default model will be used.
        """
        self.agent = Agent(
            name="documentation_agent",
            model=model or "gemini-2.0-flash",
            description="I generate comprehensive documentation for code.",
            instruction="""
            You are a documentation expert. Your job is to generate clear, comprehensive documentation for code, including:
            1. Function and class purpose
            2. Parameter descriptions
            3. Return value descriptions
            4. Usage examples
            5. Edge cases and limitations
            
            Always follow the documentation style of the language (e.g., docstrings for Python, JSDoc for JavaScript).
            When you need more context about the codebase, use the search_code tool.
            """,
            tools=[generate_docs_tool, search_code_tool]
        )
        
        logger.info("Documentation Agent initialized")
    
    async def generate_documentation(self, code: str, language: str = "python", context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate documentation for the given code.
        
        Args:
            code: The code to document.
            language: The programming language of the code.
            context: Optional additional context.
            
        Returns:
            A dictionary containing the generated documentation.
        """
        try:
            # Create a session for this documentation generation
            session = self.agent.create_session()
            
            # Prepare the input
            user_input = f"""
            Please generate comprehensive documentation for the following {language} code:
            
            ```{language}
            {code}
            ```
            
            Follow the standard documentation style for {language}.
            """
            
            # Run the agent
            response = await session.send_message(user_input)
            
            logger.info(f"Documentation generated for {len(code)} characters of {language} code")
            
            # Format the response
            return {
                "documentation": response.text,
                "thinking": response.thinking,
                "tool_calls": response.tool_calls,
                "session_id": session.session_id
            }
            
        except Exception as e:
            logger.error(f"Error generating documentation: {str(e)}")
            return {
                "error": str(e),
                "documentation": f"Error generating documentation: {str(e)}"
            }
