import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Loads an HTML template and replaces placeholders with actual values
 * @param templatePath Absolute path to the template file
 * @param replacements Key-value pairs for placeholder replacements
 * @returns Processed HTML content
 */
export function loadTemplate(
    templatePath: string,
    replacements: Record<string, string>
): string {
    try {
        // Read the template file
        let templateContent = fs.readFileSync(templatePath, 'utf8');

        // Replace placeholders with actual values
        Object.entries(replacements).forEach(([key, value]) => {
            const placeholder = new RegExp(`\\$\\{${key}\\}`, 'g');
            templateContent = templateContent.replace(placeholder, value);
        });

        return templateContent;
    } catch (error) {
        console.error(`Error loading template from ${templatePath}:`, error);
        throw new Error(`Failed to load template: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
}
