const fs = require('fs');
const path = require('path');

// Define source and destination directories
const sourceDir = path.join(__dirname, '..', 'src', 'assets');
const destDir = path.join(__dirname, '..', 'media');

// Create the destination directory if it doesn't exist
if (!fs.existsSync(destDir)) {
    fs.mkdirSync(destDir, { recursive: true });
}

// Copy all files from source to destination
console.log(`Copying assets from ${sourceDir} to ${destDir}...`);

try {
    // Read all files in the source directory
    const files = fs.readdirSync(sourceDir);
    
    // Copy each file to the destination directory
    files.forEach(file => {
        const sourcePath = path.join(sourceDir, file);
        const destPath = path.join(destDir, file);
        
        // Only copy files, not directories
        if (fs.statSync(sourcePath).isFile()) {
            fs.copyFileSync(sourcePath, destPath);
            console.log(`Copied: ${file}`);
        }
    });
    
    console.log('Asset copying completed successfully!');
} catch (error) {
    console.error('Error copying assets:', error);
    process.exit(1);
}
