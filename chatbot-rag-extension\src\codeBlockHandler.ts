import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { FileManager } from './fileManager';
import { CodeAnalyzer } from './codeAnalyzer';

/**
 * Handler for code block functionality
 */
export class CodeBlockHandler {
    /**
     * Process code blocks in content
     * @param content The content to process
     * @returns The processed content with enhanced code blocks
     */
    public static processCodeBlocks(content: string): string {
        if (!content) {
            return '';
        }

        // Regular expression to match code blocks with language specification
        const codeBlockRegex = /```([a-zA-Z0-9_+-]*)(\r?\n[\s\S]*?\r?\n)```/g;

        // Replace code blocks with our enhanced UI
        return content.replace(codeBlockRegex, (match, language, code) => {
            // Clean up the code (remove first and last newlines)
            const cleanCode = code.replace(/^\r?\n/, '').replace(/\r?\n$/, '');

            // Determine if this is a terminal command block
            const isTerminal = language === 'bash' || language === 'sh' || language === 'shell' || language === 'cmd';

            // Create a unique ID for this code block
            const blockId = `code-block-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

            // Determine the file extension and path (for file blocks)
            let fileName = '';
            let filePath = '';

            if (!isTerminal && language) {
                // For code blocks, use the language as the file extension
                fileName = `example.${language}`;
            }

            // Create the code block HTML
            return this.createCodeBlockHTML(blockId, fileName, filePath, cleanCode, language, isTerminal);
        });
    }

    /**
     * Create HTML for a code block
     * @param blockId Unique ID for the code block
     * @param fileName File name for the code block
     * @param filePath File path for the code block
     * @param code Code content
     * @param language Programming language
     * @param isTerminal Whether this is a terminal command block
     * @returns HTML for the code block
     */
    private static createCodeBlockHTML(
        blockId: string,
        fileName: string,
        filePath: string,
        code: string,
        language: string,
        isTerminal: boolean
    ): string {
        // Determine the block class based on type
        const blockClass = isTerminal ? 'code-block terminal-block' : 'code-block';

        // Determine the header title
        const headerTitle = isTerminal ? 'Terminal' : fileName;

        // Create the HTML
        return `
            <div id="${blockId}" class="${blockClass}">
                <div class="code-block-header">
                    <div class="code-block-title">
                        <button class="code-block-collapse" onclick="window.codeBlockFunctions.toggleCodeBlock('${blockId}')">−</button>
                        <span class="code-block-filename">${headerTitle}</span>
                        ${filePath ? `<span class="code-block-path">${filePath}</span>` : ''}
                    </div>
                    <div class="code-block-stats">
                        ${!isTerminal ? `
                            <span class="code-block-added">+0</span>
                            <span class="code-block-removed">-0</span>
                        ` : ''}
                        <div class="code-block-actions">
                            ${!isTerminal ? `<button class="code-block-action-button" disabled>Apply</button>` : ''}
                            ${!isTerminal ? `<button class="code-block-action-button" disabled>Undo</button>` : ''}
                        </div>
                    </div>
                </div>
                <div class="code-block-content">
                    <pre><code class="language-${language || 'plaintext'}">${this.escapeHtml(code)}</code></pre>
                    <div class="code-block-resize-handle" onmousedown="window.codeBlockFunctions.startResize('${blockId}', event)"></div>
                </div>
            </div>
        `;
    }

    /**
     * Process code tokens in content
     * @param content The content to process
     * @returns The processed content with enhanced code tokens
     */
    public static processCodeTokens(content: string): string {
        if (!content) {
            return '';
        }

        // Regular expression to match code tokens {SomeCodeEntity}
        const tokenRegex = /\{([^\}]+)\}/g;

        // Replace tokens with clickable spans
        return content.replace(tokenRegex, (match, token) => {
            return `<span class="code-token" onclick="window.codeBlockFunctions.findCodeToken('${token}')">{${token}}</span>`;
        });
    }

    /**
     * Escape HTML special characters
     * @param text The text to escape
     * @returns The escaped text
     */
    private static escapeHtml(text: string): string {
        const map: { [key: string]: string } = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, m => map[m]);
    }

    /**
     * This method is no longer used. Code block functions are loaded directly from media/codeBlocks.js
     * @deprecated Use direct loading from media directory instead
     * @returns Empty string
     */
    public static getCodeBlockFunctions(): string {
        console.warn('getCodeBlockFunctions is deprecated. Use direct loading from media directory instead.');
        return '';
    }

    /**
     * This method is no longer used. Code block styles are loaded directly from media/codeBlocks.css
     * @deprecated Use direct loading from media directory instead
     * @returns Empty string
     */
    public static getCodeBlockStyles(): string {
        console.warn('getCodeBlockStyles is deprecated. Use direct loading from media directory instead.');
        return '';
    }
}
