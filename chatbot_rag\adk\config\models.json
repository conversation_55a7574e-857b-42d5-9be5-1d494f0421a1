{"default": {"provider": "google", "model_name": "gemini-1.5-pro"}, "providers": {"google": {"default_model": "gemini-1.5-pro", "models": {"gemini-1.5-pro": {"description": "Google's Gemini 1.5 Pro model - balanced performance and capabilities", "context_window": 1000000, "supports_tools": true}, "gemini-1.5-flash": {"description": "Google's Gemini 1.5 Flash model - faster and more cost-effective", "context_window": 1000000, "supports_tools": true}, "gemini-1.0-pro": {"description": "Google's Gemini 1.0 Pro model - previous generation", "context_window": 32000, "supports_tools": true}}}, "anthropic": {"default_model": "claude-3-sonnet-20240229", "models": {"claude-3-opus-20240229": {"description": "<PERSON><PERSON><PERSON>'s Claude 3 Opus - most powerful Claude model", "context_window": 200000, "supports_tools": true}, "claude-3-sonnet-20240229": {"description": "Anthropic's Claude 3 Sonnet - balanced performance and cost", "context_window": 200000, "supports_tools": true}, "claude-3-haiku-20240307": {"description": "Anthropic's Claude 3 Haiku - fastest and most cost-effective Claude model", "context_window": 200000, "supports_tools": true}, "claude-3.5-sonnet-20240620": {"description": "Anthropic's Claude 3.5 Sonnet - latest model with improved capabilities", "context_window": 200000, "supports_tools": true}}}, "ollama": {"default_model": "llama3", "models": {"llama3": {"description": "Meta's Llama 3 model running locally via Ollama", "context_window": 8192, "supports_tools": false}, "llama3:8b": {"description": "Meta's Llama 3 8B model running locally via Ollama", "context_window": 8192, "supports_tools": false}, "llama3:70b": {"description": "Meta's Llama 3 70B model running locally via Ollama", "context_window": 8192, "supports_tools": false}, "deepseek-coder": {"description": "DeepSeek Coder model running locally via Ollama", "context_window": 16384, "supports_tools": false}, "codellama": {"description": "Meta's Code Llama model running locally via Ollama", "context_window": 16384, "supports_tools": false}, "mistral": {"description": "Mistral AI's model running locally via Ollama", "context_window": 8192, "supports_tools": false}}}, "openai": {"default_model": "gpt-4o", "models": {"gpt-4o": {"description": "OpenAI's GPT-4o model - latest and most capable", "context_window": 128000, "supports_tools": true}, "gpt-4-turbo": {"description": "OpenAI's GPT-4 Turbo model", "context_window": 128000, "supports_tools": true}, "gpt-3.5-turbo": {"description": "OpenAI's GPT-3.5 Turbo model - faster and more cost-effective", "context_window": 16385, "supports_tools": true}}}}}