import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import * as os from 'os';
import axios from 'axios';
import { EventSourcePolyfill } from 'event-source-polyfill';
import { RagChatViewProvider } from './ragChatViewProvider';
import { SettingsManager } from './settingsManager';
import { DiffViewProvider, FileDiff } from './diffViewProvider';
import { AuthenticationManager } from './authenticationManager';
import { FileManager } from './fileManager';
import { CodeAnalyzer, ValidationResult } from './codeAnalyzer';
import { CodeTokenHandler, CodeToken } from './codeTokenHandler';
import { DiffManager } from './diffManager';
import { ChatHistoryManager, ChatSession, ChatMessage } from './chatHistoryManager';

interface SuggestedEdit {
    start: { line: number; character: number };
    end: { line: number; character: number };
    text: string;
}

interface ApiResponse {
    answer: string;
    suggestedEdits?: SuggestedEdit[];
}

// Legacy interface kept for backward compatibility
interface ChatHistoryItem {
    question: string;
    answer: string;
    timestamp: Date;
    fileEdits?: {
        fileName: string;
        applied: boolean;
        edits?: SuggestedEdit[];
    }[];
    messageId?: string;
    title?: string;
}

export class RagChatController {
    private _apiUrl: string;
    private _authManager: AuthenticationManager;
    private chatHistoryManager: ChatHistoryManager;

    constructor(private readonly context: vscode.ExtensionContext) {
        // Get API URL from settings
        const settings = SettingsManager.getSettings();
        this._apiUrl = settings.apiUrl;

        // Initialize authentication manager
        this._authManager = AuthenticationManager.getInstance();

        // Initialize chat history manager
        this.chatHistoryManager = new ChatHistoryManager(context);

        // Listen for configuration changes
        vscode.workspace.onDidChangeConfiguration(e => {
            if (e.affectsConfiguration('ragAssistant')) {
                const newSettings = SettingsManager.getSettings();
                this._apiUrl = newSettings.apiUrl;
            }
        });
    }

    // Method to update API URL
    public async updateApiUrl(newUrl: string) {
        await SettingsManager.updateSetting('apiUrl', newUrl);
        this._apiUrl = newUrl;
    }

    // Method to store chat history
    private async storeChatHistory(question: string, answer: string, fileEdits?: { fileName: string; applied: boolean }[], messageId?: string) {
        const history: ChatHistoryItem[] = this.context.globalState.get('chatHistory', []);

        // Generate a default title from the first line of the question
        // or the first few words if there are no line breaks
        let defaultTitle = question.split('\n')[0].trim();
        if (defaultTitle.length > 50) {
            // Truncate to first 50 chars and add ellipsis
            defaultTitle = defaultTitle.substring(0, 47) + '...';
        }

        history.push({
            question,
            answer,
            timestamp: new Date(),
            fileEdits,
            messageId,
            title: defaultTitle // Set the default title
        });

        // Respect the maxHistoryItems setting
        const settings = SettingsManager.getSettings();
        if (history.length > settings.maxHistoryItems) {
            // Remove oldest items
            history.splice(0, history.length - settings.maxHistoryItems);
        }

        await this.context.globalState.update('chatHistory', history);

        // Send updated history to webview
        return history;
    }

    // Method to get chat history (legacy format for backward compatibility)
    public async getChatHistory(): Promise<ChatHistoryItem[]> {
        // Get sessions from the chat history manager
        const sessions = this.chatHistoryManager.getSessions();

        // Convert sessions to legacy format
        return sessions.map((session: ChatSession) => {
            // Find the first user message and assistant message pair
            const userMessage = session.messages.find((m: ChatMessage) => m.isUser);
            const assistantMessage = session.messages.find((m: ChatMessage) => !m.isUser);

            // Create a legacy history item
            return {
                question: userMessage?.content || '',
                answer: assistantMessage?.content || '',
                timestamp: session.createdAt,
                fileEdits: assistantMessage?.fileEdits,
                messageId: assistantMessage?.id,
                title: session.title
            };
        });
    }

    // Method to clear chat history
    public async clearChatHistory() {
        await this.chatHistoryManager.clearSessions();
    }

    // Method to rename a chat history item
    public async renameChatHistoryItem(index: number, newTitle: string, chatViewProvider: RagChatViewProvider): Promise<boolean> {
        // Get all sessions
        const sessions = this.chatHistoryManager.getSessions();

        if (index >= 0 && index < sessions.length) {
            // Get the session ID
            const sessionId = sessions[index].id;

            // Update the title
            const success = await this.chatHistoryManager.renameSession(sessionId, newTitle);

            if (success) {
                // Send updated history to webview
                const updatedSessions = this.chatHistoryManager.getSessions();
                const legacyHistory = await this.getChatHistory();

                chatViewProvider.postMessage({
                    type: 'updateChatHistory',
                    history: legacyHistory
                });

                return true;
            }
        }

        return false;
    }

    // Method to load a specific history item
    public async loadHistoryItem(index: number, chatViewProvider: RagChatViewProvider) {
        // Get all sessions
        const sessions = this.chatHistoryManager.getSessions();

        if (index >= 0 && index < sessions.length) {
            // Get the session
            const session: ChatSession = sessions[index];

            // Set this session as active
            await this.chatHistoryManager.setActiveSession(session.id);

            // Clear the current chat view
            chatViewProvider.clearMessages();

            // Add all messages from the session to the chat view
            for (const message of session.messages) {
                if (message.isUser) {
                    chatViewProvider.addMessage(message.content, true);
                } else {
                    chatViewProvider.addMessage(message.content, false, message.fileEdits, message.id);
                }
            }
        }
    }

    public async handleQuestion(
        question: string,
        chatViewProvider: RagChatViewProvider,
        useStreaming: boolean = true
    ) {
        try {
            console.log(`Handling question: ${question}`);

            // Get current file context
            const editor = vscode.window.activeTextEditor;
            const fileContext = editor ? {
                fileName: editor.document.fileName,
                language: editor.document.languageId,
                selectedText: editor.document.getText(editor.selection),
                fullText: editor.document.getText()
            } : null;

            console.log('File context:', fileContext ? fileContext.fileName : 'No file open');

            // Add user message to chat
            console.log('Adding user message to chat');
            const messageId = chatViewProvider.addMessage(question, true);
            console.log(`Added user message with ID: ${messageId}`);

            if (!messageId) {
                console.error('Failed to add user message to chat');
            }

            try {
                // First, check if the API is available
                try {
                    const healthCheck = await axios.get(`${this._apiUrl}/health`, { timeout: 5000 });
                    if (healthCheck.status !== 200) {
                        throw new Error(`API health check failed with status: ${healthCheck.status}`);
                    }
                    console.log('API health check successful:', healthCheck.data);
                } catch (healthError) {
                    console.error('API health check failed:', healthError);
                    chatViewProvider.addMessage(
                        `I'm currently unable to connect to the RAG service at ${this._apiUrl}. ` +
                        `Please make sure the service is running. Error: ${healthError instanceof Error ? healthError.message : 'Unknown error'}`,
                        false
                    );
                    return;
                }

                // Check if authentication is required and authenticate if needed
                if (this._authManager.authConfig.enabled && !this._authManager.isAuthenticated) {
                    const authenticated = await this._authManager.authenticate();
                    if (!authenticated) {
                        chatViewProvider.addMessage(
                            "Authentication failed. Please check your credentials in the settings.",
                            false
                        );
                        return;
                    }
                }

                // Get authentication headers
                const headers = this._authManager.getAuthHeaders();

                // Create a message ID for the assistant's response
                const assistantMessageId = `msg-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

                if (useStreaming) {
                    // Use streaming API
                    console.log(`DEBUG: Making streaming API request to ${this._apiUrl}/ask/stream`);

                    try {
                        // Add an empty assistant message that will be updated with streaming content
                        chatViewProvider.addMessage('', false, undefined, assistantMessageId);

                        // Create EventSource for streaming
                        // Add the question as a query parameter since EventSource only supports GET
                        const encodedQuestion = encodeURIComponent(question);
                        const eventSourceUrl = `${this._apiUrl}/ask/stream?question=${encodedQuestion}`;

                        // Note: We're passing the question in the URL
                        // In a production app, you would need to handle more complex data like context
                        // by modifying the backend to accept JSON in the request body

                        // Create EventSource with GET data
                        console.log(`DEBUG: Creating EventSource with URL: ${eventSourceUrl}`);
                        const eventSource = new EventSourcePolyfill(eventSourceUrl, {
                            headers: headers,
                            withCredentials: false, // Set to false for cross-origin requests
                            heartbeatTimeout: 60000
                        } as any);

                        // Add event listeners for open and error events
                        eventSource.onopen = (event) => {
                            console.log(`DEBUG: EventSource connection opened`, event);
                        };

                        // Note: We're using a workaround here because EventSource doesn't natively support POST
                        // In a production app, you would modify the backend to accept the data in the URL or headers

                        let fullResponse = '';

                        // Handle streaming events
                        eventSource.onmessage = async (event) => {
                            console.log(`DEBUG: Received message event:`, event);
                            console.log(`DEBUG: Message data:`, event.data);

                            try {
                                const data = JSON.parse(event.data);
                                console.log(`DEBUG: Parsed streaming data:`, data);

                                if (data.type === 'token') {
                                    // Append token to the full response
                                    fullResponse += data.content;

                                    // Update the message in the UI
                                    chatViewProvider.updateMessage(assistantMessageId, fullResponse);
                                } else if (data.type === 'end') {
                                    // Stream completed
                                    console.log(`DEBUG: Streaming completed`);
                                    eventSource.close();

                                    // Store in chat history using the new ChatHistoryManager
                                    // First, check if we have an active session
                                    let activeSession = this.chatHistoryManager.getActiveSession();

                                    // If no active session, create a new one
                                    if (!activeSession) {
                                        await this.chatHistoryManager.createSession();
                                    }

                                    // Add the messages to the session
                                    await this.chatHistoryManager.addMessage(question, true);
                                    await this.chatHistoryManager.addMessage(fullResponse, false, undefined, assistantMessageId);

                                    // Send updated history to webview
                                    const legacyHistory = await this.getChatHistory();
                                    chatViewProvider.postMessage({
                                        type: 'updateChatHistory',
                                        history: legacyHistory
                                    });
                                } else if (data.type === 'error') {
                                    console.error(`DEBUG: Streaming error:`, data.content);
                                    eventSource.close();
                                    chatViewProvider.updateMessage(assistantMessageId,
                                        `Error: ${data.content}`);
                                }
                            } catch (parseError) {
                                console.error(`DEBUG: Error parsing streaming data:`, parseError);
                            }
                        };

                        eventSource.onerror = (error) => {
                            console.error(`DEBUG: EventSource error:`, error);

                            // Log more details about the error
                            if (error instanceof Event) {
                                console.error(`DEBUG: EventSource error type: ${error.type}`);
                                console.error(`DEBUG: EventSource readyState: ${eventSource.readyState}`);
                            }

                            // Close the connection
                            eventSource.close();

                            // Update the message with an error
                            chatViewProvider.updateMessage(assistantMessageId,
                                `Error: Connection to the server was lost. Please try again.\n\nDetails: ${error instanceof Error ? error.message : 'Unknown error'}`);
                        };

                        // Return early as the response will be handled asynchronously
                        return;
                    } catch (streamingError) {
                        console.error(`DEBUG: Streaming setup failed:`, streamingError);
                        // Fall back to non-streaming API if streaming fails
                        console.log(`DEBUG: Falling back to non-streaming API`);
                    }
                }

                // Non-streaming API (fallback)
                // Make the API request with authentication headers
                console.log(`DEBUG: Sending request to ${this._apiUrl}/ask with question: ${question}`);
                console.log(`DEBUG: Request payload:`, JSON.stringify({
                    question,
                    context: fileContext
                }, null, 2));

                console.log(`DEBUG: Starting API request...`);
                // Declare response variable outside the try block so it's accessible throughout the function
                let response;

                try {
                    response = await axios.post<ApiResponse>(`${this._apiUrl}/ask`, {
                        question,
                        context: fileContext,
                        selected_subjects: [] // Add any selected subjects if you have them
                    }, {
                        headers,
                        timeout: 30000 // 30 seconds timeout
                    });

                    console.log(`DEBUG: API request successful, status: ${response.status}`);
                    console.log(`DEBUG: Response data:`, JSON.stringify(response.data, null, 2));
                } catch (apiError) {
                    console.error(`DEBUG: API request failed:`, apiError);
                    throw apiError; // Re-throw to be caught by the outer catch block
                }

                if (!response) {
                    throw new Error('API request failed with no response');
                }

                // Prepare file edits information if there are suggested edits
                let fileEdits: {
                    fileName: string;
                    applied: boolean;
                    edits?: SuggestedEdit[];
                }[] | undefined;

                console.log(`DEBUG: Processing suggested edits...`);
                if (response.data.suggestedEdits && response.data.suggestedEdits.length > 0 && fileContext) {
                    console.log(`DEBUG: Found ${response.data.suggestedEdits.length} suggested edits`);
                    fileEdits = [{
                        fileName: fileContext.fileName,
                        applied: false,
                        edits: response.data.suggestedEdits
                    }];
                }

                // Add assistant response to chat with file edit info
                console.log(`DEBUG: Adding assistant response to chat: ${response.data.answer.substring(0, 50)}...`);
                const messageId = chatViewProvider.addMessage(response.data.answer, false, fileEdits);
                console.log(`DEBUG: Added assistant message with ID: ${messageId}`);

                // Store in chat history using the new ChatHistoryManager
                console.log(`DEBUG: Storing chat history...`);

                // First, check if we have an active session
                let activeSession = this.chatHistoryManager.getActiveSession();

                // If no active session, create a new one
                if (!activeSession) {
                    await this.chatHistoryManager.createSession();
                }

                // Add the messages to the session
                await this.chatHistoryManager.addMessage(question, true);
                await this.chatHistoryManager.addMessage(response.data.answer, false, fileEdits, messageId);

                // Get the legacy history format for the webview
                const legacyHistory = await this.getChatHistory();
                console.log(`DEBUG: Chat history stored, sessions: ${this.chatHistoryManager.getSessions().length}`);

                // Send updated history to webview
                console.log(`DEBUG: Sending updated history to webview...`);
                chatViewProvider.postMessage({
                    type: 'updateChatHistory',
                    history: legacyHistory
                });
                console.log(`DEBUG: History update sent to webview`);

                // Handle suggested edits if they exist
                if (response.data.suggestedEdits && response.data.suggestedEdits.length > 0 && fileContext) {
                    console.log(`DEBUG: Handling suggested edits UI...`);
                    // The user will now use the UI to apply edits
                    // We'll handle this via message passing from the webview
                }
            } catch (error) {
                // Handle API errors with more detailed messages
                console.error('Error communicating with RAG API:', error);

                let errorMessage = "I'm currently unable to connect to the RAG service.";

                if (axios.isAxiosError(error)) {
                    if (error.response) {
                        // The request was made and the server responded with a status code
                        // that falls out of the range of 2xx
                        errorMessage += ` Server responded with status ${error.response.status}: ${error.response.data?.detail || error.message}`;
                    } else if (error.request) {
                        // The request was made but no response was received
                        errorMessage += " No response received from server. Please check if the service is running.";
                    } else {
                        // Something happened in setting up the request that triggered an Error
                        errorMessage += ` Error: ${error.message}`;
                    }
                } else if (error instanceof Error) {
                    errorMessage += ` Error: ${error.message}`;
                }

                // Add a suggestion to check settings
                errorMessage += `\n\nPlease verify that the API URL (${this._apiUrl}) is correct in the settings.`;

                chatViewProvider.addMessage(errorMessage, false);
            }
        } catch (error) {
            vscode.window.showErrorMessage('Error communicating with RAG Assistant');
            console.error(error);
        }
    }

    // Method to apply edits to a file
    public async applyEditsToFile(messageId: string, fileName: string, chatViewProvider: RagChatViewProvider) {
        try {
            // Get the history to find the suggested edits
            const history: ChatHistoryItem[] = this.context.globalState.get('chatHistory', []);
            const historyItem = history.find(item => item.messageId === messageId);

            if (!historyItem) {
                vscode.window.showErrorMessage('Could not find the associated message');
                return;
            }

            // Open the file
            const document = await vscode.workspace.openTextDocument(fileName);
            const originalContent = document.getText();

            // Find the file edit entry for this file
            const fileEditEntry = historyItem.fileEdits?.find(edit => edit.fileName === fileName);

            // Get the suggested edits from the history item or use a placeholder edit
            const suggestedEdits: FileDiff['edits'] = fileEditEntry?.edits || [
                {
                    start: { line: 0, character: 0 },
                    end: { line: 0, character: 0 },
                    text: '// Edited by RAG Assistant\n'
                }
            ];

            // Generate a file diff
            const fileDiff = DiffViewProvider.generateFileDiff(
                fileName,
                originalContent,
                suggestedEdits
            );

            // Show the diff view and ask for confirmation
            const shouldApply = await DiffViewProvider.showDiff(fileDiff);

            if (shouldApply) {
                // Apply the edits to the file
                const editor = await vscode.window.showTextDocument(document);
                await editor.edit(editBuilder => {
                    for (const edit of suggestedEdits) {
                        const range = new vscode.Range(
                            new vscode.Position(edit.start.line, edit.start.character),
                            new vscode.Position(edit.end.line, edit.end.character)
                        );
                        editBuilder.replace(range, edit.text);
                    }
                });

                // Update the file edit status in the chat history manager
                await this.chatHistoryManager.updateFileEditStatus(messageId, fileName, true);

                // Also update in the legacy history for backward compatibility
                if (historyItem.fileEdits) {
                    historyItem.fileEdits.forEach((edit: { fileName: string; applied: boolean }) => {
                        if (edit.fileName === fileName) {
                            edit.applied = true;
                        }
                    });
                    await this.context.globalState.update('chatHistory', history);
                }

                // Notify the webview that the edit was applied
                chatViewProvider.postMessage({
                    type: 'updateFileEdit',
                    messageId,
                    fileName
                });

                vscode.window.showInformationMessage('Edits applied successfully');
            } else {
                vscode.window.showInformationMessage('Edit operation cancelled');
            }
        } catch (error) {
            vscode.window.showErrorMessage(`Error applying edits: ${error}`);
            console.error(error);
        }
    }

    // Method to open a file for viewing
    public async openFileForViewing(fileName: string) {
        try {
            const document = await vscode.workspace.openTextDocument(fileName);
            await vscode.window.showTextDocument(document);
        } catch (error) {
            vscode.window.showErrorMessage(`Error opening file: ${error}`);
            console.error(error);
        }
    }

    // New methods for file handling

    /**
     * Find a code token in the workspace
     * @param token The token to find
     * @param filePath Optional file path to search in
     * @param tokenType Optional token type
     * @returns A promise that resolves to the token information
     */
    public async findCodeToken(
        token: string,
        filePath?: string,
        tokenType?: string
    ): Promise<CodeToken | undefined> {
        try {
            // Use the CodeTokenHandler to find the token
            const tokenHandler = CodeTokenHandler.getInstance();
            return await tokenHandler.findCodeToken(token, filePath, tokenType);
        } catch (error) {
            vscode.window.showErrorMessage(`Error finding token: ${error}`);
            console.error(error);
            return undefined;
        }
    }

    /**
     * Get information about a code token
     * @param token The token name
     * @returns A promise that resolves to the token information
     */
    public async getTokenInfo(token: string): Promise<CodeToken | undefined> {
        try {
            // Get the token from the cache
            const tokenHandler = CodeTokenHandler.getInstance();
            return tokenHandler.getToken(token);
        } catch (error) {
            console.error(`Error getting token info: ${error}`);
            return undefined;
        }
    }

    /**
     * Get the token history
     * @returns A promise that resolves to the token history
     */
    public async getTokenHistory(): Promise<string[]> {
        try {
            // Get the token history
            const tokenHandler = CodeTokenHandler.getInstance();
            return tokenHandler.getTokenHistory();
        } catch (error) {
            console.error(`Error getting token history: ${error}`);
            return [];
        }
    }

    /**
     * Apply changes to a file
     * @param blockId The ID of the code block
     * @param filePath The file path
     * @param code The new code
     * @param chatViewProvider The chat view provider
     */
    public async applyChangesToFile(
        blockId: string,
        filePath: string,
        code: string,
        chatViewProvider: RagChatViewProvider
    ): Promise<void> {
        try {
            const fileManager = FileManager.getInstance();
            const diffManager = DiffManager.getInstance();

            // Check if the file exists
            const absolutePath = fileManager.getAbsolutePath(filePath);
            if (!absolutePath || !fileManager.fileExists(absolutePath)) {
                vscode.window.showErrorMessage(`File not found: ${filePath}`);
                return;
            }

            // Create a backup of the file
            const backupPath = await fileManager.createBackup(absolutePath);

            // Open the file
            const document = await vscode.workspace.openTextDocument(absolutePath);
            const originalContent = document.getText();

            // Generate a diff between the original and new content
            const diffInfo = diffManager.generateDiff(filePath, originalContent, code);

            // Validate the edit
            const codeAnalyzer = CodeAnalyzer.getInstance();
            const edit: { start: vscode.Position; end: vscode.Position; text: string } = {
                start: new vscode.Position(0, 0),
                end: new vscode.Position(document.lineCount - 1, document.lineAt(document.lineCount - 1).text.length),
                text: code
            };
            const validationResult = await codeAnalyzer.validateEdits(absolutePath, [edit]);

            // Show the diff and ask for confirmation
            const shouldApply = await diffManager.showDiffPanel(diffInfo);

            if (!shouldApply) {
                // User cancelled the operation
                // Delete the backup
                const fs = require('fs');
                if (fs.existsSync(backupPath)) {
                    fs.unlinkSync(backupPath);
                }
                vscode.window.showInformationMessage('Changes cancelled');
                return;
            }

            // Show a warning if there are validation errors or warnings
            if (!validationResult.valid || validationResult.warnings.length > 0) {
                // Prepare the message
                let message = 'The changes may contain issues:\n';

                // Add errors
                if (validationResult.errors.length > 0) {
                    message += '\nErrors:\n';
                    validationResult.errors.forEach(error => {
                        message += `- ${error.message} (Line ${error.range.start.line + 1})\n`;
                    });
                }

                // Add warnings
                if (validationResult.warnings.length > 0) {
                    message += '\nWarnings:\n';
                    validationResult.warnings.forEach(warning => {
                        message += `- ${warning.message} (Line ${warning.range.start.line + 1})\n`;
                    });
                }

                // Add suggestions count if available
                if (validationResult.suggestions.length > 0) {
                    message += `\n${validationResult.suggestions.length} suggestions available.`;
                }

                // Show the warning
                const proceed = await vscode.window.showWarningMessage(
                    message,
                    'Apply Anyway',
                    'Show Details',
                    'Cancel'
                );

                if (proceed === 'Show Details') {
                    // Show detailed validation results in a new document
                    await this.showValidationDetails(filePath, validationResult);

                    // Ask again after showing details
                    const proceedAfterDetails = await vscode.window.showWarningMessage(
                        'Do you want to apply the changes?',
                        'Apply Anyway',
                        'Cancel'
                    );

                    if (proceedAfterDetails !== 'Apply Anyway') {
                        // Delete the backup
                        const fs = require('fs');
                        if (fs.existsSync(backupPath)) {
                            fs.unlinkSync(backupPath);
                        }
                        return;
                    }
                } else if (proceed !== 'Apply Anyway') {
                    // Delete the backup
                    const fs = require('fs');
                    if (fs.existsSync(backupPath)) {
                        fs.unlinkSync(backupPath);
                    }
                    return;
                }
            }

            // Apply the edit
            await fileManager.applyEdits(absolutePath, [edit]);

            // Store the backup path in the extension context
            const backupInfo = this.context.workspaceState.get<{[key: string]: string}>('fileBackups') || {};
            backupInfo[blockId] = backupPath;
            await this.context.workspaceState.update('fileBackups', backupInfo);

            // Store the diff info in the extension context
            const diffInfos = this.context.workspaceState.get<{[key: string]: any}>('diffInfos') || {};
            diffInfos[blockId] = {
                filePath,
                addedLines: diffInfo.addedLines,
                removedLines: diffInfo.removedLines,
                timestamp: new Date().toISOString()
            };
            await this.context.workspaceState.update('diffInfos', diffInfos);

            // Notify the webview that the changes were applied
            chatViewProvider.postMessage({
                type: 'updateCodeBlock',
                blockId,
                action: 'applied',
                addedLines: diffInfo.addedLines,
                removedLines: diffInfo.removedLines
            });

            vscode.window.showInformationMessage(`Changes applied to ${filePath}`);
        } catch (error) {
            vscode.window.showErrorMessage(`Error applying changes: ${error}`);
            console.error(error);
        }
    }

    /**
     * Show validation details in a new document
     * @param filePath The file path
     * @param validationResult The validation result
     */
    private async showValidationDetails(filePath: string, validationResult: ValidationResult): Promise<void> {
        try {
            // Create a temporary file for the validation details
            const tempFilePath = path.join(os.tmpdir(), `validation-${Date.now()}.md`);

            // Prepare the content
            let content = `# Validation Results for ${path.basename(filePath)}

`;

            // Add file path
            content += `**File:** ${filePath}\n\n`;

            // Add summary
            content += `## Summary\n\n`;
            content += `- **Valid:** ${validationResult.valid ? 'Yes' : 'No'}\n`;
            content += `- **Errors:** ${validationResult.errors.length}\n`;
            content += `- **Warnings:** ${validationResult.warnings.length}\n`;
            content += `- **Suggestions:** ${validationResult.suggestions.length}\n\n`;

            // Add errors
            if (validationResult.errors.length > 0) {
                content += `## Errors\n\n`;
                validationResult.errors.forEach((error, index) => {
                    content += `### Error ${index + 1}\n\n`;
                    content += `- **Message:** ${error.message}\n`;
                    content += `- **Line:** ${error.range.start.line + 1}\n`;
                    content += `- **Column:** ${error.range.start.character + 1}\n`;
                    if (error.source) {
                        content += `- **Source:** ${error.source}\n`;
                    }
                    if (error.code) {
                        content += `- **Code:** ${error.code}\n`;
                    }
                    content += `\n`;
                });
            }

            // Add warnings
            if (validationResult.warnings.length > 0) {
                content += `## Warnings\n\n`;
                validationResult.warnings.forEach((warning, index) => {
                    content += `### Warning ${index + 1}\n\n`;
                    content += `- **Message:** ${warning.message}\n`;
                    content += `- **Line:** ${warning.range.start.line + 1}\n`;
                    content += `- **Column:** ${warning.range.start.character + 1}\n`;
                    if (warning.source) {
                        content += `- **Source:** ${warning.source}\n`;
                    }
                    if (warning.code) {
                        content += `- **Code:** ${warning.code}\n`;
                    }
                    content += `\n`;
                });
            }

            // Add suggestions
            if (validationResult.suggestions.length > 0) {
                content += `## Suggestions\n\n`;
                validationResult.suggestions.forEach((suggestion, index) => {
                    content += `### Suggestion ${index + 1}\n\n`;
                    content += `- **Message:** ${suggestion.message}\n`;
                    content += `- **Line:** ${suggestion.range.start.line + 1}\n`;
                    content += `- **Column:** ${suggestion.range.start.character + 1}\n`;
                    if (suggestion.replacement) {
                        content += `- **Replacement:** \`${suggestion.replacement}\`\n`;
                    }
                    content += `\n`;
                });
            }

            // Write the content to the temporary file
            fs.writeFileSync(tempFilePath, content);

            // Open the file
            const document = await vscode.workspace.openTextDocument(tempFilePath);
            await vscode.window.showTextDocument(document);
        } catch (error) {
            console.error(`Error showing validation details: ${error}`);
            vscode.window.showErrorMessage(`Error showing validation details: ${error}`);
        }
    }

    /**
     * Undo changes to a file
     * @param blockId The ID of the code block
     * @param filePath The file path
     * @param chatViewProvider The chat view provider
     */
    public async undoChangesToFile(
        blockId: string,
        filePath: string,
        chatViewProvider: RagChatViewProvider
    ): Promise<void> {
        try {
            // Get the backup path from the extension context
            const backupInfo = this.context.workspaceState.get<{[key: string]: string}>('fileBackups') || {};
            const backupPath = backupInfo[blockId];

            if (!backupPath) {
                vscode.window.showErrorMessage(`No backup found for ${filePath}`);
                return;
            }

            // Ask for confirmation
            const shouldUndo = await vscode.window.showWarningMessage(
                `Are you sure you want to undo changes to ${filePath}?`,
                'Undo Changes',
                'Cancel'
            );

            if (shouldUndo !== 'Undo Changes') {
                return;
            }

            // Restore the file from the backup
            const fileManager = FileManager.getInstance();
            await fileManager.restoreFromBackup(backupPath);

            // Remove the backup info
            delete backupInfo[blockId];
            await this.context.workspaceState.update('fileBackups', backupInfo);

            // Remove the diff info
            const diffInfos = this.context.workspaceState.get<{[key: string]: any}>('diffInfos') || {};
            delete diffInfos[blockId];
            await this.context.workspaceState.update('diffInfos', diffInfos);

            // Notify the webview that the changes were undone
            chatViewProvider.postMessage({
                type: 'updateCodeBlock',
                blockId,
                action: 'undone'
            });

            vscode.window.showInformationMessage(`Changes to ${filePath} have been undone`);
        } catch (error) {
            vscode.window.showErrorMessage(`Error undoing changes: ${error}`);
            console.error(error);
        }
    }

    /**
     * Create a new chat session
     * @param chatViewProvider The chat view provider to update
     */
    public async createNewSession(chatViewProvider: RagChatViewProvider): Promise<void> {
        try {
            // Clear the current messages in the UI
            chatViewProvider.clearMessages();

            // Create a new session
            await this.chatHistoryManager.createSession();

            // Update the chat history in the UI
            const legacyHistory = await this.getChatHistory();
            chatViewProvider.postMessage({
                type: 'updateChatHistory',
                history: legacyHistory
            });

            vscode.window.showInformationMessage('New chat session created');
        } catch (error) {
            vscode.window.showErrorMessage(`Error creating new session: ${error instanceof Error ? error.message : String(error)}`);
            console.error(error);
        }
    }

    /**
     * Delete a chat session
     * @param index The index of the session to delete
     * @param chatViewProvider The chat view provider to update
     */
    public async deleteSession(index: number, chatViewProvider: RagChatViewProvider): Promise<void> {
        try {
            // Get all sessions
            const sessions = this.chatHistoryManager.getSessions();

            if (index >= 0 && index < sessions.length) {
                // Get the session ID
                const sessionId = sessions[index].id;

                // Check if this is the active session
                const isActiveSession = sessions[index].isActive;

                // Delete the session
                const success = await this.chatHistoryManager.deleteSession(sessionId);

                if (success) {
                    // If we deleted the active session, clear the messages in the UI
                    if (isActiveSession) {
                        chatViewProvider.clearMessages();
                    }

                    // Update the chat history in the UI
                    const legacyHistory = await this.getChatHistory();
                    chatViewProvider.postMessage({
                        type: 'updateChatHistory',
                        history: legacyHistory
                    });

                    vscode.window.showInformationMessage('Chat session deleted');
                } else {
                    vscode.window.showErrorMessage('Failed to delete chat session');
                }
            }
        } catch (error) {
            vscode.window.showErrorMessage(`Error deleting session: ${error instanceof Error ? error.message : String(error)}`);
            console.error(error);
        }
    }
}



