// VS Code API
import * as vscode from 'vscode';
import { SettingsManager } from './settingsManager';

// This class handles markdown rendering and syntax highlighting
export class MarkdownRenderer {
    // Get the HTML and CSS for the markdown renderer
    public static getRendererScript(): string {
        return `
        <script>
            // Function to load scripts dynamically
            function loadScript(url, callback) {
                const script = document.createElement('script');
                script.type = 'text/javascript';
                script.src = url;
                script.onload = callback;
                document.head.appendChild(script);
            }

            // Function to load CSS dynamically
            function loadCSS(url) {
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.type = 'text/css';
                link.href = url;
                document.head.appendChild(link);
            }

            // Load marked.js for markdown parsing
            loadScript('https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js', function() {
                // Load highlight.js for syntax highlighting
                loadScript('https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/lib/highlight.min.js', function() {
                    // Configure marked to use highlight.js
                    marked.setOptions({
                        highlight: function(code, lang) {
                            if (lang && hljs.getLanguage(lang)) {
                                return hljs.highlight(code, { language: lang }).value;
                            }
                            return hljs.highlightAuto(code).value;
                        },
                        breaks: true,
                        gfm: true
                    });

                    // Notify that markdown is ready
                    if (window.markdownReady) {
                        window.markdownReady();
                    }
                });

                // Load highlight.js styles
                loadCSS('https://cdn.jsdelivr.net/npm/highlight.js@11.7.0/styles/vs2015.min.css');
            });

            // Function to render markdown
            function renderMarkdown(text, container) {
                if (typeof marked !== 'undefined') {
                    container.innerHTML = marked.parse(text);

                    // Apply syntax highlighting to code blocks
                    if (typeof hljs !== 'undefined') {
                        container.querySelectorAll('pre code').forEach((block) => {
                            hljs.highlightElement(block);
                        });
                    }
                } else {
                    // Fallback if marked is not loaded
                    container.textContent = text;
                }
            }
        </script>
        `;
    }

    // Get the CSS for markdown content
    public static getMarkdownCSS(): string {
        return `
        <style>
            .markdown-content {
                font-family: var(--vscode-font-family);
                line-height: 1.5;
                word-wrap: break-word;
            }

            .markdown-content h1,
            .markdown-content h2,
            .markdown-content h3,
            .markdown-content h4,
            .markdown-content h5,
            .markdown-content h6 {
                margin-top: 24px;
                margin-bottom: 16px;
                font-weight: 600;
                line-height: 1.25;
            }

            .markdown-content h1 {
                font-size: 2em;
                border-bottom: 1px solid var(--vscode-editor-lineHighlightBorder);
                padding-bottom: 0.3em;
            }

            .markdown-content h2 {
                font-size: 1.5em;
                border-bottom: 1px solid var(--vscode-editor-lineHighlightBorder);
                padding-bottom: 0.3em;
            }

            .markdown-content h3 {
                font-size: 1.25em;
            }

            .markdown-content p {
                margin-top: 0;
                margin-bottom: 16px;
            }

            .markdown-content code {
                font-family: var(--vscode-editor-font-family, monospace);
                padding: 0.2em 0.4em;
                margin: 0;
                font-size: 85%;
                background-color: var(--vscode-editor-inactiveSelectionBackground);
                border-radius: 3px;
            }

            .markdown-content pre {
                font-family: var(--vscode-editor-font-family, monospace);
                padding: 16px;
                overflow: auto;
                font-size: 85%;
                line-height: 1.45;
                background-color: var(--vscode-editor-background);
                border-radius: 3px;
                margin-top: 0;
                margin-bottom: 16px;
                word-wrap: normal;
            }

            .markdown-content pre code {
                display: inline;
                max-width: auto;
                padding: 0;
                margin: 0;
                overflow: visible;
                line-height: inherit;
                word-wrap: normal;
                background-color: transparent;
                border: 0;
            }

            .markdown-content blockquote {
                padding: 0 1em;
                color: var(--vscode-descriptionForeground);
                border-left: 0.25em solid var(--vscode-editor-lineHighlightBorder);
                margin-left: 0;
                margin-right: 0;
            }

            .markdown-content ul,
            .markdown-content ol {
                padding-left: 2em;
                margin-top: 0;
                margin-bottom: 16px;
            }

            .markdown-content table {
                display: block;
                width: 100%;
                overflow: auto;
                margin-top: 0;
                margin-bottom: 16px;
                border-spacing: 0;
                border-collapse: collapse;
            }

            .markdown-content table th {
                font-weight: 600;
                padding: 6px 13px;
                border: 1px solid var(--vscode-editor-lineHighlightBorder);
            }

            .markdown-content table td {
                padding: 6px 13px;
                border: 1px solid var(--vscode-editor-lineHighlightBorder);
            }

            .markdown-content table tr {
                background-color: var(--vscode-editor-background);
                border-top: 1px solid var(--vscode-editor-lineHighlightBorder);
            }

            .markdown-content table tr:nth-child(2n) {
                background-color: var(--vscode-editor-inactiveSelectionBackground);
            }

            .markdown-content img {
                max-width: 100%;
                box-sizing: content-box;
            }

            .markdown-content hr {
                height: 0.25em;
                padding: 0;
                margin: 24px 0;
                background-color: var(--vscode-editor-lineHighlightBorder);
                border: 0;
            }
        </style>
        `;
    }

    // Check if markdown support is enabled in settings
    public static isMarkdownEnabled(): boolean {
        return SettingsManager.getSettings().markdownSupport;
    }

    // Check if syntax highlighting is enabled in settings
    public static isSyntaxHighlightingEnabled(): boolean {
        return SettingsManager.getSettings().syntaxHighlighting;
    }
}
