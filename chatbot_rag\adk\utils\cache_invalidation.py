"""
Cache invalidation manager for tool result caching.

This module provides mechanisms to automatically invalidate cache entries
when data changes, such as when files are edited, created, or deleted.
"""

import logging
import os
import re
import fnmatch
from typing import Dict, Any, List, Optional, Set, Callable
import time

from chatbot_rag.adk.utils.tool_cache import get_tool_cache

# Configure logging
logger = logging.getLogger("cache_invalidation")

class CacheInvalidationManager:
    """
    Manager for cache invalidation triggers.

    This class provides methods to:
    1. Register invalidation triggers for specific events
    2. Invalidate cache entries based on file paths and patterns
    3. Track file modifications to detect changes
    """

    def __init__(self):
        """Initialize the cache invalidation manager."""
        self.tool_cache = get_tool_cache()

        # Map of tool names to file patterns they depend on
        self.tool_dependencies: Dict[str, List[str]] = {
            # Search tools depend on all code files
            "search_code": ["*.py", "*.js", "*.ts", "*.html", "*.css", "*.json"],

            # File structure tools depend on directory structure
            "get_file_structure": ["*"],

            # File reading tools depend on specific files
            "read_file": ["*"],

            # Codebase understanding tools depend on all code files
            "understand_codebase": ["*.py", "*.js", "*.ts", "*.html", "*.css", "*.json"],
        }

        # Map of file paths to last modified timestamps
        self.file_timestamps: Dict[str, float] = {}

        # Set of recently modified files
        self.recently_modified: Set[str] = set()

        # Register default invalidation triggers
        self._register_default_triggers()

        logger.info("Cache invalidation manager initialized")

    def _register_default_triggers(self):
        """Register default invalidation triggers."""
        # These will be called by the VS Code extension when files change
        pass

    def invalidate_for_file_change(self, file_path: str):
        """
        Invalidate cache entries when a file changes.

        Args:
            file_path: Path to the changed file
        """
        # Update the timestamp for this file
        self.file_timestamps[file_path] = time.time()

        # Add to recently modified files
        self.recently_modified.add(file_path)

        # Keep only the 100 most recent files
        if len(self.recently_modified) > 100:
            self.recently_modified = set(sorted(
                self.recently_modified,
                key=lambda f: self.file_timestamps.get(f, 0),
                reverse=True
            )[:100])

        # Get file extension
        _, ext = os.path.splitext(file_path)
        ext = ext.lower()

        # Determine which tools to invalidate based on file type
        tools_to_invalidate = set()

        for tool_name, patterns in self.tool_dependencies.items():
            for pattern in patterns:
                if fnmatch.fnmatch(file_path, pattern) or fnmatch.fnmatch(os.path.basename(file_path), pattern):
                    tools_to_invalidate.add(tool_name)
                    break

        # Invalidate cache for affected tools
        for tool_name in tools_to_invalidate:
            self.tool_cache.invalidate(tool_name)
            logger.info(f"Invalidated cache for tool '{tool_name}' due to changes in {file_path}")

    def invalidate_for_directory_change(self, directory_path: str):
        """
        Invalidate cache entries when a directory changes.

        Args:
            directory_path: Path to the changed directory
        """
        # Invalidate file structure tools
        self.tool_cache.invalidate("get_file_structure")
        logger.info(f"Invalidated cache for file structure tools due to changes in directory {directory_path}")

        # Invalidate search tools if it's a code directory
        code_dirs = ["src", "lib", "app", "components", "utils", "helpers", "services", "models", "controllers"]
        dir_name = os.path.basename(directory_path)

        if dir_name in code_dirs or any(code_dir in directory_path for code_dir in code_dirs):
            self.tool_cache.invalidate("search_code")
            self.tool_cache.invalidate("understand_codebase")
            logger.info(f"Invalidated cache for code search tools due to changes in directory {directory_path}")

    def invalidate_for_file_creation(self, file_path: str):
        """
        Invalidate cache entries when a file is created.

        Args:
            file_path: Path to the created file
        """
        # Similar to file change, but might have different logic in the future
        self.invalidate_for_file_change(file_path)

    def invalidate_for_file_deletion(self, file_path: str):
        """
        Invalidate cache entries when a file is deleted.

        Args:
            file_path: Path to the deleted file
        """
        # Remove from timestamps and recently modified
        if file_path in self.file_timestamps:
            del self.file_timestamps[file_path]

        if file_path in self.recently_modified:
            self.recently_modified.remove(file_path)

        # Similar invalidation logic to file change
        self.invalidate_for_file_change(file_path)

    def invalidate_for_refactoring(self, file_paths: List[str]):
        """
        Invalidate cache entries when code is refactored.

        Args:
            file_paths: Paths to the refactored files
        """
        # Refactoring often affects multiple files and can have wider impact
        for file_path in file_paths:
            self.invalidate_for_file_change(file_path)

        # Additionally, invalidate codebase understanding
        self.tool_cache.invalidate("understand_codebase")
        logger.info(f"Invalidated cache for codebase understanding due to refactoring of {len(file_paths)} files")

    def check_for_changes(self):
        """
        Check for file changes and invalidate cache if needed.

        This method can be called periodically to detect file changes
        that weren't explicitly reported through the other methods.
        """
        # Get all tracked files
        tracked_files = set(self.file_timestamps.keys())

        # Check if any tracked files have changed
        for file_path in tracked_files:
            if os.path.exists(file_path):
                current_mtime = os.path.getmtime(file_path)
                last_mtime = self.file_timestamps.get(file_path, 0)

                if current_mtime > last_mtime:
                    logger.info(f"Detected change in {file_path}")
                    self.invalidate_for_file_change(file_path)
            else:
                # File has been deleted
                logger.info(f"Detected deletion of {file_path}")
                self.invalidate_for_file_deletion(file_path)

    def register_tool_dependency(self, tool_name: str, file_patterns: List[str]):
        """
        Register file patterns that a tool depends on.

        Args:
            tool_name: Name of the tool
            file_patterns: List of file patterns the tool depends on
        """
        self.tool_dependencies[tool_name] = file_patterns
        logger.info(f"Registered dependencies for tool '{tool_name}': {file_patterns}")

    def get_invalidated_tools(self) -> Set[str]:
        """
        Get the set of tools that have been invalidated.

        Returns:
            Set of tool names that have been invalidated
        """
        # This is a placeholder implementation
        # In a real implementation, you would track which tools have been invalidated
        return set(self.tool_dependencies.keys())

# Global invalidation manager instance
_global_invalidation_manager: Optional[CacheInvalidationManager] = None

def get_invalidation_manager() -> CacheInvalidationManager:
    """
    Get the global cache invalidation manager instance.

    Returns:
        The global cache invalidation manager instance
    """
    global _global_invalidation_manager

    if _global_invalidation_manager is None:
        _global_invalidation_manager = CacheInvalidationManager()

    return _global_invalidation_manager

# Decorator for registering tool dependencies
def depends_on_files(file_patterns: List[str]):
    """
    Decorator for registering tool dependencies on file patterns.

    Args:
        file_patterns: List of file patterns the tool depends on

    Returns:
        Decorated function
    """
    def decorator(func):
        # Get the tool name from the function name
        tool_name = func.__name__

        # Register the dependency
        invalidation_manager = get_invalidation_manager()
        invalidation_manager.register_tool_dependency(tool_name, file_patterns)

        # Return the original function unchanged
        return func

    return decorator
