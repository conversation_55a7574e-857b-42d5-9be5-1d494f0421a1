import * as vscode from 'vscode';
import { SettingsManager } from './settingsManager';

export enum AuthType {
    ApiKey = 'apiKey',
    Bearer = 'bearer',
    Basic = 'basic'
}

export interface AuthConfig {
    enabled: boolean;
    type: AuthType;
    apiKey?: string;
    bearerToken?: string;
    username?: string;
    password?: string;
}

export class AuthenticationManager {
    private static _instance: AuthenticationManager;
    private _authConfig: AuthConfig;
    private _isAuthenticated: boolean = false;
    
    private constructor() {
        this._authConfig = this.loadAuthConfig();
        
        // Listen for configuration changes
        vscode.workspace.onDidChangeConfiguration(e => {
            if (e.affectsConfiguration('ragAssistant')) {
                this._authConfig = this.loadAuthConfig();
            }
        });
    }
    
    public static getInstance(): AuthenticationManager {
        if (!AuthenticationManager._instance) {
            AuthenticationManager._instance = new AuthenticationManager();
        }
        return AuthenticationManager._instance;
    }
    
    private loadAuthConfig(): AuthConfig {
        const settings = SettingsManager.getSettings();
        return {
            enabled: settings.authEnabled || false,
            type: settings.authType as AuthType || AuthType.ApiKey,
            apiKey: settings.apiKey,
            bearerToken: settings.bearerToken,
            username: settings.username,
            password: settings.password
        };
    }
    
    public get isAuthenticated(): boolean {
        return this._isAuthenticated;
    }
    
    public get authConfig(): AuthConfig {
        return this._authConfig;
    }
    
    public async authenticate(): Promise<boolean> {
        if (!this._authConfig.enabled) {
            this._isAuthenticated = true;
            return true;
        }
        
        // Check if we have the necessary credentials
        switch (this._authConfig.type) {
            case AuthType.ApiKey:
                if (!this._authConfig.apiKey) {
                    const apiKey = await this.promptForCredential('API Key');
                    if (!apiKey) {
                        return false;
                    }
                    await SettingsManager.updateSetting('apiKey', apiKey);
                    this._authConfig.apiKey = apiKey;
                }
                break;
                
            case AuthType.Bearer:
                if (!this._authConfig.bearerToken) {
                    const token = await this.promptForCredential('Bearer Token');
                    if (!token) {
                        return false;
                    }
                    await SettingsManager.updateSetting('bearerToken', token);
                    this._authConfig.bearerToken = token;
                }
                break;
                
            case AuthType.Basic:
                if (!this._authConfig.username || !this._authConfig.password) {
                    const username = await this.promptForCredential('Username');
                    if (!username) {
                        return false;
                    }
                    
                    const password = await this.promptForCredential('Password', true);
                    if (!password) {
                        return false;
                    }
                    
                    await SettingsManager.updateSetting('username', username);
                    await SettingsManager.updateSetting('password', password);
                    
                    this._authConfig.username = username;
                    this._authConfig.password = password;
                }
                break;
        }
        
        // At this point, we have the necessary credentials
        this._isAuthenticated = true;
        return true;
    }
    
    public async logout(): Promise<void> {
        this._isAuthenticated = false;
        
        // Clear sensitive credentials
        switch (this._authConfig.type) {
            case AuthType.ApiKey:
                await SettingsManager.updateSetting('apiKey', '');
                this._authConfig.apiKey = '';
                break;
                
            case AuthType.Bearer:
                await SettingsManager.updateSetting('bearerToken', '');
                this._authConfig.bearerToken = '';
                break;
                
            case AuthType.Basic:
                await SettingsManager.updateSetting('username', '');
                await SettingsManager.updateSetting('password', '');
                this._authConfig.username = '';
                this._authConfig.password = '';
                break;
        }
    }
    
    public getAuthHeaders(): Record<string, string> {
        if (!this._authConfig.enabled || !this._isAuthenticated) {
            return {};
        }
        
        switch (this._authConfig.type) {
            case AuthType.ApiKey:
                return { 'X-API-Key': this._authConfig.apiKey || '' };
                
            case AuthType.Bearer:
                return { 'Authorization': `Bearer ${this._authConfig.bearerToken || ''}` };
                
            case AuthType.Basic:
                const credentials = Buffer.from(`${this._authConfig.username || ''}:${this._authConfig.password || ''}`).toString('base64');
                return { 'Authorization': `Basic ${credentials}` };
                
            default:
                return {};
        }
    }
    
    private async promptForCredential(credentialName: string, isPassword: boolean = false): Promise<string | undefined> {
        return vscode.window.showInputBox({
            prompt: `Enter your ${credentialName}`,
            placeHolder: credentialName,
            password: isPassword,
            ignoreFocusOut: true
        });
    }
}
