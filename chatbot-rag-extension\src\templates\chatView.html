<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG Assistant</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            padding: 0;
            margin: 0;
        }
        .chat-container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            position: relative;
        }
        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .message {
            padding: 10px;
            border-radius: 8px;
            word-wrap: break-word;
        }
        .user-message {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            margin-left: auto;
            max-width: 85%;
        }
        .assistant-message {
            background-color: var(--vscode-editor-background);
            border: 1px solid var(--vscode-editor-lineHighlightBorder);
            margin-right: auto;
            width: 100%;
            box-sizing: border-box;
        }
        .timestamp {
            font-size: 0.8em;
            color: var(--vscode-descriptionForeground);
            margin-top: 4px;
        }
        .input-container {
            display: flex;
            gap: 8px;
            padding: 10px;
            background: var(--vscode-editor-background);
            border-top: 1px solid var(--vscode-editor-lineHighlightBorder);
        }
        #questionInput {
            flex: 1;
            padding: 8px;
            border: 1px solid var(--vscode-input-border);
            background: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            border-radius: 4px;
            resize: vertical;
            min-height: 38px;
            max-height: 200px;
            overflow-y: auto;
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            line-height: 1.5;
        }
        #sendButton {
            padding: 8px 16px;
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s, transform 0.1s;
        }
        #sendButton:active, #sendButton.button-clicked {
            background-color: var(--vscode-button-hoverBackground);
            transform: scale(0.95);
        }
        #sendButton:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .clear-button {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 4px 8px;
            background: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s, transform 0.1s;
        }
        .clear-button:active, .clear-button.button-clicked {
            background-color: var(--vscode-button-secondaryHoverBackground);
            transform: scale(0.95);
        }
        .file-edit-container {
            margin-top: 8px;
            padding: 8px;
            background-color: var(--vscode-editor-inactiveSelectionBackground);
            border-radius: 4px;
        }
        .file-edit-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }
        .file-name {
            font-weight: bold;
            font-size: 0.9em;
        }
        .edit-actions {
            display: flex;
            gap: 4px;
        }
        .edit-button {
            padding: 2px 6px;
            font-size: 0.8em;
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 2px;
            cursor: pointer;
        }
        .edit-status {
            font-size: 0.8em;
            font-style: italic;
        }
        .history-button {
            position: absolute;
            top: 10px;
            right: 80px;
            padding: 4px 8px;
            background: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s, transform 0.1s;
        }
        .history-button:active, .history-button.button-clicked {
            background-color: var(--vscode-button-secondaryHoverBackground);
            transform: scale(0.95);
        }
        .history-panel {
            position: absolute;
            top: 40px;
            right: 10px;
            width: 250px;
            max-height: 300px;
            overflow-y: auto;
            background: var(--vscode-editor-background);
            border: 1px solid var(--vscode-editor-lineHighlightBorder);
            border-radius: 4px;
            display: none;
            z-index: 10;
        }
        .history-item {
            padding: 8px;
            border-bottom: 1px solid var(--vscode-editor-lineHighlightBorder);
            cursor: pointer;
        }
        .history-item:hover {
            background-color: var(--vscode-list-hoverBackground);
        }
        .history-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            border-bottom: 1px solid var(--vscode-panel-border);
        }

        .history-header-title {
            margin: 0;
            font-size: 14px;
            font-weight: 600;
        }

        .new-chat-button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 4px 8px;
            border-radius: 2px;
            cursor: pointer;
            font-size: 12px;
        }

        .new-chat-button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }

        .history-separator {
            height: 1px;
            background-color: var(--vscode-panel-border);
            margin: 4px 0;
        }

        .empty-history {
            padding: 16px;
            text-align: center;
            color: var(--vscode-descriptionForeground);
            font-style: italic;
        }

        .history-item-title {
            font-weight: bold;
            margin-bottom: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .history-button-container {
            display: flex;
            gap: 4px;
        }

        .history-rename-button,
        .history-delete-button {
            background: none;
            border: none;
            cursor: pointer;
            font-size: 12px;
            padding: 2px 4px;
            opacity: 0.7;
            transition: opacity 0.2s;
        }

        .history-rename-button:hover,
        .history-delete-button:hover {
            opacity: 1;
        }

        .history-item-meta {
            display: flex;
            justify-content: space-between;
            font-size: 0.8em;
            color: var(--vscode-descriptionForeground);
        }

        .message-count {
            font-style: italic;
        }

        .active-session {
            background-color: var(--vscode-list-activeSelectionBackground);
            color: var(--vscode-list-activeSelectionForeground);
        }

        .title-edit-input {
            width: 100%;
            max-width: calc(100% - 60px); /* Leave space for buttons */
            padding: 2px 4px;
            border: 1px solid var(--vscode-input-border);
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            font-size: 12px;
            margin-right: 8px;
        }

        /* Toast notifications */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            animation: fadeIn 0.3s, fadeOut 0.3s 2.7s;
        }

        .success-toast {
            background-color: var(--vscode-notificationsSuccessBackground, #4CAF50);
            color: var(--vscode-notificationsSuccessForeground, white);
        }

        .error-toast {
            background-color: var(--vscode-notificationsErrorBackground, #F44336);
            color: var(--vscode-notificationsErrorForeground, white);
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes fadeOut {
            from { opacity: 1; transform: translateY(0); }
            to { opacity: 0; transform: translateY(-20px); }
        }
        .history-item-date {
            font-size: 0.8em;
            color: var(--vscode-descriptionForeground);
        }
        .markdown-content pre {
            background-color: var(--vscode-textCodeBlock-background);
            padding: 8px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .markdown-content code {
            font-family: var(--vscode-editor-font-family);
            font-size: 0.9em;
        }
        .loading-message {
            background-color: var(--vscode-editor-inactiveSelectionBackground);
            font-style: italic;
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0% { opacity: 0.6; }
            50% { opacity: 1; }
            100% { opacity: 0.6; }
        }

        /* Special formatting elements */
        .think-block {
            background-color: var(--vscode-editor-lineHighlightBackground);
            border-left: 3px solid var(--vscode-editor-selectionBackground);
            padding: 4px 8px;
            margin: 6px 0;
            font-style: italic;
            color: var(--vscode-descriptionForeground);
            border-radius: 4px;
            overflow: hidden;
        }

        .think-header {
            display: flex;
            align-items: center;
            margin-bottom: 0;
            padding: 2px 0;
            cursor: pointer;
        }

        .think-toggle {
            background: none;
            border: none;
            color: var(--vscode-foreground);
            cursor: pointer;
            padding: 0;
            margin-right: 4px;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 16px;
            height: 16px;
            line-height: 1;
        }

        .think-emoji {
            margin-right: 4px;
        }

        .think-content {
            margin-left: 24px; /* Aligns with the content after the toggle button and emoji */
            transition: max-height 0.3s ease-out;
            padding-top: 2px;
            padding-bottom: 2px;
        }

        .think-block.collapsed .think-content {
            display: none;
        }

        .think-block.collapsed {
            padding-bottom: 4px;
        }

        .horizontal-line {
            border: none;
            height: 1px;
            background-color: var(--vscode-editorWidget-border);
            margin: 12px 0;
        }

        /* Ensure newlines are preserved */
        .preserve-whitespace {
            white-space: pre-wrap;
        }

        /* Bold text styling */
        .bold-text {
            font-weight: bold;
            color: var(--vscode-editor-foreground);
        }

        /* Inline code styling */
        .inline-code {
            font-family: var(--vscode-editor-font-family);
            background-color: rgba(100, 180, 255, 0.1);
            color: #4B9EF0;
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 0.9em;
            white-space: nowrap;
        }

        /* Code block styling */
        .code-block {
            margin: 10px 0;
            border-radius: 6px;
            overflow: hidden;
            border: 1px solid var(--vscode-editor-lineHighlightBorder);
            background-color: var(--vscode-editor-background);
        }

        .code-block-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 6px 10px;
            background-color: var(--vscode-editorGroupHeader-tabsBackground);
            border-bottom: 1px solid var(--vscode-editor-lineHighlightBorder);
            font-family: var(--vscode-editor-font-family);
            font-size: 12px;
        }

        .code-block-title {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .code-block-collapse {
            background: none;
            border: none;
            color: var(--vscode-foreground);
            cursor: pointer;
            padding: 0;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
        }

        .code-block-filename {
            font-weight: bold;
            color: var(--vscode-foreground);
            cursor: pointer;
        }

        .code-block-path {
            color: var(--vscode-descriptionForeground);
            font-style: italic;
            margin-left: 4px;
        }

        .code-block-stats {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .code-block-added {
            color: #4CAF50;
        }

        .code-block-removed {
            color: #F44336;
        }

        .code-block-actions {
            display: flex;
            gap: 8px;
        }

        .code-block-action-button {
            background: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 3px;
            padding: 2px 6px;
            font-size: 11px;
            cursor: pointer;
        }

        .code-block-action-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .code-block-content {
            max-height: 300px;
            overflow: auto;
            position: relative;
        }

        .code-block-content::-webkit-scrollbar {
            width: 8px;
            height: 8px;
            opacity: 0;
        }

        .code-block-content:hover::-webkit-scrollbar {
            opacity: 1;
        }

        .code-block-content::-webkit-scrollbar-track {
            background: transparent;
        }

        .code-block-content::-webkit-scrollbar-thumb {
            background: var(--vscode-scrollbarSlider-background);
            border-radius: 4px;
        }

        .code-block-content::-webkit-scrollbar-thumb:hover {
            background: var(--vscode-scrollbarSlider-hoverBackground);
        }

        .code-block-content pre {
            margin: 0;
            padding: 10px;
            font-family: var(--vscode-editor-font-family);
            font-size: 13px;
            line-height: 1.5;
            tab-size: 4;
        }

        .code-block-content code {
            font-family: inherit;
        }

        .code-block-resize-handle {
            height: 5px;
            background-color: var(--vscode-editorGroupHeader-tabsBackground);
            cursor: ns-resize;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .code-block-resize-handle::after {
            content: '≡';
            color: var(--vscode-descriptionForeground);
            font-size: 10px;
            transform: rotate(90deg);
        }

        /* Line highlighting */
        .line-added {
            background-color: rgba(80, 200, 120, 0.2);
        }

        .line-removed {
            background-color: rgba(255, 100, 100, 0.2);
            text-decoration: line-through;
        }

        .line-modified {
            background-color: rgba(255, 220, 100, 0.2);
        }

        /* Code token styling */
        .code-token {
            color: var(--vscode-textLink-foreground);
            text-decoration: underline;
            cursor: pointer;
        }

        .code-token:hover {
            color: var(--vscode-textLink-activeForeground);
        }

        /* Terminal styling */
        .terminal-block {
            background-color: var(--vscode-terminal-background, #1e1e1e);
            color: var(--vscode-terminal-foreground, #cccccc);
        }

        .terminal-block .code-block-header {
            background-color: var(--vscode-terminal-border, #333333);
        }

        .terminal-block pre {
            color: inherit;
        }
    </style>
    <!-- These will be replaced with actual URIs in the TypeScript code -->
    <link href="${styleUri}" rel="stylesheet">
    <link href="${codeBlocksStyleUri}" rel="stylesheet">
    <link href="${codeTokensStyleUri}" rel="stylesheet">
    <script src="${scriptUri}"></script>
    <script src="${codeBlocksScriptUri}"></script>
    <script src="${codeTokensScriptUri}"></script>
    ${markdownRendererScript}

    <!-- Code token functions and styles are loaded from external files -->

    <!-- Prism.js for syntax highlighting -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism-tomorrow.min.css" rel="stylesheet" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/plugins/autoloader/prism-autoloader.min.js"></script>
</head>
<body>
    <div class="chat-container">
        <button class="clear-button" id="clearButton">Clear Chat</button>
        <button class="history-button" id="historyButton">History</button>
        <div class="history-panel" id="historyPanel"></div>
        <div class="messages" id="messages"></div>
        <div class="input-container">
            <textarea id="questionInput"
                   placeholder="Type your question... (Shift+Enter for new line)"
                   autocomplete="off"
                   rows="1"></textarea>
            <button id="sendButton">Send</button>
        </div>
    </div>
    <script>
        const vscode = acquireVsCodeApi();
        const messagesDiv = document.getElementById('messages');
        const questionInput = document.getElementById('questionInput');
        const sendButton = document.getElementById('sendButton');
        const clearButton = document.getElementById('clearButton');
        const historyButton = document.getElementById('historyButton');
        const historyPanel = document.getElementById('historyPanel');

        // Initialize state
        let state = vscode.getState() || {
            messages: [],
            chatHistory: [],
            settings: {
                markdownSupport: ${markdownEnabled},
                syntaxHighlighting: ${syntaxHighlightingEnabled}
            }
        };

        console.log('Initial state:', state);

        // Restore messages from state
        state.messages.forEach(msg => {
            addMessageToUI(msg.content, msg.isUser, msg.timestamp, msg.id, msg.fileEdits);
        });

        function formatTimestamp(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleTimeString();
        }

        // Helper function to process special tags in content
        function processSpecialTags(content) {
            if (!content) return '';

            // Process <think> tags
            content = processThinkTags(content);

            // Process horizontal lines (---)
            content = processHorizontalLines(content);

            // Process bold text (**text**)
            content = processBoldText(content);

            // Process inline code (`code`)
            content = processInlineCode(content);

            // Process code blocks and tokens using the external script
            if (window.codeBlockFunctions) {
                content = window.codeBlockFunctions.processCodeBlocks(content);
            }

            // Process code tokens using the token handler
            if (window.codeTokenFunctions && typeof window.codeTokenFunctions.processCodeTokens === 'function') {
                content = window.codeTokenFunctions.processCodeTokens(content);
            }

            return content;
        }

        // Process <think>...</think> tags
        function processThinkTags(content) {
            // Regular expression to match <think>...</think> tags
            // Using non-greedy matching with .*? to handle nested tags properly
            const thinkRegex = /<think>(.*?)<\/think>/gs;

            // Generate a unique ID for each think block
            let thinkBlockCounter = 0;

            // Replace <think>...</think> tags with collapsible styled divs
            return content.replace(thinkRegex, (match, thinkContent) => {
                const thinkId = `think-block-${Date.now()}-${thinkBlockCounter++}`;
                // Trim whitespace from the content to reduce unnecessary gaps
                const trimmedContent = thinkContent.trim();
                return `<div class="think-block" id="${thinkId}"><div class="think-header" onclick="toggleThinkBlock('${thinkId}')"><button class="think-toggle">-</button><span class="think-emoji">💭</span><span>Thinking...</span></div><div class="think-content">${trimmedContent}</div></div>`;
            });
        }

        // Function to toggle the visibility of think blocks
        function toggleThinkBlock(thinkId) {
            const thinkBlock = document.getElementById(thinkId);
            if (thinkBlock) {
                thinkBlock.classList.toggle('collapsed');
                const toggleButton = thinkBlock.querySelector('.think-toggle');
                if (toggleButton) {
                    toggleButton.textContent = thinkBlock.classList.contains('collapsed') ? '+' : '-';
                }
            }
        }

        // Process horizontal lines (---)
        function processHorizontalLines(content) {
            // Regular expression to match three or more hyphens on a line
            const hrRegex = /^(\s*)---+\s*$/gm;

            // Replace --- with horizontal line elements
            return content.replace(hrRegex, '<hr class="horizontal-line">');
        }

        // Process bold text (**text**)
        function processBoldText(content) {
            // Regular expression to match text between ** symbols
            // Using non-greedy matching with .*? to handle nested formatting properly
            // Negative lookahead to avoid matching inside code blocks
            const boldRegex = /\*\*(.*?)\*\*/g;

            // Replace **text** with bold elements
            return content.replace(boldRegex, (match, boldText) => {
                return `<strong class="bold-text">${boldText}</strong>`;
            });
        }

        // Process inline code (`code`)
        function processInlineCode(content) {
            // Regular expression to match text between backticks
            // Using non-greedy matching with .*? to handle nested formatting properly
            // Avoid matching triple backticks (code blocks)
            const codeRegex = /(?<!`)`([^`]+)`(?!`)/g;

            // Replace `code` with inline code elements
            return content.replace(codeRegex, (match, codeText) => {
                return `<code class="inline-code">${codeText}</code>`;
            });
        }


        function formatDate(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
        }

        function addMessageToUI(content, isUser, timestamp = Date.now(), id, fileEdits) {
            const messageDiv = document.createElement('div');
            messageDiv.classList.add('message');
            messageDiv.classList.add(isUser ? 'user-message' : 'assistant-message');
            messageDiv.id = id || 'msg-' + Date.now();

            const textDiv = document.createElement('div');

            // Process special tags for assistant messages
            if (!isUser) {
                // Add preserve-whitespace class to ensure newlines are displayed correctly
                textDiv.classList.add('preserve-whitespace');

                // Process special tags like <think> and horizontal lines
                content = processSpecialTags(content);
            }

            // Use markdown rendering for assistant messages if enabled
            if (!isUser && typeof renderMarkdown === 'function' && ${markdownEnabled}) {
                textDiv.classList.add('markdown-content');
                // Use innerHTML instead of renderMarkdown for content with HTML tags
                if (content.includes('<div class="think-block">') ||
                    content.includes('<hr class="horizontal-line">') ||
                    content.includes('<div id="code-block-') ||
                    content.includes('<span class="code-token"')) {
                    textDiv.innerHTML = content;
                } else {
                    renderMarkdown(content, textDiv);
                }
            } else {
                // For user messages or when markdown is disabled
                if (!isUser && (
                    content.includes('<div class="think-block">') ||
                    content.includes('<hr class="horizontal-line">') ||
                    content.includes('<div id="code-block-') ||
                    content.includes('<span class="code-token"')
                )) {
                    textDiv.innerHTML = content;
                } else {
                    textDiv.textContent = content;
                }
            }

            messageDiv.appendChild(textDiv);

            const timeDiv = document.createElement('div');
            timeDiv.classList.add('timestamp');
            timeDiv.textContent = formatTimestamp(timestamp);
            messageDiv.appendChild(timeDiv);

            // Apply syntax highlighting to code blocks
            setTimeout(() => {
                if (window.codeBlockFunctions && typeof window.codeBlockFunctions.highlightAll === 'function') {
                    window.codeBlockFunctions.highlightAll();
                } else if (typeof Prism !== 'undefined') {
                    Prism.highlightAllUnder(messageDiv);
                }
            }, 0);

            // Add file edit UI if applicable
            if (fileEdits && fileEdits.length > 0 && !isUser) {
                fileEdits.forEach(edit => {
                    const fileEditContainer = document.createElement('div');
                    fileEditContainer.classList.add('file-edit-container');

                    const fileEditHeader = document.createElement('div');
                    fileEditHeader.classList.add('file-edit-header');

                    const fileName = document.createElement('div');
                    fileName.classList.add('file-name');
                    fileName.textContent = edit.fileName.split('/').pop() || edit.fileName.split('\\').pop() || edit.fileName;
                    fileEditHeader.appendChild(fileName);

                    const editActions = document.createElement('div');
                    editActions.classList.add('edit-actions');

                    if (!edit.applied) {
                        const applyButton = document.createElement('button');
                        applyButton.classList.add('edit-button');
                        applyButton.textContent = 'Apply Edit';
                        applyButton.addEventListener('click', () => {
                            vscode.postMessage({
                                type: 'applyEdit',
                                messageId: messageDiv.id,
                                fileName: edit.fileName
                            });
                        });
                        editActions.appendChild(applyButton);
                    } else {
                        const statusSpan = document.createElement('span');
                        statusSpan.classList.add('edit-status');
                        statusSpan.textContent = 'Applied';
                        editActions.appendChild(statusSpan);
                    }

                    const viewButton = document.createElement('button');
                    viewButton.classList.add('edit-button');
                    viewButton.textContent = 'View File';
                    viewButton.addEventListener('click', () => {
                        vscode.postMessage({
                            type: 'viewFile',
                            fileName: edit.fileName
                        });
                    });
                    editActions.appendChild(viewButton);

                    fileEditHeader.appendChild(editActions);
                    fileEditContainer.appendChild(fileEditHeader);

                    messageDiv.appendChild(fileEditContainer);
                });
            }

            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function sendMessage() {
            console.log('sendMessage function called');
            const question = questionInput.value.trim();
            console.log('Question value:', question);

            if (question) {
                console.log('Question is not empty, proceeding...');
                try {
                    // Create the message object
                    const messageObj = {
                        type: 'askQuestion',
                        question: question
                    };
                    console.log('Message object created:', messageObj);

                    // Post the message to the extension
                    console.log('Posting message to extension...');
                    vscode.postMessage(messageObj);
                    console.log('Message posted successfully');

                    // We'll let the extension add the message to the UI
                    console.log('Waiting for extension to add message to UI...');

                    // Add a loading indicator
                    const loadingDiv = document.createElement('div');
                    loadingDiv.id = 'loading-indicator';
                    loadingDiv.classList.add('message', 'assistant-message', 'loading-message');
                    loadingDiv.textContent = 'Processing your request...';
                    messagesDiv.appendChild(loadingDiv);
                    messagesDiv.scrollTop = messagesDiv.scrollHeight;

                    // Clear the input field and disable the send button
                    questionInput.value = '';
                    sendButton.disabled = true;
                    console.log('Input cleared and send button disabled');
                } catch (error) {
                    console.error('Error sending message:', error);
                    // Show error in UI
                    const errorDiv = document.createElement('div');
                    errorDiv.classList.add('message', 'assistant-message', 'error-message');
                    errorDiv.style.backgroundColor = '#ffdddd';
                    errorDiv.style.color = '#ff0000';
                    errorDiv.textContent = 'Error sending message: ' + (error instanceof Error ? error.message : 'Unknown error');
                    messagesDiv.appendChild(errorDiv);
                    messagesDiv.scrollTop = messagesDiv.scrollHeight;
                }
            } else {
                console.log('Question is empty, not sending');
            }
        }

        function updateHistoryPanel() {
            historyPanel.innerHTML = '';
            if (state.chatHistory && state.chatHistory.length > 0) {
                // Create a header with a new chat button
                const headerDiv = document.createElement('div');
                headerDiv.classList.add('history-header');

                const headerTitle = document.createElement('h3');
                headerTitle.textContent = 'Chat Sessions';
                headerTitle.classList.add('history-header-title');
                headerDiv.appendChild(headerTitle);

                const newChatButton = document.createElement('button');
                newChatButton.classList.add('new-chat-button');
                newChatButton.textContent = '+ New Chat';
                newChatButton.addEventListener('click', (e) => {
                    e.stopPropagation();
                    vscode.postMessage({ type: 'newChatSession' });
                    historyPanel.style.display = 'none';
                });
                headerDiv.appendChild(newChatButton);

                historyPanel.appendChild(headerDiv);

                // Add a separator
                const separator = document.createElement('div');
                separator.classList.add('history-separator');
                historyPanel.appendChild(separator);

                // Create a list of chat sessions
                state.chatHistory.forEach((historyItem, index) => {
                    const itemDiv = document.createElement('div');
                    itemDiv.classList.add('history-item');

                    // Add active indicator if this is the active session
                    if (historyItem.isActive) {
                        itemDiv.classList.add('active-session');
                    }

                    const titleDiv = document.createElement('div');
                    titleDiv.classList.add('history-item-title');

                    // Use custom title if available, otherwise use truncated question
                    titleDiv.textContent = historyItem.title ||
                        (historyItem.question.substring(0, 30) +
                        (historyItem.question.length > 30 ? '...' : ''));

                    // Add a rename button
                    const renameButton = document.createElement('button');
                    renameButton.classList.add('history-rename-button');
                    renameButton.textContent = '✏️';
                    renameButton.title = 'Rename this chat session';
                    renameButton.addEventListener('click', (e) => {
                        e.stopPropagation(); // Prevent triggering the parent click event

                        // Create an inline edit field
                        const currentTitle = historyItem.title ||
                            (historyItem.question.substring(0, 30) +
                            (historyItem.question.length > 30 ? '...' : ''));

                        // Hide the title text
                        const titleText = titleDiv.firstChild;
                        titleText.style.display = 'none';

                        // Create input field
                        const inputField = document.createElement('input');
                        inputField.type = 'text';
                        inputField.value = currentTitle;
                        inputField.classList.add('title-edit-input');

                        // Insert before the button container
                        titleDiv.insertBefore(inputField, buttonContainer);

                        // Focus the input field
                        inputField.focus();
                        inputField.select();

                        // Handle input field events
                        inputField.addEventListener('keydown', (event) => {
                            if (event.key === 'Enter') {
                                // Save the new title
                                const newTitle = inputField.value.trim();
                                if (newTitle) {
                                    // Send message to extension to rename this history item
                                    vscode.postMessage({
                                        type: 'renameChatHistory',
                                        index: index,
                                        newTitle: newTitle
                                    });
                                }

                                // Remove input field and show title
                                inputField.remove();
                                titleText.style.display = '';
                            } else if (event.key === 'Escape') {
                                // Cancel editing
                                inputField.remove();
                                titleText.style.display = '';
                            }
                        });

                        // Handle blur event (clicking outside)
                        inputField.addEventListener('blur', () => {
                            // Save the new title
                            const newTitle = inputField.value.trim();
                            if (newTitle && newTitle !== currentTitle) {
                                // Send message to extension to rename this history item
                                vscode.postMessage({
                                    type: 'renameChatHistory',
                                    index: index,
                                    newTitle: newTitle
                                });
                            }

                            // Remove input field and show title
                            inputField.remove();
                            titleText.style.display = '';
                        });
                    });

                    // Add a delete button
                    const deleteButton = document.createElement('button');
                    deleteButton.classList.add('history-delete-button');
                    deleteButton.textContent = '🗑️';
                    deleteButton.title = 'Delete this chat session';
                    deleteButton.addEventListener('click', (e) => {
                        e.stopPropagation(); // Prevent triggering the parent click event

                        // Confirm deletion
                        if (confirm('Are you sure you want to delete this chat session?')) {
                            // Send message to extension to delete this history item
                            vscode.postMessage({
                                type: 'deleteChatSession',
                                index: index
                            });
                        }
                    });

                    // Add buttons to title div
                    const buttonContainer = document.createElement('div');
                    buttonContainer.classList.add('history-button-container');
                    buttonContainer.appendChild(renameButton);
                    buttonContainer.appendChild(deleteButton);
                    titleDiv.appendChild(buttonContainer);

                    itemDiv.appendChild(titleDiv);

                    // Add message count and date
                    const metaDiv = document.createElement('div');
                    metaDiv.classList.add('history-item-meta');

                    const messageCountSpan = document.createElement('span');
                    messageCountSpan.classList.add('message-count');
                    // Use the message count from the session if available, otherwise show a default
                    const count = historyItem.messageCount || 2;
                    messageCountSpan.textContent = count === 1 ? '1 message' : `${count} messages`;
                    metaDiv.appendChild(messageCountSpan);

                    const dateDiv = document.createElement('span');
                    dateDiv.classList.add('history-item-date');
                    dateDiv.textContent = formatDate(historyItem.timestamp);
                    metaDiv.appendChild(dateDiv);

                    itemDiv.appendChild(metaDiv);

                    // Add click handler to load the session
                    itemDiv.addEventListener('click', () => {
                        vscode.postMessage({
                            type: 'loadHistoryItem',
                            index: index
                        });
                        historyPanel.style.display = 'none';
                    });

                    historyPanel.appendChild(itemDiv);
                });
            } else {
                // Create a header with a new chat button
                const headerDiv = document.createElement('div');
                headerDiv.classList.add('history-header');

                const headerTitle = document.createElement('h3');
                headerTitle.textContent = 'Chat Sessions';
                headerTitle.classList.add('history-header-title');
                headerDiv.appendChild(headerTitle);

                const newChatButton = document.createElement('button');
                newChatButton.classList.add('new-chat-button');
                newChatButton.textContent = '+ New Chat';
                newChatButton.addEventListener('click', (e) => {
                    e.stopPropagation();
                    vscode.postMessage({ type: 'newChatSession' });
                    historyPanel.style.display = 'none';
                });
                headerDiv.appendChild(newChatButton);

                historyPanel.appendChild(headerDiv);

                // Add a separator
                const separator = document.createElement('div');
                separator.classList.add('history-separator');
                historyPanel.appendChild(separator);

                // Show empty state
                const emptyDiv = document.createElement('div');
                emptyDiv.textContent = 'No chat sessions available';
                emptyDiv.classList.add('empty-history');
                historyPanel.appendChild(emptyDiv);
            }
        }

        // Function to add visual feedback for button clicks
        function addButtonClickEffect(button) {
            button.classList.add('button-clicked');
            console.log('Button clicked: ' + (button.id || button.className));

            // Remove the effect after a short delay
            setTimeout(function() {
                button.classList.remove('button-clicked');
            }, 300);
        }

        // Handle sending messages
        sendButton.addEventListener('click', (e) => {
            addButtonClickEffect(sendButton);
            console.log('Send button clicked, calling sendMessage()');
            sendMessage();
        });

        questionInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                console.log('Enter key pressed, calling sendMessage()');
                sendMessage();
            }
        });

        // Clear chat history
        clearButton.addEventListener('click', () => {
            addButtonClickEffect(clearButton);
            console.log('Clear button clicked');
            vscode.postMessage({ type: 'clearChat' });
            messagesDiv.innerHTML = '';
            state.messages = [];
            vscode.setState(state);
        });

        // Toggle history panel
        historyButton.addEventListener('click', () => {
            addButtonClickEffect(historyButton);
            console.log('History button clicked');
            if (historyPanel.style.display === 'block') {
                historyPanel.style.display = 'none';
            } else {
                updateHistoryPanel();
                historyPanel.style.display = 'block';
            }
        });

        // Close history panel when clicking outside
        document.addEventListener('click', (e) => {
            if (e.target !== historyButton && !historyPanel.contains(e.target) &&
                historyPanel.style.display === 'block') {
                historyPanel.style.display = 'none';
            }
        });

        // Handle incoming messages
        window.addEventListener('message', event => {
            try {
                console.log('Received message from extension:', event.data);
                const message = event.data;

                // Debug message handling
                if (message.type === 'addMessage') {
                    console.log('Adding message to UI: ' + (message.isUser ? 'User' : 'Assistant') + ' - ' + message.message.substring(0, 30) + '...');
                }

                switch (message.type) {
                case 'addMessage':
                    // Remove loading indicator if it exists
                    const loadingIndicator = document.getElementById('loading-indicator');
                    if (loadingIndicator) {
                        loadingIndicator.remove();
                    }

                    addMessageToUI(message.message, message.isUser, message.timestamp, message.id, message.fileEdits);
                    state.messages.push({
                        content: message.message,
                        isUser: message.isUser,
                        timestamp: message.timestamp,
                        id: message.id,
                        fileEdits: message.fileEdits
                    });
                    vscode.setState(state);
                    sendButton.disabled = false;
                    break;
                case 'updateMessage':
                    console.log('Updating message: ' + message.id + ' with content: ' + message.content.substring(0, 30) + '...');
                    const messageToUpdate = document.getElementById(message.id);
                    if (messageToUpdate) {
                        // Find the text content div (first child)
                        const textDiv = messageToUpdate.querySelector('div:first-child');
                        if (textDiv) {
                            // Process special tags for assistant messages
                            let processedContent = message.content;

                            // Add preserve-whitespace class to ensure newlines are displayed correctly
                            textDiv.classList.add('preserve-whitespace');

                            // Process special tags like <think> and horizontal lines
                            processedContent = processSpecialTags(processedContent);

                            // Update the content based on whether markdown is enabled
                            if (typeof renderMarkdown === 'function' && ${markdownEnabled} === 'true') {
                                // Use innerHTML instead of renderMarkdown for content with HTML tags
                                if (processedContent.includes('<div class="think-block">') ||
                                    processedContent.includes('<hr class="horizontal-line">') ||
                                    processedContent.includes('<div id="code-block-') ||
                                    processedContent.includes('<span class="code-token"')) {
                                    textDiv.innerHTML = processedContent;
                                } else {
                                    textDiv.innerHTML = ''; // Clear existing content
                                    renderMarkdown(processedContent, textDiv);
                                }
                            } else {
                                // When markdown is disabled
                                if (processedContent.includes('<div class="think-block">') ||
                                    processedContent.includes('<hr class="horizontal-line">') ||
                                    processedContent.includes('<div id="code-block-') ||
                                    processedContent.includes('<span class="code-token"')) {
                                    textDiv.innerHTML = processedContent;
                                } else {
                                    textDiv.textContent = processedContent;
                                }
                            }
                        }

                        // Apply syntax highlighting to code blocks
                        setTimeout(() => {
                            if (window.codeBlockFunctions && typeof window.codeBlockFunctions.highlightAll === 'function') {
                                window.codeBlockFunctions.highlightAll();
                            } else if (typeof Prism !== 'undefined') {
                                Prism.highlightAllUnder(messageToUpdate);
                            }
                        }, 0);

                        // Update the message in state
                        const stateMessageIndex = state.messages.findIndex(msg => msg.id === message.id);
                        if (stateMessageIndex !== -1) {
                            state.messages[stateMessageIndex].content = message.content;
                            vscode.setState(state);
                        }
                    }
                    break;

                case 'clearMessages':
                    // Clear only the messages, not the chat history
                    messagesDiv.innerHTML = '';
                    state.messages = [];
                    vscode.setState(state);
                    break;

                case 'updateChatHistory':
                    state.chatHistory = message.history;
                    vscode.setState(state);
                    // If the history panel is open, update it
                    if (historyPanel.style.display === 'block') {
                        updateHistoryPanel();
                    }
                    break;

                case 'sessionDeleted':
                    if (message.success) {
                        // Show success message
                        const successToast = document.createElement('div');
                        successToast.classList.add('toast', 'success-toast');
                        successToast.textContent = 'Chat session deleted successfully';
                        document.body.appendChild(successToast);

                        // Remove after 3 seconds
                        setTimeout(() => {
                            successToast.remove();
                        }, 3000);

                        // If the history panel is open, update it
                        if (historyPanel.style.display === 'block') {
                            updateHistoryPanel();
                        }
                    } else {
                        // Show error message
                        const errorToast = document.createElement('div');
                        errorToast.classList.add('toast', 'error-toast');
                        errorToast.textContent = 'Error deleting chat session: ' + (message.error || 'Unknown error');
                        document.body.appendChild(errorToast);

                        // Remove after 5 seconds
                        setTimeout(() => {
                            errorToast.remove();
                        }, 5000);
                    }
                    break;
                case 'updateFileEdit':
                    const msgElement = document.getElementById(message.messageId);
                    if (msgElement) {
                        const fileEditContainers = msgElement.querySelectorAll('.file-edit-container');
                        fileEditContainers.forEach(container => {
                            const fileNameElement = container.querySelector('.file-name');
                            const fileBaseName = message.fileName.split('/').pop() || message.fileName.split('\\').pop() || message.fileName;
                            if (fileNameElement && fileNameElement.textContent === fileBaseName) {
                                const editActions = container.querySelector('.edit-actions');
                                editActions.innerHTML = '';

                                const statusSpan = document.createElement('span');
                                statusSpan.classList.add('edit-status');
                                statusSpan.textContent = 'Applied';
                                editActions.appendChild(statusSpan);

                                const viewButton = document.createElement('button');
                                viewButton.classList.add('edit-button');
                                viewButton.textContent = 'View File';
                                viewButton.addEventListener('click', () => {
                                    vscode.postMessage({
                                        type: 'viewFile',
                                        fileName: message.fileName
                                    });
                                });
                                editActions.appendChild(viewButton);
                            }
                        });

                        // Update state
                        state.messages.forEach(msg => {
                            if (msg.id === message.messageId && msg.fileEdits) {
                                msg.fileEdits.forEach(edit => {
                                    if (edit.fileName === message.fileName) {
                                        edit.applied = true;
                                    }
                                });
                            }
                        });
                        vscode.setState(state);
                    }
                    break;
                case 'updateCodeBlock':
                    if (message.blockId && message.action) {
                        if (message.action === 'applied') {
                            // Call the updateAfterApply function in codeBlockFunctions
                            if (window.codeBlockFunctions && typeof window.codeBlockFunctions.updateAfterApply === 'function') {
                                window.codeBlockFunctions.updateAfterApply(message.blockId);
                            }
                        } else if (message.action === 'undone') {
                            // Call the updateAfterUndo function in codeBlockFunctions
                            if (window.codeBlockFunctions && typeof window.codeBlockFunctions.updateAfterUndo === 'function') {
                                window.codeBlockFunctions.updateAfterUndo(message.blockId);
                            }
                        }
                    }
                    break;

                case 'updateSettings':
                    state.settings = message.settings;
                    vscode.setState(state);
                    // Refresh all messages to apply new settings
                    if (message.settings.markdownSupport !== state.settings.markdownSupport) {
                        messagesDiv.innerHTML = '';
                        state.messages.forEach(msg => {
                            addMessageToUI(msg.content, msg.isUser, msg.timestamp, msg.id, msg.fileEdits);
                        });
                    }
                    break;
                case 'error':
                    console.error('Error from extension:', message.message);

                    // Remove loading indicator if it exists
                    const errorLoadingIndicator = document.getElementById('loading-indicator');
                    if (errorLoadingIndicator) {
                        errorLoadingIndicator.remove();
                    }

                    // Display error message to user
                    const errorDiv = document.createElement('div');
                    errorDiv.classList.add('message', 'assistant-message', 'error-message');
                    errorDiv.style.backgroundColor = '#ffdddd';
                    errorDiv.style.color = '#ff0000';
                    errorDiv.textContent = 'Error: ' + message.message;
                    messagesDiv.appendChild(errorDiv);
                    messagesDiv.scrollTop = messagesDiv.scrollHeight;
                    // Re-enable the send button
                    sendButton.disabled = false;
                    break;
                }
            } catch (error) {
                console.error('Error handling message from extension:', error);

                // Remove loading indicator if it exists
                const catchLoadingIndicator = document.getElementById('loading-indicator');
                if (catchLoadingIndicator) {
                    catchLoadingIndicator.remove();
                }

                // Display error to user
                const errorDiv = document.createElement('div');
                errorDiv.classList.add('message', 'assistant-message', 'error-message');
                errorDiv.style.backgroundColor = '#ffdddd';
                errorDiv.style.color = '#ff0000';
                errorDiv.textContent = 'Error processing message: ' + (error instanceof Error ? error.message : 'Unknown error');
                messagesDiv.appendChild(errorDiv);
                messagesDiv.scrollTop = messagesDiv.scrollHeight;
                // Re-enable the send button
                sendButton.disabled = false;
            }
        });

        // Enable/disable send button based on input
        questionInput.addEventListener('input', () => {
            sendButton.disabled = !questionInput.value.trim();

            // Auto-resize the textarea based on content
            questionInput.style.height = 'auto';
            questionInput.style.height = Math.min(200, questionInput.scrollHeight) + 'px';
        });

        // Handle Shift+Enter for new lines
        questionInput.addEventListener('keydown', (event) => {
            if (event.key === 'Enter') {
                if (event.shiftKey) {
                    // Manually insert a new line when Shift+Enter is pressed
                    event.preventDefault(); // Prevent default behavior

                    // Get cursor position
                    const start = questionInput.selectionStart;
                    const end = questionInput.selectionEnd;

                    // Insert newline at cursor position
                    const value = questionInput.value;
                    questionInput.value = value.substring(0, start) + '\n' + value.substring(end);

                    // Move cursor after the inserted newline
                    questionInput.selectionStart = questionInput.selectionEnd = start + 1;

                    // Trigger the input event to resize the textarea
                    questionInput.dispatchEvent(new Event('input'));
                } else {
                    // Submit on Enter without Shift
                    event.preventDefault();
                    if (!sendButton.disabled) {
                        sendButton.click();
                    }
                }
            }
        });
    </script>
</body>
</html>
