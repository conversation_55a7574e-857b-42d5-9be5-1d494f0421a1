import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { FileManager } from './fileManager';
import { CodeAnalyzer } from './codeAnalyzer';

/**
 * Represents a code token with additional context
 */
export interface CodeToken {
    name: string;
    type: string;
    description?: string;
    filePath?: string;
    position?: vscode.Position;
    references?: { filePath: string; position: vscode.Position }[];
}

/**
 * Handles code token functionality for the RAG Assistant
 */
export class CodeTokenHandler {
    private static instance: CodeTokenHandler;
    private fileManager: FileManager;
    private codeAnalyzer: CodeAnalyzer;
    private tokenCache: Map<string, CodeToken>;
    private tokenHistory: string[];
    private maxHistoryItems: number = 10;

    private constructor() {
        this.fileManager = FileManager.getInstance();
        this.codeAnalyzer = CodeAnalyzer.getInstance();
        this.tokenCache = new Map<string, CodeToken>();
        this.tokenHistory = [];
    }

    /**
     * Get the singleton instance of CodeTokenHandler
     */
    public static getInstance(): CodeTokenHandler {
        if (!CodeTokenHandler.instance) {
            CodeTokenHandler.instance = new CodeTokenHandler();
        }
        return CodeTokenHandler.instance;
    }

    /**
     * Process code tokens in content
     * @param content The content to process
     * @returns The processed content with enhanced code tokens
     */
    public processCodeTokens(content: string): string {
        if (!content) return '';

        // Regular expressions for different token formats
        // Format 1: `TokenName` - Basic token
        // Format 2: `TokenName:TokenType` - Token with type
        // Format 3: `TokenName:TokenType:FilePath` - Token with type and file path
        const tokenRegex = /`([^:`]+)(?::([^:`]+))?(?::([^:`]+))?`/g;

        // Replace tokens with clickable spans
        return content.replace(tokenRegex, (match, name, type, filePath) => {
            // Create a unique ID for this token
            const tokenId = `token-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

            // Store token information in cache
            this.cacheToken(name, type || 'unknown', filePath);

            // Create HTML for the token
            return this.createTokenHTML(tokenId, name, type, filePath);
        });
    }

    /**
     * Create HTML for a code token
     * @param tokenId Unique ID for the token
     * @param name Token name
     * @param type Token type (optional)
     * @param filePath File path (optional)
     * @returns HTML string for the token
     */
    private createTokenHTML(tokenId: string, name: string, type?: string, filePath?: string): string {
        // Create data attributes for the token
        const dataAttributes = `
            data-token-name="${name}"
            ${type ? `data-token-type="${type}"` : ''}
            ${filePath ? `data-token-file="${filePath}"` : ''}
        `;

        // Create the HTML
        return `<span id="${tokenId}" class="code-token ${type ? `token-type-${type.toLowerCase()}` : ''}" ${dataAttributes} onclick="window.codeTokenFunctions.findCodeToken('${name}', '${filePath || ''}', '${type || ''}', '${tokenId}')"><code>${name}${type ? `:${type}` : ''}${filePath ? `:${filePath}` : ''}</code></span>`;
    }

    /**
     * Cache a token for future reference
     * @param name Token name
     * @param type Token type
     * @param filePath File path (optional)
     */
    private cacheToken(name: string, type: string, filePath?: string): void {
        // Check if token is already in cache
        if (!this.tokenCache.has(name)) {
            this.tokenCache.set(name, {
                name,
                type,
                filePath
            });
        }
    }

    /**
     * Find a code token in the workspace
     * @param tokenName The token name to find
     * @param filePath Optional file path to search in
     * @param tokenType Optional token type
     * @param tokenId Optional token ID for UI updates
     * @returns A promise that resolves to the token information
     */
    public async findCodeToken(
        tokenName: string,
        filePath?: string,
        tokenType?: string,
        tokenId?: string
    ): Promise<CodeToken | undefined> {
        try {
            // Add to token history
            this.addToHistory(tokenName);

            // If a specific file path is provided, search only in that file
            if (filePath) {
                const absolutePath = this.fileManager.getAbsolutePath(filePath);
                if (absolutePath && this.fileManager.fileExists(absolutePath)) {
                    const position = await this.codeAnalyzer.findCodeEntity(absolutePath, tokenName);

                    if (position) {
                        // Update token cache
                        const token: CodeToken = {
                            name: tokenName,
                            type: tokenType || 'unknown',
                            filePath: absolutePath,
                            position
                        };
                        this.tokenCache.set(tokenName, token);

                        // Highlight the token in the file
                        await this.fileManager.highlightLine(absolutePath, position);

                        // Return the token information
                        return token;
                    }
                }
            }

            // If no file path is provided or the token wasn't found in the specified file,
            // search in the active editor first
            const editor = vscode.window.activeTextEditor;
            if (editor) {
                const position = await this.codeAnalyzer.findCodeEntity(editor.document.fileName, tokenName);

                if (position) {
                    // Update token cache
                    const token: CodeToken = {
                        name: tokenName,
                        type: tokenType || 'unknown',
                        filePath: editor.document.fileName,
                        position
                    };
                    this.tokenCache.set(tokenName, token);

                    // Highlight the token in the file
                    await this.fileManager.highlightLine(editor.document.fileName, position);

                    // Return the token information
                    return token;
                }
            }

            // If the token wasn't found in the active editor, search in the workspace
            const token = await this.searchWorkspace(tokenName, tokenType);

            if (token) {
                // Highlight the token in the file
                if (token.filePath && token.position) {
                    await this.fileManager.highlightLine(token.filePath, token.position);
                }

                // Return the token information
                return token;
            }

            // If the token wasn't found, show a warning
            vscode.window.showWarningMessage(`Token '${tokenName}' not found in the workspace`);
            return undefined;
        } catch (error) {
            vscode.window.showErrorMessage(`Error finding token: ${error}`);
            console.error(error);
            return undefined;
        }
    }

    /**
     * Search for a token in the workspace
     * @param tokenName The token name to search for
     * @param tokenType Optional token type to filter by
     * @returns A promise that resolves to the token information
     */
    private async searchWorkspace(tokenName: string, tokenType?: string): Promise<CodeToken | undefined> {
        try {
            // Get all files in the workspace
            const workspaceRoot = this.fileManager.getWorkspaceRoot();
            if (!workspaceRoot) {
                return undefined;
            }

            // Use VS Code's built-in search API to find the token
            const results = await vscode.workspace.findFiles('**/*.*', '**/node_modules/**');

            // Filter results based on file type if a token type is provided
            let filesToSearch = results;
            if (tokenType) {
                const fileExtensions = this.getFileExtensionsForTokenType(tokenType);
                if (fileExtensions.length > 0) {
                    filesToSearch = results.filter(file => {
                        const ext = file.fsPath.split('.').pop()?.toLowerCase();
                        return ext && fileExtensions.includes(ext);
                    });
                }
            }

            // Search for the token in each file
            for (const file of filesToSearch) {
                try {
                    const position = await this.codeAnalyzer.findCodeEntity(file.fsPath, tokenName);

                    if (position) {
                        // Update token cache
                        const token: CodeToken = {
                            name: tokenName,
                            type: tokenType || 'unknown',
                            filePath: file.fsPath,
                            position
                        };
                        this.tokenCache.set(tokenName, token);

                        // Open the file
                        await this.fileManager.openFile(file.fsPath);

                        // Return the token information
                        return token;
                    }
                } catch (error) {
                    // Skip files that can't be opened or searched
                    console.error(`Error searching file ${file.fsPath}: ${error}`);
                }
            }

            return undefined;
        } catch (error) {
            console.error(`Error searching workspace: ${error}`);
            return undefined;
        }
    }

    /**
     * Get file extensions for a token type
     * @param tokenType The token type
     * @returns An array of file extensions
     */
    private getFileExtensionsForTokenType(tokenType: string): string[] {
        // Map token types to file extensions
        const typeToExtensions: { [key: string]: string[] } = {
            'class': ['ts', 'js', 'java', 'py', 'cs', 'cpp', 'php'],
            'function': ['ts', 'js', 'java', 'py', 'cs', 'cpp', 'php'],
            'variable': ['ts', 'js', 'java', 'py', 'cs', 'cpp', 'php'],
            'interface': ['ts', 'java', 'cs', 'cpp'],
            'type': ['ts', 'cs', 'cpp'],
            'enum': ['ts', 'java', 'cs', 'cpp'],
            'component': ['tsx', 'jsx', 'vue', 'svelte'],
            'module': ['ts', 'js', 'py'],
            'import': ['ts', 'js', 'py', 'java'],
            'css': ['css', 'scss', 'less'],
            'html': ['html', 'htm', 'xml'],
            'json': ['json'],
            'markdown': ['md', 'markdown'],
            'sql': ['sql'],
            'yaml': ['yml', 'yaml'],
            'docker': ['dockerfile', 'dockerignore'],
            'git': ['gitignore', 'gitattributes']
        };

        // Return extensions for the token type or an empty array if not found
        return typeToExtensions[tokenType.toLowerCase()] || [];
    }

    /**
     * Add a token to the history
     * @param tokenName The token name to add
     */
    private addToHistory(tokenName: string): void {
        // Remove the token if it's already in the history
        this.tokenHistory = this.tokenHistory.filter(t => t !== tokenName);

        // Add the token to the beginning of the history
        this.tokenHistory.unshift(tokenName);

        // Limit the history size
        if (this.tokenHistory.length > this.maxHistoryItems) {
            this.tokenHistory.pop();
        }
    }

    /**
     * Get the token history
     * @returns The token history
     */
    public getTokenHistory(): string[] {
        return [...this.tokenHistory];
    }

    /**
     * Get a token from the cache
     * @param tokenName The token name
     * @returns The token information or undefined if not found
     */
    public getToken(tokenName: string): CodeToken | undefined {
        return this.tokenCache.get(tokenName);
    }

    /**
     * Get all tokens from the cache
     * @returns An array of tokens
     */
    public getAllTokens(): CodeToken[] {
        return Array.from(this.tokenCache.values());
    }

    /**
     * Clear the token cache
     */
    public clearCache(): void {
        this.tokenCache.clear();
    }

    /**
     * Get JavaScript functions for code token functionality
     * @param extensionPath The path to the extension
     * @returns JavaScript code as a string
     */
    public static getCodeTokenFunctions(extensionPath: string): string {
        try {
            const filePath = path.join(extensionPath, 'media', 'codeTokens.js');
            return fs.readFileSync(filePath, 'utf8');
        } catch (error) {
            console.error(`Error reading code token functions file: ${error}`);
            return '// Error loading code token functions';
        }
    }

    /**
     * Get CSS styles for code tokens
     * @param extensionPath The path to the extension
     * @returns CSS code as a string
     */
    public static getCodeTokenStyles(extensionPath: string): string {
        try {
            const filePath = path.join(extensionPath, 'media', 'codeTokens.css');
            return fs.readFileSync(filePath, 'utf8');
        } catch (error) {
            console.error(`Error reading code token styles file: ${error}`);
            return '/* Error loading code token styles */';
        }
    }
}
