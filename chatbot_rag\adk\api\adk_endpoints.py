"""
API endpoints for the ADK integration.
"""

import logging
import async<PERSON>
import json
from typing import Dict, Any, List, Optional

from fastapi import APIRouter, HTTPException, WebSocket, WebSocketDisconnect, Depends
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

from chatbot_rag.adk.agents.main_agent import CodeAssistantAgent
from chatbot_rag.adk.utils.adk_utils import format_agent_response
from chatbot_rag.adk.utils.config_utils import get_available_models, get_default_model_info

# Configure logging
logger = logging.getLogger("adk_endpoints")

# Create router
router = APIRouter(prefix="/adk", tags=["adk"])

# Initialize default agent (will use default model)
default_agent = CodeAssistantAgent()

# Define request and response models
class SubagentModelConfig(BaseModel):
    provider: Optional[str] = None
    name: Optional[str] = None

class AgentRequest(BaseModel):
    query: str
    context: Optional[Dict[str, Any]] = None
    agent_name: Optional[str] = None
    model_provider: Optional[str] = None
    model_name: Optional[str] = None
    subagent_models: Optional[Dict[str, SubagentModelConfig]] = None

class CodebaseRequest(BaseModel):
    query: str
    context: Optional[Dict[str, Any]] = None
    model_provider: Optional[str] = None
    model_name: Optional[str] = None
    subagent_models: Optional[Dict[str, SubagentModelConfig]] = None

class AgentResponse(BaseModel):
    answer: str
    thinking: Optional[str] = None
    tool_calls: Optional[List[Dict[str, Any]]] = None
    suggestedEdits: Optional[List[Dict[str, Any]]] = None
    session_id: Optional[str] = None

class CodebaseResponse(BaseModel):
    answer: str
    thinking: Optional[str] = None
    tool_calls: Optional[List[Dict[str, Any]]] = None
    codebase_context: Optional[Dict[str, Any]] = None
    session_id: Optional[str] = None

@router.post("/agent/run", response_model=AgentResponse)
async def run_agent(request: AgentRequest):
    """
    Run the code assistant agent.
    """
    try:
        logger.info(f"=== AGENT REQUEST ===")
        logger.info(f" - Query: {request.query}")

        # Check if a specific model or subagent models are requested
        agent = default_agent
        if request.model_provider or request.model_name or request.subagent_models:
            logger.info(f" - Using custom model: Provider={request.model_provider}, Name={request.model_name}")

            # Convert subagent models to dictionary format
            subagent_models = None
            if request.subagent_models:
                subagent_models = {
                    agent_type: {"provider": config.provider, "name": config.name}
                    for agent_type, config in request.subagent_models.items()
                }
                logger.info(f" - Using custom subagent models: {subagent_models}")

            agent = CodeAssistantAgent(
                model_provider=request.model_provider,
                model_name=request.model_name,
                subagent_models=subagent_models
            )

        # Process the query
        response = await agent.process_query(request.query, request.context)

        # Format the response
        formatted_response = format_agent_response(response)

        return formatted_response
    except Exception as e:
        logger.error(f"Error running agent: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error running agent: {str(e)}")

@router.post("/codebase/understand", response_model=CodebaseResponse)
async def understand_codebase(request: CodebaseRequest):
    """
    Use the Codebase Understanding Agent to analyze the codebase structure.
    """
    try:
        logger.info(f"=== CODEBASE UNDERSTANDING REQUEST ===")
        logger.info(f" - Query: {request.query}")

        # Check if a specific model or subagent models are requested
        agent = default_agent
        if request.model_provider or request.model_name or request.subagent_models:
            logger.info(f" - Using custom model: Provider={request.model_provider}, Name={request.model_name}")

            # Convert subagent models to dictionary format
            subagent_models = None
            if request.subagent_models:
                subagent_models = {
                    agent_type: {"provider": config.provider, "name": config.name}
                    for agent_type, config in request.subagent_models.items()
                }
                logger.info(f" - Using custom subagent models: {subagent_models}")

            agent = CodeAssistantAgent(
                model_provider=request.model_provider,
                model_name=request.model_name,
                subagent_models=subagent_models
            )

        # Process the query with the codebase understanding agent
        response = await agent.understand_codebase(request.query, request.context)

        # Format the response
        formatted_response = {
            "answer": response.get("response", ""),
            "thinking": response.get("thinking", ""),
            "tool_calls": response.get("tool_calls", []),
            "codebase_context": response.get("codebase_context", {}),
            "session_id": response.get("session_id", "")
        }

        return formatted_response
    except Exception as e:
        logger.error(f"Error understanding codebase: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error understanding codebase: {str(e)}")

@router.websocket("/agent/ws")
async def agent_websocket(websocket: WebSocket):
    """
    WebSocket endpoint for interactive agent communication.
    """
    await websocket.accept()
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_json()

            # Extract query, context, and model info
            query = data.get("query", "")
            context = data.get("context", {})
            model_provider = data.get("model_provider", None)
            model_name = data.get("model_name", None)
            subagent_models_data = data.get("subagent_models", None)

            try:
                # Check if a specific model or subagent models are requested
                agent = default_agent
                if model_provider or model_name or subagent_models_data:
                    logger.info(f"WebSocket using custom model: Provider={model_provider}, Name={model_name}")

                    # Convert subagent models to dictionary format
                    subagent_models = None
                    if subagent_models_data:
                        subagent_models = {
                            agent_type: {"provider": config.get("provider"), "name": config.get("name")}
                            for agent_type, config in subagent_models_data.items()
                        }
                        logger.info(f"WebSocket using custom subagent models: {subagent_models}")

                    agent = CodeAssistantAgent(
                        model_provider=model_provider,
                        model_name=model_name,
                        subagent_models=subagent_models
                    )

                # Process with agent
                response = await agent.process_query(query, context)

                # Format the response
                formatted_response = format_agent_response(response)

                # Send result back to client
                await websocket.send_json({
                    "type": "response",
                    "data": formatted_response
                })
            except Exception as e:
                # Send error back to client
                await websocket.send_json({
                    "type": "error",
                    "error": str(e)
                })
    except WebSocketDisconnect:
        logger.info("Client disconnected from agent websocket")

@router.get("/agent/stream")
async def stream_agent_response(
    query: str,
    model_provider: Optional[str] = None,
    model_name: Optional[str] = None,
    subagent_models: Optional[str] = None  # JSON string of subagent models
):
    """
    Stream the agent response.
    """
    async def generate_stream():
        try:
            # Check if a specific model or subagent models are requested
            agent = default_agent
            if model_provider or model_name or subagent_models:
                logger.info(f"Stream using custom model: Provider={model_provider}, Name={model_name}")

                # Parse subagent models from JSON string if provided
                parsed_subagent_models = None
                if subagent_models:
                    try:
                        subagent_models_data = json.loads(subagent_models)
                        parsed_subagent_models = {
                            agent_type: {"provider": config.get("provider"), "name": config.get("name")}
                            for agent_type, config in subagent_models_data.items()
                        }
                        logger.info(f"Stream using custom subagent models: {parsed_subagent_models}")
                    except json.JSONDecodeError as e:
                        logger.error(f"Error parsing subagent models JSON: {e}")

                agent = CodeAssistantAgent(
                    model_provider=model_provider,
                    model_name=model_name,
                    subagent_models=parsed_subagent_models
                )

            # Create a session for this query
            session = agent.agent.create_session()

            # Send the message and get the response
            response_future = session.send_message(query)

            # Start with a start event
            yield f'data: {{"type":"start"}}\n\n'

            # Wait for the response
            response = await response_future

            # Stream the thinking
            if response.thinking:
                thinking_chunks = response.thinking.split('\n')
                for chunk in thinking_chunks:
                    if chunk.strip():
                        escaped_chunk = chunk.replace('\\', '\\\\').replace('"', '\\"').replace('\n', '\\n')
                        yield f'data: {{"type":"thinking","content":"{escaped_chunk}"}}\n\n'
                        await asyncio.sleep(0.05)

            # Stream the response text
            text_chunks = response.text.split('\n')
            for chunk in text_chunks:
                escaped_chunk = chunk.replace('\\', '\\\\').replace('"', '\\"').replace('\n', '\\n')
                yield f'data: {{"type":"token","content":"{escaped_chunk}\\n"}}\n\n'
                await asyncio.sleep(0.05)

            # Stream tool calls if any
            if response.tool_calls:
                for tool_call in response.tool_calls:
                    tool_call_json = json.dumps({
                        "name": tool_call.name,
                        "args": tool_call.args
                    })
                    yield f'data: {{"type":"tool_call","content":{tool_call_json}}}\n\n'

            # End the stream
            yield f'data: {{"type":"end"}}\n\n'

        except Exception as e:
            logger.error(f"Error in streaming response: {str(e)}")
            escaped_error = str(e).replace('"', '\\"')
            yield f'data: {{"type":"error","content":"Error generating response: {escaped_error}"}}\n\n'

    return StreamingResponse(
        generate_stream(),
        media_type="text/event-stream"
    )

@router.get("/models")
async def list_models(provider: Optional[str] = None):
    """
    List available models, optionally filtered by provider.
    """
    try:
        models = get_available_models(provider)
        default_info = get_default_model_info()

        return {
            "models": models,
            "default": default_info,
            "count": len(models)
        }
    except Exception as e:
        logger.error(f"Error listing models: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error listing models: {str(e)}")

@router.get("/health")
async def health_check():
    """
    Health check endpoint.
    """
    from chatbot_rag.adk.utils.config_utils import get_default_model_info

    default_info = get_default_model_info()

    return {
        "status": "ok",
        "agent": "code_assistant_agent",
        "model": default_info.get("model_name", "unknown"),
        "provider": default_info.get("provider", "unknown"),
        "supported_providers": ["google", "anthropic", "ollama", "openai"]
    }
