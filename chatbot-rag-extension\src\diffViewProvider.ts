import * as vscode from 'vscode';
import * as path from 'path';

export interface FileDiff {
    fileName: string;
    originalContent: string;
    newContent: string;
    edits: {
        start: { line: number; character: number };
        end: { line: number; character: number };
        text: string;
    }[];
}

export class DiffViewProvider {
    /**
     * Show a diff view between original content and new content
     * @param fileDiff The file diff information
     * @returns A promise that resolves when the user accepts or rejects the changes
     */
    public static async showDiff(fileDiff: FileDiff): Promise<boolean> {
        // Create temporary files for the diff
        const fileName = path.basename(fileDiff.fileName);
        const originalUri = vscode.Uri.parse(`untitled:${fileName}.original`);
        const newUri = vscode.Uri.parse(`untitled:${fileName}.new`);
        
        // Create the documents
        const originalDoc = await vscode.workspace.openTextDocument(originalUri);
        const newDoc = await vscode.workspace.openTextDocument(newUri);
        
        // Edit the documents
        const originalEdit = new vscode.WorkspaceEdit();
        originalEdit.insert(originalUri, new vscode.Position(0, 0), fileDiff.originalContent);
        await vscode.workspace.applyEdit(originalEdit);
        
        const newEdit = new vscode.WorkspaceEdit();
        newEdit.insert(newUri, new vscode.Position(0, 0), fileDiff.newContent);
        await vscode.workspace.applyEdit(newEdit);
        
        // Show the diff
        const title = `Diff for ${fileName}`;
        await vscode.commands.executeCommand('vscode.diff', originalUri, newUri, title);
        
        // Ask the user if they want to apply the changes
        const result = await vscode.window.showInformationMessage(
            'Do you want to apply these changes?',
            { modal: true },
            'Apply',
            'Cancel'
        );
        
        return result === 'Apply';
    }
    
    /**
     * Apply edits to a document to generate new content
     * @param originalContent The original content
     * @param edits The edits to apply
     * @returns The new content with edits applied
     */
    public static applyEditsToContent(originalContent: string, edits: FileDiff['edits']): string {
        // Convert string to lines
        const lines = originalContent.split(/\r?\n/);
        
        // Sort edits in reverse order (to avoid position changes)
        const sortedEdits = [...edits].sort((a, b) => {
            if (a.start.line !== b.start.line) {
                return b.start.line - a.start.line;
            }
            return b.start.character - a.start.character;
        });
        
        // Apply each edit
        for (const edit of sortedEdits) {
            // Get the affected lines
            const startLine = edit.start.line;
            const endLine = edit.end.line;
            
            // Handle single line edit
            if (startLine === endLine) {
                const line = lines[startLine];
                lines[startLine] = 
                    line.substring(0, edit.start.character) + 
                    edit.text + 
                    line.substring(edit.end.character);
            } else {
                // Handle multi-line edit
                const startLineText = lines[startLine];
                const endLineText = lines[endLine];
                
                // Create the new text
                const newText = 
                    startLineText.substring(0, edit.start.character) + 
                    edit.text + 
                    endLineText.substring(edit.end.character);
                
                // Replace the lines
                lines.splice(startLine, endLine - startLine + 1, newText);
            }
        }
        
        return lines.join('\n');
    }
    
    /**
     * Generate a file diff from original content and edits
     * @param fileName The file name
     * @param originalContent The original content
     * @param edits The edits to apply
     * @returns A FileDiff object
     */
    public static generateFileDiff(
        fileName: string, 
        originalContent: string, 
        edits: FileDiff['edits']
    ): FileDiff {
        const newContent = this.applyEditsToContent(originalContent, edits);
        
        return {
            fileName,
            originalContent,
            newContent,
            edits
        };
    }
}
