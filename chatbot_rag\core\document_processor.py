from dataclasses import dataclass
from datetime import datetime
from typing import List, Dict, Optional
import os
import hashlib

@dataclass
class FileMetadata:
    path: str
    file_type: str
    extension: str
    relative_path: str
    created: float
    modified: float
    size: int
    content_hash: str
    language: Optional[str] = None
    
class DocumentProcessor:
    def __init__(self, root_path: str):
        self.root_path = root_path
        self.supported_formats = {
            'code': ['.py', '.js', '.ts', '.cpp', '.h', '.rs', '.go'],
            'documentation': ['.md', '.rst', '.txt'],
            'document': ['.pdf', '.doc', '.docx'],
            'presentation': ['.ppt', '.pptx'],
            'data': ['.json', '.yaml', '.csv', '.xlsx']
        }
        
    def _detect_language(self, file_path: str) -> Optional[str]:
        """Detect programming language based on file extension and content"""
        # Add language detection logic here
        pass
        
    def _calculate_file_hash(self, file_path: str) -> str:
        """Calculate SHA-256 hash of file content"""
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for byte_block in iter(lambda: f.read(4096), b""):
                sha256_hash.update(byte_block)
        return sha256_hash.hexdigest()
        
    def process_file(self, file_path: str) -> FileMetadata:
        """Process a single file and return its metadata"""
        ext = os.path.splitext(file_path)[1].lower()
        file_type = next((k for k, v in self.supported_formats.items() 
                         if ext in v), 'unknown')
                         
        return FileMetadata(
            path=file_path,
            file_type=file_type,
            extension=ext,
            relative_path=os.path.relpath(file_path, self.root_path),
            created=os.path.getctime(file_path),
            modified=os.path.getmtime(file_path),
            size=os.path.getsize(file_path),
            content_hash=self._calculate_file_hash(file_path),
            language=self._detect_language(file_path) if file_type == 'code' else None
        )