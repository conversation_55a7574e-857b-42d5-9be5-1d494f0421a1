"""
Tool result caching utility.

This module provides a caching mechanism for tool results to improve performance
by avoiding redundant tool calls.
"""

import logging
import json
import hashlib
import os
import time
from typing import Dict, Any, Optional, Tuple, List, Callable
from functools import wraps
import asyncio
import inspect

# Configure logging
logger = logging.getLogger("tool_cache")

class ToolCache:
    """
    Cache for tool results to improve performance.

    This class provides methods to:
    1. Cache tool results based on tool name and arguments
    2. Retrieve cached results
    3. Invalidate cache entries
    4. Persist cache to disk
    """

    def __init__(self, cache_dir: str = None, max_cache_size: int = 1000, ttl: int = 3600):
        """
        Initialize the tool cache.

        Args:
            cache_dir: Directory to store persistent cache. If None, cache is in-memory only.
            max_cache_size: Maximum number of entries to keep in the cache
            ttl: Time-to-live for cache entries in seconds (default: 1 hour)
        """
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.cache_dir = cache_dir
        self.max_cache_size = max_cache_size
        self.ttl = ttl

        # Create cache directory if it doesn't exist
        if self.cache_dir:
            os.makedirs(self.cache_dir, exist_ok=True)

            # Load cache from disk if it exists
            self._load_cache()

        logger.info(f"Tool cache initialized with max_size={max_cache_size}, ttl={ttl}s")

    def _generate_key(self, tool_name: str, args: Dict[str, Any]) -> str:
        """
        Generate a cache key based on tool name and arguments.

        Args:
            tool_name: Name of the tool
            args: Tool arguments

        Returns:
            A unique cache key
        """
        # Sort arguments to ensure consistent keys
        sorted_args = json.dumps(args, sort_keys=True)

        # Generate a hash of the tool name and arguments
        key = hashlib.md5(f"{tool_name}:{sorted_args}".encode()).hexdigest()

        return key

    def get(self, tool_name: str, args: Dict[str, Any]) -> Tuple[bool, Any]:
        """
        Get a cached result for a tool call.

        Args:
            tool_name: Name of the tool
            args: Tool arguments

        Returns:
            Tuple of (hit, result) where hit is True if the result was found in the cache
        """
        key = self._generate_key(tool_name, args)

        if key in self.cache:
            entry = self.cache[key]

            # Check if the entry has expired
            if time.time() - entry["timestamp"] > self.ttl:
                logger.debug(f"Cache entry expired for {tool_name}")
                del self.cache[key]
                return False, None

            logger.debug(f"Cache hit for {tool_name}")
            return True, entry["result"]

        logger.debug(f"Cache miss for {tool_name}")
        return False, None

    def set(self, tool_name: str, args: Dict[str, Any], result: Any) -> None:
        """
        Set a cached result for a tool call.

        Args:
            tool_name: Name of the tool
            args: Tool arguments
            result: Result to cache
        """
        key = self._generate_key(tool_name, args)

        # Add to cache
        self.cache[key] = {
            "tool_name": tool_name,
            "args": args,
            "result": result,
            "timestamp": time.time()
        }

        # Evict oldest entries if cache is too large
        if len(self.cache) > self.max_cache_size:
            self._evict_oldest_entries()

        # Persist cache to disk if enabled
        if self.cache_dir:
            self._save_cache()

        logger.debug(f"Cached result for {tool_name}")

    def invalidate(self, tool_name: Optional[str] = None) -> None:
        """
        Invalidate cache entries.

        Args:
            tool_name: Optional tool name to invalidate. If None, invalidates all entries.
        """
        if tool_name:
            # Invalidate entries for a specific tool
            keys_to_remove = [key for key, entry in self.cache.items() if entry["tool_name"] == tool_name]
            for key in keys_to_remove:
                del self.cache[key]
            logger.info(f"Invalidated {len(keys_to_remove)} cache entries for {tool_name}")
        else:
            # Invalidate all entries
            self.cache.clear()
            logger.info("Invalidated all cache entries")

        # Persist cache to disk if enabled
        if self.cache_dir:
            self._save_cache()

    def _evict_oldest_entries(self, count: int = 100) -> None:
        """
        Evict the oldest entries from the cache.

        Args:
            count: Number of entries to evict
        """
        # Sort entries by timestamp
        sorted_entries = sorted(self.cache.items(), key=lambda x: x[1]["timestamp"])

        # Evict oldest entries
        for key, _ in sorted_entries[:count]:
            del self.cache[key]

        logger.debug(f"Evicted {count} oldest cache entries")

    def _save_cache(self) -> None:
        """Save the cache to disk."""
        try:
            cache_file = os.path.join(self.cache_dir, "tool_cache.json")

            # Create a serializable version of the cache
            serializable_cache = {}
            for key, entry in self.cache.items():
                # Try to make the result JSON serializable
                try:
                    # Test if it's serializable
                    json.dumps(entry["result"])
                    serializable_cache[key] = entry
                except (TypeError, OverflowError):
                    # Skip entries that can't be serialized
                    logger.warning(f"Skipping non-serializable cache entry for {entry['tool_name']}")

            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(serializable_cache, f)

            logger.debug(f"Saved {len(serializable_cache)} cache entries to disk")
        except Exception as e:
            logger.error(f"Error saving cache to disk: {str(e)}")

    def _load_cache(self) -> None:
        """Load the cache from disk."""
        try:
            cache_file = os.path.join(self.cache_dir, "tool_cache.json")

            if os.path.exists(cache_file):
                with open(cache_file, 'r', encoding='utf-8') as f:
                    self.cache = json.load(f)

                # Remove expired entries
                current_time = time.time()
                keys_to_remove = [
                    key for key, entry in self.cache.items()
                    if current_time - entry["timestamp"] > self.ttl
                ]

                for key in keys_to_remove:
                    del self.cache[key]

                logger.info(f"Loaded {len(self.cache)} cache entries from disk (removed {len(keys_to_remove)} expired entries)")
            else:
                logger.info("No cache file found, starting with empty cache")
        except Exception as e:
            logger.error(f"Error loading cache from disk: {str(e)}")
            self.cache = {}

    def is_enabled(self) -> bool:
        """
        Check if caching is enabled.

        Returns:
            True if caching is enabled, False otherwise
        """
        # Check environment variable
        return os.environ.get("TOOL_CACHE_ENABLED", "1") == "1"

    def get_statistics(self) -> Dict[str, Any]:
        """
        Get cache statistics.

        Returns:
            Dictionary with cache statistics
        """
        # Count hits and misses by tool
        tool_stats = {}
        for key, entry in self.cache.items():
            tool_name = entry.get("tool_name", "unknown")
            if tool_name not in tool_stats:
                tool_stats[tool_name] = {
                    "count": 0,
                    "hits": 0,
                    "misses": 0
                }
            tool_stats[tool_name]["count"] += 1

        # Calculate hit and miss rates
        total_hits = sum(stats.get("hits", 0) for stats in tool_stats.values())
        total_misses = sum(stats.get("misses", 0) for stats in tool_stats.values())
        total_requests = total_hits + total_misses

        hit_rate = total_hits / total_requests if total_requests > 0 else 0
        miss_rate = total_misses / total_requests if total_requests > 0 else 0

        return {
            "size": len(self.cache),
            "hit_rate": hit_rate,
            "miss_rate": miss_rate,
            "tool_stats": tool_stats
        }

# Global cache instance
_global_cache: Optional[ToolCache] = None

def get_tool_cache() -> ToolCache:
    """
    Get the global tool cache instance.

    Returns:
        The global tool cache instance
    """
    global _global_cache

    if _global_cache is None:
        # Get cache directory from environment or use default
        cache_dir = os.environ.get("TOOL_CACHE_DIR", os.path.join(os.path.dirname(os.path.dirname(__file__)), "data", "cache"))

        # Get cache size from environment or use default
        max_cache_size = int(os.environ.get("TOOL_CACHE_SIZE", "1000"))

        # Get TTL from environment or use default
        ttl = int(os.environ.get("TOOL_CACHE_TTL", "3600"))

        _global_cache = ToolCache(cache_dir=cache_dir, max_cache_size=max_cache_size, ttl=ttl)

    return _global_cache

def cached_tool(ttl: Optional[int] = None):
    """
    Decorator for caching tool results.

    Args:
        ttl: Optional time-to-live for cache entries in seconds. If None, uses the cache default.

    Returns:
        Decorated function
    """
    def decorator(func):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # Get the tool name from the function name
            tool_name = func.__name__

            # Get the cache
            cache = get_tool_cache()

            # Check if we should skip caching
            skip_cache = kwargs.pop("skip_cache", False)

            if not skip_cache:
                # Try to get from cache
                hit, result = cache.get(tool_name, kwargs)
                if hit:
                    return result

            # Call the function
            result = await func(*args, **kwargs)

            # Cache the result
            if not skip_cache:
                cache.set(tool_name, kwargs, result)

            return result

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            # Get the tool name from the function name
            tool_name = func.__name__

            # Get the cache
            cache = get_tool_cache()

            # Check if we should skip caching
            skip_cache = kwargs.pop("skip_cache", False)

            if not skip_cache:
                # Try to get from cache
                hit, result = cache.get(tool_name, kwargs)
                if hit:
                    return result

            # Call the function
            result = func(*args, **kwargs)

            # Cache the result
            if not skip_cache:
                cache.set(tool_name, kwargs, result)

            return result

        # Check if the function is async
        if inspect.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper

    return decorator
