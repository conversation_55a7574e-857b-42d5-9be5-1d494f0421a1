"""
Refactoring Agent implementation using Google's Agent Development Kit.
"""

import logging
from typing import Dict, Any, Optional

from google.adk.agents import Agent
from google.adk.models import GeminiModel

from chatbot_rag.adk.tools.code_tools import refactor_code_tool
from chatbot_rag.adk.tools.rag_tools import search_code_tool

# Configure logging
logger = logging.getLogger("refactoring_agent")

class RefactoringAgent:
    """
    Agent specialized in code refactoring.
    """
    
    def __init__(self, model: Optional[GeminiModel] = None):
        """
        Initialize the Refactoring Agent.
        
        Args:
            model: Optional GeminiModel instance. If not provided, a default model will be used.
        """
        self.agent = Agent(
            name="refactoring_agent",
            model=model or "gemini-2.0-flash",
            description="I refactor code to improve readability, performance, or other goals.",
            instruction="""
            You are a code refactoring expert. Your job is to refactor code to improve:
            1. Readability and maintainability
            2. Performance and efficiency
            3. Error handling and robustness
            4. Adherence to best practices and design patterns
            
            Always explain your changes and why they improve the code.
            When you need more context about the codebase, use the search_code tool.
            """,
            tools=[refactor_code_tool, search_code_tool]
        )
        
        logger.info("Refactoring Agent initialized")
    
    async def refactor(self, code: str, language: str = "python", goal: str = "readability", context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Refactor the given code.
        
        Args:
            code: The code to refactor.
            language: The programming language of the code.
            goal: The refactoring goal.
            context: Optional additional context.
            
        Returns:
            A dictionary containing the refactored code and explanation.
        """
        try:
            # Create a session for this refactoring
            session = self.agent.create_session()
            
            # Prepare the input
            user_input = f"""
            Please refactor the following {language} code to improve {goal}:
            
            ```{language}
            {code}
            ```
            
            Provide the refactored code and explain your changes.
            """
            
            # Run the agent
            response = await session.send_message(user_input)
            
            logger.info(f"Code refactoring completed for {len(code)} characters of {language} code")
            
            # Format the response
            return {
                "refactored_code": response.text,
                "thinking": response.thinking,
                "tool_calls": response.tool_calls,
                "session_id": session.session_id
            }
            
        except Exception as e:
            logger.error(f"Error refactoring code: {str(e)}")
            return {
                "error": str(e),
                "refactored_code": f"Error refactoring code: {str(e)}"
            }
