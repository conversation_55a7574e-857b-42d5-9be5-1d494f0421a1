import * as vscode from 'vscode';
import axios from 'axios';
import { SettingsManager } from './settingsManager';

/**
 * Class for handling cache invalidation when files change
 */
export class CacheInvalidator {
    private static instance: CacheInvalidator;
    private fileWatcher: vscode.FileSystemWatcher | undefined;
    private debounceTimers: Map<string, NodeJS.Timeout> = new Map();
    private outputChannel: vscode.OutputChannel;
    private apiUrl: string;
    
    /**
     * Create a new CacheInvalidator
     * @param context VS Code extension context
     */
    private constructor(context: vscode.ExtensionContext) {
        this.outputChannel = vscode.window.createOutputChannel('RAG Assistant Cache');
        this.apiUrl = SettingsManager.getSettings().apiUrl;
        
        // Initialize file watcher
        this.initializeFileWatcher(context);
        
        this.outputChannel.appendLine('Cache invalidator initialized');
    }
    
    /**
     * Get the CacheInvalidator instance
     * @param context VS Code extension context
     * @returns The CacheInvalidator instance
     */
    public static getInstance(context: vscode.ExtensionContext): CacheInvalidator {
        if (!CacheInvalidator.instance) {
            CacheInvalidator.instance = new CacheInvalidator(context);
        }
        return CacheInvalidator.instance;
    }
    
    /**
     * Initialize the file watcher
     * @param context VS Code extension context
     */
    private initializeFileWatcher(context: vscode.ExtensionContext): void {
        // Create a file system watcher for all files
        this.fileWatcher = vscode.workspace.createFileSystemWatcher('**/*');
        
        // Watch for file changes
        this.fileWatcher.onDidChange(uri => this.handleFileChange(uri, 'change'));
        this.fileWatcher.onDidCreate(uri => this.handleFileChange(uri, 'create'));
        this.fileWatcher.onDidDelete(uri => this.handleFileChange(uri, 'delete'));
        
        // Register the file watcher with the extension context
        context.subscriptions.push(this.fileWatcher);
        
        this.outputChannel.appendLine('File watcher initialized');
    }
    
    /**
     * Handle file changes
     * @param uri URI of the changed file
     * @param changeType Type of change (change, create, delete)
     */
    private handleFileChange(uri: vscode.Uri, changeType: 'change' | 'create' | 'delete'): void {
        const filePath = uri.fsPath;
        
        // Skip node_modules, .git, and other non-code directories
        if (filePath.includes('node_modules') || 
            filePath.includes('.git') || 
            filePath.includes('__pycache__') ||
            filePath.includes('.vscode')) {
            return;
        }
        
        // Debounce the invalidation to avoid too many requests
        const key = `${filePath}-${changeType}`;
        if (this.debounceTimers.has(key)) {
            clearTimeout(this.debounceTimers.get(key));
        }
        
        this.debounceTimers.set(key, setTimeout(() => {
            this.invalidateCache(filePath, changeType);
            this.debounceTimers.delete(key);
        }, 1000)); // 1 second debounce
    }
    
    /**
     * Invalidate the cache for a file
     * @param filePath Path to the file
     * @param changeType Type of change (change, create, delete)
     */
    private async invalidateCache(filePath: string, changeType: 'change' | 'create' | 'delete'): Promise<void> {
        try {
            this.outputChannel.appendLine(`Invalidating cache for ${filePath} (${changeType})`);
            
            // Call the API to invalidate the cache
            const response = await axios.post(`${this.apiUrl}/adk/cache/invalidate`, {
                file_path: filePath,
                change_type: changeType
            });
            
            if (response.status === 200) {
                this.outputChannel.appendLine(`Cache invalidated successfully: ${JSON.stringify(response.data)}`);
            } else {
                this.outputChannel.appendLine(`Error invalidating cache: ${response.status} ${response.statusText}`);
            }
        } catch (error) {
            this.outputChannel.appendLine(`Error invalidating cache: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    
    /**
     * Invalidate the cache for a directory
     * @param dirPath Path to the directory
     */
    public async invalidateDirectoryCache(dirPath: string): Promise<void> {
        try {
            this.outputChannel.appendLine(`Invalidating cache for directory ${dirPath}`);
            
            // Call the API to invalidate the cache
            const response = await axios.post(`${this.apiUrl}/adk/cache/invalidate-directory`, {
                directory_path: dirPath
            });
            
            if (response.status === 200) {
                this.outputChannel.appendLine(`Directory cache invalidated successfully: ${JSON.stringify(response.data)}`);
            } else {
                this.outputChannel.appendLine(`Error invalidating directory cache: ${response.status} ${response.statusText}`);
            }
        } catch (error) {
            this.outputChannel.appendLine(`Error invalidating directory cache: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    
    /**
     * Invalidate the cache for a refactoring operation
     * @param filePaths Paths to the refactored files
     */
    public async invalidateRefactoringCache(filePaths: string[]): Promise<void> {
        try {
            this.outputChannel.appendLine(`Invalidating cache for refactoring of ${filePaths.length} files`);
            
            // Call the API to invalidate the cache
            const response = await axios.post(`${this.apiUrl}/adk/cache/invalidate-refactoring`, {
                file_paths: filePaths
            });
            
            if (response.status === 200) {
                this.outputChannel.appendLine(`Refactoring cache invalidated successfully: ${JSON.stringify(response.data)}`);
            } else {
                this.outputChannel.appendLine(`Error invalidating refactoring cache: ${response.status} ${response.statusText}`);
            }
        } catch (error) {
            this.outputChannel.appendLine(`Error invalidating refactoring cache: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
    
    /**
     * Invalidate all caches
     */
    public async invalidateAllCaches(): Promise<void> {
        try {
            this.outputChannel.appendLine('Invalidating all caches');
            
            // Call the API to invalidate all caches
            const response = await axios.post(`${this.apiUrl}/adk/cache/invalidate-all`);
            
            if (response.status === 200) {
                this.outputChannel.appendLine(`All caches invalidated successfully: ${JSON.stringify(response.data)}`);
            } else {
                this.outputChannel.appendLine(`Error invalidating all caches: ${response.status} ${response.statusText}`);
            }
        } catch (error) {
            this.outputChannel.appendLine(`Error invalidating all caches: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
}
