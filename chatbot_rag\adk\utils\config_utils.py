"""
Utilities for loading and managing configuration.
"""

import os
import json
import logging
from typing import Dict, Any, Optional, List

# Configure logging
logger = logging.getLogger("config_utils")

def get_models_config() -> Dict[str, Any]:
    """
    Load the models configuration from the JSON file.
    
    Returns:
        Dictionary containing the models configuration
    """
    try:
        config_path = os.path.join(
            os.path.dirname(os.path.dirname(__file__)), 
            "config", 
            "models.json"
        )
        
        if not os.path.exists(config_path):
            logger.warning(f"Models configuration file not found at {config_path}")
            return {
                "default": {"provider": "google", "model_name": "gemini-1.5-pro"},
                "providers": {}
            }
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        logger.info(f"Loaded models configuration with {len(config.get('providers', {}))} providers")
        return config
    
    except Exception as e:
        logger.error(f"Error loading models configuration: {str(e)}")
        return {
            "default": {"provider": "google", "model_name": "gemini-1.5-pro"},
            "providers": {}
        }

def get_available_models(provider: Optional[str] = None) -> List[Dict[str, Any]]:
    """
    Get a list of available models, optionally filtered by provider.
    
    Args:
        provider: Optional provider to filter by
        
    Returns:
        List of model information dictionaries
    """
    try:
        config = get_models_config()
        providers = config.get("providers", {})
        
        models = []
        
        # If provider is specified, only get models for that provider
        if provider:
            if provider not in providers:
                logger.warning(f"Provider {provider} not found in configuration")
                return []
            
            provider_config = providers[provider]
            for model_name, model_info in provider_config.get("models", {}).items():
                models.append({
                    "provider": provider,
                    "name": model_name,
                    "default": model_name == provider_config.get("default_model"),
                    **model_info
                })
        
        # Otherwise, get models for all providers
        else:
            for provider_name, provider_config in providers.items():
                for model_name, model_info in provider_config.get("models", {}).items():
                    models.append({
                        "provider": provider_name,
                        "name": model_name,
                        "default": model_name == provider_config.get("default_model"),
                        **model_info
                    })
        
        return models
    
    except Exception as e:
        logger.error(f"Error getting available models: {str(e)}")
        return []

def get_default_model_info() -> Dict[str, str]:
    """
    Get the default model provider and name.
    
    Returns:
        Dictionary with provider and model_name keys
    """
    try:
        config = get_models_config()
        default = config.get("default", {})
        
        # Check environment variables first
        provider = os.environ.get("MODEL_PROVIDER", default.get("provider", "google"))
        model_name = os.environ.get("MODEL_NAME", None)
        
        # If model_name is not set in environment, get the default for the provider
        if not model_name:
            providers = config.get("providers", {})
            if provider in providers:
                model_name = providers[provider].get("default_model")
            
            # If still not set, use the default from the config
            if not model_name:
                model_name = default.get("model_name", "gemini-1.5-pro")
        
        return {
            "provider": provider,
            "model_name": model_name
        }
    
    except Exception as e:
        logger.error(f"Error getting default model info: {str(e)}")
        return {
            "provider": "google",
            "model_name": "gemini-1.5-pro"
        }
