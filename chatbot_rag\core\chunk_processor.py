from dataclasses import dataclass
from typing import List, Dict, Optional
from datetime import datetime
import tiktoken

@dataclass
class ChunkMetadata:
    source_file: FileMetadata
    chunk_index: int
    token_count: int
    embedding_model: str
    chunk_timestamp: str
    context_window: Dict[str, str]  # Previous/next chunk summaries
    semantic_type: str  # e.g., "code", "comment", "documentation"

class ChunkProcessor:
    def __init__(self, embedding_model: str, chunk_size: int = 1000, overlap: int = 100):
        self.embedding_model = embedding_model
        self.embeddings = OllamaEmbeddings(model=embedding_model)
        self.tokenizer = tiktoken.encoding_for_model("gpt-4")
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=overlap,
            length_function=len,
            is_separator_regex=False
        )
        
    def _count_tokens(self, text: str) -> int:
        """Count tokens in text using tiktoken"""
        return len(self.tokenizer.encode(text))
        
    def _detect_semantic_type(self, chunk: str) -> str:
        """Detect if chunk is code, comment, or documentation"""
        # Add detection logic here
        pass
        
    def create_enhanced_chunks(self, content: str, file_metadata: FileMetadata) -> List[Dict]:
        """Create chunks with enhanced metadata"""
        raw_chunks = self.text_splitter.split_text(content)
        enhanced_chunks = []
        
        for i, chunk in enumerate(raw_chunks):
            # Create context window
            prev_summary = self._summarize_chunk(raw_chunks[i-1]) if i > 0 else None
            next_summary = self._summarize_chunk(raw_chunks[i+1]) if i < len(raw_chunks)-1 else None
            
            metadata = ChunkMetadata(
                source_file=file_metadata,
                chunk_index=i,
                token_count=self._count_tokens(chunk),
                embedding_model=self.embedding_model,
                chunk_timestamp=datetime.now().isoformat(),
                context_window={
                    'previous': prev_summary,
                    'next': next_summary
                },
                semantic_type=self._detect_semantic_type(chunk)
            )
            
            enhanced_chunks.append({
                'content': chunk,
                'metadata': metadata
            })
            
        return enhanced_chunks