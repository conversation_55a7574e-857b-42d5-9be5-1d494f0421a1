// Initialize code block functions
window.codeBlockFunctions = {
    // Variables for resize functionality
    isResizing: false,
    currentResizeBlock: null,
    initialHeight: 0,
    initialWidth: 0,
    initialX: 0,
    initialY: 0,

    // Toggle code block collapse/expand
    toggleCodeBlock: function(blockId) {
        const block = document.getElementById(blockId);
        const content = block.querySelector('.code-block-content');
        const button = block.querySelector('.code-block-collapse');

        if (content.style.display === 'none') {
            // Expand
            content.style.display = 'block';
            button.textContent = '\u2212'; // Minus sign
        } else {
            // Collapse
            content.style.display = 'none';
            button.textContent = '\u002B'; // Plus sign
        }
    },

    // Start resizing a code block
    startResize: function(blockId, event) {
        this.isResizing = true;
        const codeBlock = document.getElementById(blockId);
        this.currentResizeBlock = codeBlock.querySelector('.code-block-content');
        this.initialHeight = this.currentResizeBlock.offsetHeight;
        this.initialWidth = this.currentResizeBlock.offsetWidth;
        this.initialX = event.clientX;
        this.initialY = event.clientY;

        // Add resizing class to the code block
        codeBlock.classList.add('resizing');

        // Prevent text selection during resize
        event.preventDefault();

        // Add event listeners for resize
        document.addEventListener('mousemove', this.resize.bind(this));
        document.addEventListener('mouseup', this.stopResize.bind(this));
    },

    // Handle resize
    resize: function(e) {
        if (!this.isResizing) return;

        // Calculate deltas
        const deltaX = e.clientX - this.initialX;
        const deltaY = e.clientY - this.initialY;

        // Calculate new dimensions with minimums
        const newWidth = Math.max(300, this.initialWidth + deltaX); // Minimum width of 300px
        const newHeight = Math.max(100, this.initialHeight + deltaY); // Minimum height of 100px

        // Apply new dimensions
        this.currentResizeBlock.style.width = newWidth + 'px';
        this.currentResizeBlock.style.height = newHeight + 'px';

        // Ensure the pre element also resizes properly
        const preElement = this.currentResizeBlock.querySelector('pre');
        if (preElement) {
            preElement.style.minWidth = (newWidth - 20) + 'px'; // Account for padding
            preElement.style.minHeight = (newHeight - 20) + 'px'; // Account for padding
        }
    },

    // Stop resizing
    stopResize: function() {
        if (!this.isResizing) return;

        this.isResizing = false;

        // Remove resizing class from all code blocks
        document.querySelectorAll('.code-block.resizing').forEach(block => {
            block.classList.remove('resizing');
        });

        // Remove event listeners
        document.removeEventListener('mousemove', this.resize.bind(this));
        document.removeEventListener('mouseup', this.stopResize.bind(this));

        // Store the current dimensions to make them persistent
        if (this.currentResizeBlock) {
            this.currentResizeBlock.dataset.lastWidth = this.currentResizeBlock.style.width;
            this.currentResizeBlock.dataset.lastHeight = this.currentResizeBlock.style.height;
        }
    },

    // Find a code token in the file
    findCodeToken: function(token) {
        // Send a message to the extension to find the token
        if (window.vscode) {
            window.vscode.postMessage({
                type: 'findCodeToken',
                token: token
            });
        }
    },

    // Open a file in VS Code
    openFile: function(filePath) {
        if (window.vscode) {
            window.vscode.postMessage({
                type: 'openFile',
                filePath: filePath
            });
        }
    },

    // Apply changes to a file
    applyChanges: function(blockId, filePath) {
        const block = document.getElementById(blockId);
        if (!block) return;

        // Get the code content
        const codeElement = block.querySelector('code');
        if (!codeElement) return;

        const code = codeElement.textContent;

        // Send a message to the extension to apply the changes
        if (window.vscode) {
            window.vscode.postMessage({
                type: 'applyChanges',
                blockId: blockId,
                filePath: filePath,
                code: code
            });
        }
    },

    // Undo changes to a file
    undoChanges: function(blockId, filePath) {
        // Send a message to the extension to undo the changes
        if (window.vscode) {
            window.vscode.postMessage({
                type: 'undoChanges',
                blockId: blockId,
                filePath: filePath
            });
        }
    },

    // Update the UI after applying changes
    updateAfterApply: function(blockId) {
        const block = document.getElementById(blockId);
        if (!block) return;

        // Enable the Undo button and disable the Apply button
        const applyButton = block.querySelector('.code-block-actions button:first-child');
        const undoButton = block.querySelector('.code-block-actions button:last-child');

        if (applyButton) applyButton.disabled = true;
        if (undoButton) undoButton.disabled = false;

        // Show a success message
        this.showNotification(blockId, 'Changes applied successfully', 'success');
    },

    // Update the UI after undoing changes
    updateAfterUndo: function(blockId) {
        const block = document.getElementById(blockId);
        if (!block) return;

        // Enable the Apply button and disable the Undo button
        const applyButton = block.querySelector('.code-block-actions button:first-child');
        const undoButton = block.querySelector('.code-block-actions button:last-child');

        if (applyButton) applyButton.disabled = false;
        if (undoButton) undoButton.disabled = true;

        // Show a success message
        this.showNotification(blockId, 'Changes undone', 'info');
    },

    // Show a notification in the code block
    showNotification: function(blockId, message, type = 'info') {
        const block = document.getElementById(blockId);
        if (!block) return;

        // Create a notification element
        const notification = document.createElement('div');
        notification.className = 'code-block-notification ' + type;
        notification.textContent = message;

        // Add the notification to the code block
        block.appendChild(notification);

        // Remove the notification after a delay
        setTimeout(() => {
            notification.classList.add('fade-out');
            setTimeout(() => {
                notification.remove();
            }, 500);
        }, 3000);
    },

    // Apply syntax highlighting to all code blocks
    highlightAll: function() {
        if (typeof Prism !== 'undefined') {
            Prism.highlightAll();
        }
    }
};

// Initialize highlighting when the page loads
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        window.codeBlockFunctions.highlightAll();

        // Add scroll event listeners to all code blocks
        document.querySelectorAll('.code-block-content').forEach(content => {
            content.addEventListener('scroll', function() {
                // Add scrolling class when scrolling starts
                this.closest('.code-block').classList.add('scrolling');

                // Clear any existing timeout
                if (this.scrollTimeout) {
                    clearTimeout(this.scrollTimeout);
                }

                // Set a timeout to remove the class after scrolling stops
                this.scrollTimeout = setTimeout(() => {
                    this.closest('.code-block').classList.remove('scrolling');
                }, 1000); // 1 second after scrolling stops
            });

            // Also add the same for the pre element inside
            const preElement = content.querySelector('pre');
            if (preElement) {
                preElement.addEventListener('scroll', function() {
                    // Add scrolling class when scrolling starts
                    content.closest('.code-block').classList.add('scrolling');

                    // Clear any existing timeout
                    if (this.scrollTimeout) {
                        clearTimeout(this.scrollTimeout);
                    }

                    // Set a timeout to remove the class after scrolling stops
                    this.scrollTimeout = setTimeout(() => {
                        content.closest('.code-block').classList.remove('scrolling');
                    }, 1000); // 1 second after scrolling stops
                });
            }
        });
    }, 0);
});
