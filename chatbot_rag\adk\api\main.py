"""
Main FastAPI application for the ADK API.

This module creates the FastAPI application and includes all routers.
"""

import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from chatbot_rag.adk.api.adk_endpoints import router as adk_router
from chatbot_rag.adk.api.cache_endpoints import router as cache_router

# Configure logging
logger = logging.getLogger("adk_api")

# Create FastAPI app
app = FastAPI(
    title="ADK API",
    description="API for the Agent Development Kit",
    version="0.1.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(adk_router, prefix="/adk")

# Include cache router under the ADK router
adk_router.include_router(cache_router)

@app.get("/")
async def root():
    """
    Root endpoint.
    """
    return {
        "message": "Welcome to the ADK API",
        "docs_url": "/docs",
        "redoc_url": "/redoc"
    }
