import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';
import { FileManager } from './fileManager';
import { CodeIndexer, CodeEntity } from './codeIndexer';

/**
 * Handles code analysis and validation
 */
/**
 * Represents a validation result with detailed information
 */
export interface ValidationResult {
    valid: boolean;
    errors: ValidationError[];
    warnings: ValidationError[];
    suggestions: ValidationSuggestion[];
}

/**
 * Represents a validation error or warning
 */
export interface ValidationError {
    message: string;
    severity: vscode.DiagnosticSeverity;
    range: vscode.Range;
    source?: string;
    code?: string;
}

/**
 * Represents a validation suggestion
 */
export interface ValidationSuggestion {
    message: string;
    range: vscode.Range;
    replacement?: string;
}

/**
 * Represents a validation cache entry
 */
interface ValidationCacheEntry {
    result: ValidationResult;
    timestamp: number;
    contentHash: string;
}

export class CodeAnalyzer {
    private static instance: CodeAnalyzer;
    private fileManager: FileManager;
    private codeIndexer: CodeIndexer | null = null;
    private validationCache: Map<string, ValidationCacheEntry>;
    private readonly cacheExpirationTime: number = 5 * 60 * 1000; // 5 minutes in milliseconds
    private context: vscode.ExtensionContext | null = null;

    private constructor() {
        this.fileManager = FileManager.getInstance();
        this.validationCache = new Map<string, ValidationCacheEntry>();
    }

    /**
     * Get the singleton instance of CodeAnalyzer
     * @param context Optional extension context for initialization
     */
    public static getInstance(context?: vscode.ExtensionContext): CodeAnalyzer {
        if (!CodeAnalyzer.instance) {
            CodeAnalyzer.instance = new CodeAnalyzer();
        }

        // Initialize with context if provided
        if (context && !CodeAnalyzer.instance.context) {
            CodeAnalyzer.instance.context = context;

            // Initialize code indexer
            if (!CodeAnalyzer.instance.codeIndexer && context) {
                CodeAnalyzer.instance.codeIndexer = CodeIndexer.getInstance(context);
            }
        }

        return CodeAnalyzer.instance;
    }

    /**
     * Validate code edits
     * @param filePath The file path
     * @param edits The edits to validate
     * @returns A promise that resolves to a validation result
     */
    public async validateEdits(
        filePath: string,
        edits: { start: vscode.Position; end: vscode.Position; text: string }[]
    ): Promise<ValidationResult> {
        try {
            // Open the file
            const document = await vscode.workspace.openTextDocument(filePath);
            const originalContent = document.getText();

            // Apply the edits to a temporary string
            let editedContent = originalContent;

            // Sort edits in reverse order to avoid position changes
            const sortedEdits = [...edits].sort((a, b) => {
                if (a.start.line !== b.start.line) {
                    return b.start.line - a.start.line;
                }
                return b.start.character - a.start.character;
            });

            for (const edit of sortedEdits) {
                const startOffset = document.offsetAt(edit.start);
                const endOffset = document.offsetAt(edit.end);
                editedContent = editedContent.substring(0, startOffset) +
                                edit.text +
                                editedContent.substring(endOffset);
            }

            // Generate a hash of the edited content for caching
            const contentHash = await this.hashContent(editedContent);

            // Check if we have a cached validation result
            const cacheKey = `${filePath}:${contentHash}`;
            const cachedResult = this.validationCache.get(cacheKey);

            if (cachedResult && Date.now() - cachedResult.timestamp < this.cacheExpirationTime) {
                return cachedResult.result;
            }

            // Perform language-specific validation
            const result = await this.validateContent(filePath, editedContent);

            // Cache the result
            this.validationCache.set(cacheKey, {
                result,
                timestamp: Date.now(),
                contentHash
            });

            return result;
        } catch (error) {
            console.error(`Error validating edits: ${error}`);
            return {
                valid: false,
                errors: [{
                    message: `Error validating edits: ${error}`,
                    severity: vscode.DiagnosticSeverity.Error,
                    range: new vscode.Range(0, 0, 0, 0)
                }],
                warnings: [],
                suggestions: []
            };
        }
    }

    /**
     * Hash content for caching
     * @param content The content to hash
     * @returns A hash of the content
     */
    private async hashContent(content: string): Promise<string> {
        // Simple hash function for now
        let hash = 0;
        for (let i = 0; i < content.length; i++) {
            const char = content.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
        }
        return hash.toString();
    }

    /**
     * Validate content with language-specific validation
     * @param filePath The file path
     * @param content The content to validate
     * @returns A promise that resolves to a validation result
     */
    private async validateContent(filePath: string, content: string): Promise<ValidationResult> {
        // Create a temporary file with the edited content
        const tempFilePath = `${filePath}.temp`;
        const fs = require('fs');

        try {
            // Write the content to a temporary file
            fs.writeFileSync(tempFilePath, content);

            // Open the temporary file
            const document = await vscode.workspace.openTextDocument(tempFilePath);

            // Get the language ID
            const languageId = document.languageId;

            // Wait for diagnostics to be calculated
            return new Promise<ValidationResult>((resolve) => {
                // Wait a bit for diagnostics to be calculated
                setTimeout(async () => {
                    // Get diagnostics for the document
                    const diagnostics = vscode.languages.getDiagnostics(document.uri);

                    // Convert diagnostics to validation errors and warnings
                    const errors: ValidationError[] = [];
                    const warnings: ValidationError[] = [];

                    for (const diagnostic of diagnostics) {
                        const validationError: ValidationError = {
                            message: diagnostic.message,
                            severity: diagnostic.severity,
                            range: diagnostic.range,
                            source: diagnostic.source,
                            code: diagnostic.code?.toString()
                        };

                        if (diagnostic.severity === vscode.DiagnosticSeverity.Error) {
                            errors.push(validationError);
                        } else if (diagnostic.severity === vscode.DiagnosticSeverity.Warning) {
                            warnings.push(validationError);
                        }
                    }

                    // Get suggestions from language-specific validation
                    const suggestions = await this.getLanguageSpecificSuggestions(document, languageId);

                    // Close the document
                    await vscode.window.showTextDocument(document, { preview: true }).then(editor => {
                        return vscode.commands.executeCommand('workbench.action.closeActiveEditor');
                    });

                    // Delete the temporary file
                    fs.unlinkSync(tempFilePath);

                    resolve({
                        valid: errors.length === 0,
                        errors,
                        warnings,
                        suggestions
                    });
                }, 1000); // Wait 1 second for diagnostics
            });
        } catch (error) {
            console.error(`Error validating content: ${error}`);

            // Clean up
            if (fs.existsSync(tempFilePath)) {
                fs.unlinkSync(tempFilePath);
            }

            return {
                valid: false,
                errors: [{
                    message: `Error validating content: ${error}`,
                    severity: vscode.DiagnosticSeverity.Error,
                    range: new vscode.Range(0, 0, 0, 0)
                }],
                warnings: [],
                suggestions: []
            };
        }
    }

    /**
     * Get language-specific suggestions
     * @param document The document to get suggestions for
     * @param languageId The language ID
     * @returns A promise that resolves to an array of validation suggestions
     */
    private async getLanguageSpecificSuggestions(
        document: vscode.TextDocument,
        languageId: string
    ): Promise<ValidationSuggestion[]> {
        const suggestions: ValidationSuggestion[] = [];

        try {
            // Get code actions for the entire document
            const range = new vscode.Range(
                0, 0,
                document.lineCount - 1,
                document.lineAt(document.lineCount - 1).text.length
            );

            const codeActions = await vscode.commands.executeCommand<vscode.CodeAction[]>(
                'vscode.executeCodeActionProvider',
                document.uri,
                range
            );

            if (codeActions) {
                for (const action of codeActions) {
                    // Only include quick fix actions
                    if (action.kind && action.kind.value.includes('quickfix')) {
                        // Get the edit range and replacement text
                        if (action.edit) {
                            for (const [uri, edits] of action.edit.entries()) {
                                if (uri.toString() === document.uri.toString()) {
                                    for (const edit of edits) {
                                        suggestions.push({
                                            message: action.title,
                                            range: edit.range,
                                            replacement: edit.newText
                                        });
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (error) {
            console.error(`Error getting language-specific suggestions: ${error}`);
        }

        return suggestions;
    }

    /**
     * Get diagnostics for edited content (legacy method for backward compatibility)
     * @param filePath The file path
     * @param content The edited content
     * @returns A promise that resolves to an array of error messages
     */
    private async getDiagnostics(filePath: string, content: string): Promise<string[]> {
        // Create a temporary file with the edited content
        const tempFilePath = `${filePath}.temp`;
        const fs = require('fs');

        try {
            // Write the content to a temporary file
            fs.writeFileSync(tempFilePath, content);

            // Open the temporary file
            const document = await vscode.workspace.openTextDocument(tempFilePath);

            // Wait for diagnostics to be calculated
            return new Promise<string[]>((resolve) => {
                // Wait a bit for diagnostics to be calculated
                setTimeout(async () => {
                    // Get diagnostics for the document
                    const diagnostics = vscode.languages.getDiagnostics(document.uri);

                    // Convert diagnostics to error messages
                    const errors = diagnostics.map(diagnostic =>
                        `${diagnostic.severity === vscode.DiagnosticSeverity.Error ? 'Error' : 'Warning'}: ${diagnostic.message} (Line ${diagnostic.range.start.line + 1})`
                    );

                    // Close the document
                    await vscode.window.showTextDocument(document, { preview: true }).then(editor => {
                        return vscode.commands.executeCommand('workbench.action.closeActiveEditor');
                    });

                    // Delete the temporary file
                    fs.unlinkSync(tempFilePath);

                    resolve(errors);
                }, 1000); // Wait 1 second for diagnostics
            });
        } catch (error) {
            console.error(`Error getting diagnostics: ${error}`);

            // Clean up
            if (fs.existsSync(tempFilePath)) {
                fs.unlinkSync(tempFilePath);
            }

            return [`Error getting diagnostics: ${error}`];
        }
    }

    /**
     * Find code entities in a file
     * @param filePath The file path
     * @param entityName The entity name to find
     * @param entityType Optional entity type to filter by
     * @returns A promise that resolves to the position of the entity or undefined if not found
     */
    public async findCodeEntity(
        filePath: string,
        entityName: string,
        entityType?: string
    ): Promise<vscode.Position | undefined> {
        try {
            // Try to find the entity using the code indexer first
            if (this.codeIndexer) {
                // Ensure the workspace is indexed
                if (!this.codeIndexer.isWorkspaceIndexed()) {
                    await this.codeIndexer.indexWorkspace();
                }

                // Find entities in the file
                const entities = this.codeIndexer.findEntitiesInFile(filePath, entityType);

                // Find the entity with the matching name
                const entity = entities.find(e => e.name === entityName);

                if (entity) {
                    return entity.position;
                }

                // If not found in the file, try to find it by name
                const entitiesByName = this.codeIndexer.findEntitiesByName(entityName, entityType);

                // Filter to only include entities in the specified file
                const entityInFile = entitiesByName.find(e => e.filePath === filePath);

                if (entityInFile) {
                    return entityInFile.position;
                }
            }

            // Fall back to pattern matching if the indexer didn't find anything
            return await this.findCodeEntityByPattern(filePath, entityName);
        } catch (error) {
            console.error(`Error finding code entity: ${error}`);
            return undefined;
        }
    }

    /**
     * Find code entity by pattern matching
     * @param filePath The file path
     * @param entityName The entity name to find
     * @returns A promise that resolves to the position of the entity or undefined if not found
     */
    private async findCodeEntityByPattern(filePath: string, entityName: string): Promise<vscode.Position | undefined> {
        try {
            // Open the file
            const document = await vscode.workspace.openTextDocument(filePath);
            const text = document.getText();

            // Try to find the entity using simple pattern matching
            // This is a basic implementation - a more robust solution would use a language server or AST

            // Common patterns for different entity types
            const patterns = [
                // Class definition
                new RegExp(`class\\s+${this.escapeRegExp(entityName)}\\s*[({]`, 'i'),
                // Function/method definition
                new RegExp(`function\\s+${this.escapeRegExp(entityName)}\\s*\\(`, 'i'),
                new RegExp(`def\\s+${this.escapeRegExp(entityName)}\\s*\\(`, 'i'),
                // Variable declaration
                new RegExp(`(const|let|var)\\s+${this.escapeRegExp(entityName)}\\s*=`, 'i'),
                // Property definition
                new RegExp(`this\\.${this.escapeRegExp(entityName)}\\s*=`, 'i'),
                // Interface definition
                new RegExp(`interface\\s+${this.escapeRegExp(entityName)}\\s*[{<]`, 'i'),
                // Type definition
                new RegExp(`type\\s+${this.escapeRegExp(entityName)}\\s*=`, 'i'),
                // Enum definition
                new RegExp(`enum\\s+${this.escapeRegExp(entityName)}\\s*{`, 'i'),
                // Import statement
                new RegExp(`import\\s+.*${this.escapeRegExp(entityName)}`, 'i'),
                // Export statement
                new RegExp(`export\\s+.*${this.escapeRegExp(entityName)}`, 'i'),
                // Generic identifier
                new RegExp(`\\b${this.escapeRegExp(entityName)}\\b`, 'i')
            ];

            // Try each pattern
            for (const pattern of patterns) {
                const match = pattern.exec(text);
                if (match) {
                    const index = match.index;
                    return document.positionAt(index);
                }
            }

            return undefined;
        } catch (error) {
            console.error(`Error finding code entity by pattern: ${error}`);
            return undefined;
        }
    }

    /**
     * Escape special characters in a string for use in a regular expression
     * @param string The string to escape
     * @returns The escaped string
     */
    private escapeRegExp(string: string): string {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    /**
     * Get code completion suggestions
     * @param filePath The file path
     * @param position The position to get suggestions for
     * @returns A promise that resolves to an array of completion items
     */
    public async getCompletionSuggestions(
        filePath: string,
        position: vscode.Position
    ): Promise<vscode.CompletionItem[]> {
        try {
            // Open the file
            const document = await vscode.workspace.openTextDocument(filePath);

            // Get completion items from VS Code
            const completionItems = await vscode.commands.executeCommand<vscode.CompletionList>(
                'vscode.executeCompletionItemProvider',
                document.uri,
                position
            );

            return completionItems?.items || [];
        } catch (error) {
            console.error(`Error getting completion suggestions: ${error}`);
            return [];
        }
    }

    /**
     * Analyze code quality
     * @param filePath The file path
     * @returns A promise that resolves to a code quality report
     */
    public async analyzeCodeQuality(filePath: string): Promise<{
        issues: ValidationError[];
        metrics: { [key: string]: number };
    }> {
        try {
            // Open the file
            const document = await vscode.workspace.openTextDocument(filePath);
            const content = document.getText();

            // Get diagnostics
            const validationResult = await this.validateContent(filePath, content);

            // Calculate metrics
            const metrics = this.calculateCodeMetrics(document);

            return {
                issues: [...validationResult.errors, ...validationResult.warnings],
                metrics
            };
        } catch (error) {
            console.error(`Error analyzing code quality: ${error}`);
            return {
                issues: [],
                metrics: {}
            };
        }
    }

    /**
     * Calculate code metrics
     * @param document The document to calculate metrics for
     * @returns An object with code metrics
     */
    private calculateCodeMetrics(document: vscode.TextDocument): { [key: string]: number } {
        const content = document.getText();
        const lines = content.split('\n');

        // Calculate basic metrics
        const lineCount = lines.length;
        const emptyLines = lines.filter(line => line.trim().length === 0).length;
        const commentLines = lines.filter(line => {
            const trimmed = line.trim();
            return trimmed.startsWith('//') || trimmed.startsWith('/*') || trimmed.startsWith('*');
        }).length;
        const codeLines = lineCount - emptyLines - commentLines;

        // Calculate complexity metrics
        const complexityScore = this.calculateComplexity(content);

        return {
            lineCount,
            emptyLines,
            commentLines,
            codeLines,
            commentRatio: commentLines / (codeLines || 1), // Avoid division by zero
            complexityScore
        };
    }

    /**
     * Calculate code complexity
     * @param content The code content
     * @returns A complexity score
     */
    private calculateComplexity(content: string): number {
        // Count control flow statements as a simple complexity metric
        const controlFlowPatterns = [
            /\bif\s*\(/g,
            /\belse\b/g,
            /\bfor\s*\(/g,
            /\bwhile\s*\(/g,
            /\bdo\s*\{/g,
            /\bswitch\s*\(/g,
            /\bcase\s+/g,
            /\bcatch\s*\(/g,
            /\?/g, // Ternary operator
            /\|\|/g, // Logical OR
            /&&/g // Logical AND
        ];

        let complexityScore = 0;

        for (const pattern of controlFlowPatterns) {
            const matches = content.match(pattern);
            if (matches) {
                complexityScore += matches.length;
            }
        }

        // Count function declarations
        const functionMatches = content.match(/\bfunction\s+\w+\s*\(/g);
        if (functionMatches) {
            complexityScore += functionMatches.length * 0.5; // Weight functions less than control flow
        }

        // Count class declarations
        const classMatches = content.match(/\bclass\s+\w+/g);
        if (classMatches) {
            complexityScore += classMatches.length * 0.5; // Weight classes less than control flow
        }

        return complexityScore;
    }

    /**
     * Get code entities in a file
     * @param filePath The file path
     * @returns A promise that resolves to an array of code entities
     */
    public async getCodeEntities(filePath: string): Promise<CodeEntity[]> {
        try {
            // Try to use the code indexer first
            if (this.codeIndexer) {
                // Ensure the workspace is indexed
                if (!this.codeIndexer.isWorkspaceIndexed()) {
                    await this.codeIndexer.indexWorkspace();
                }

                // Get entities from the indexer
                return this.codeIndexer.findEntitiesInFile(filePath);
            }

            // Fall back to document symbols if the indexer is not available
            const document = await vscode.workspace.openTextDocument(filePath);
            const symbols = await vscode.commands.executeCommand<vscode.DocumentSymbol[]>(
                'vscode.executeDocumentSymbolProvider',
                document.uri
            );

            if (!symbols || symbols.length === 0) {
                return [];
            }

            // Convert symbols to code entities
            const entities: CodeEntity[] = [];
            this.convertSymbolsToEntities(symbols, filePath, '', entities);

            return entities;
        } catch (error) {
            console.error(`Error getting code entities: ${error}`);
            return [];
        }
    }

    /**
     * Convert document symbols to code entities
     * @param symbols The document symbols
     * @param filePath The file path
     * @param parentName The parent symbol name
     * @param entities The array to add entities to
     */
    private convertSymbolsToEntities(
        symbols: vscode.DocumentSymbol[],
        filePath: string,
        parentName: string,
        entities: CodeEntity[]
    ): void {
        for (const symbol of symbols) {
            // Create an entity for this symbol
            const entity: CodeEntity = {
                name: symbol.name,
                type: this.getEntityTypeFromSymbolKind(symbol.kind),
                filePath,
                position: symbol.range.start,
                symbolKind: symbol.kind,
                parentName: parentName || undefined,
                documentation: symbol.detail || undefined
            };

            // Add to entities
            entities.push(entity);

            // Process children recursively
            if (symbol.children && symbol.children.length > 0) {
                this.convertSymbolsToEntities(symbol.children, filePath, symbol.name, entities);
            }
        }
    }

    /**
     * Get entity type from symbol kind
     * @param kind The symbol kind
     * @returns The entity type
     */
    private getEntityTypeFromSymbolKind(kind: vscode.SymbolKind): string {
        switch (kind) {
            case vscode.SymbolKind.Class:
                return 'class';
            case vscode.SymbolKind.Interface:
                return 'interface';
            case vscode.SymbolKind.Enum:
                return 'enum';
            case vscode.SymbolKind.Function:
            case vscode.SymbolKind.Method:
                return 'function';
            case vscode.SymbolKind.Variable:
            case vscode.SymbolKind.Property:
                return 'variable';
            case vscode.SymbolKind.Constructor:
                return 'constructor';
            case vscode.SymbolKind.Module:
            case vscode.SymbolKind.Namespace:
                return 'module';
            case vscode.SymbolKind.TypeParameter:
            case vscode.SymbolKind.Struct:
                return 'type';
            default:
                return 'unknown';
        }
    }

    /**
     * Clear the validation cache
     */
    public clearValidationCache(): void {
        this.validationCache.clear();
    }

    /**
     * Get the size of the validation cache
     * @returns The number of entries in the validation cache
     */
    public getValidationCacheSize(): number {
        return this.validationCache.size;
    }

    /**
     * Trigger workspace indexing
     * @param force Force reindexing even if the index is recent
     * @returns A promise that resolves when indexing is complete
     */
    public async indexWorkspace(force: boolean = false): Promise<void> {
        if (this.codeIndexer) {
            return this.codeIndexer.indexWorkspace(force);
        }
    }

    /**
     * Get indexing status
     * @returns An object with indexing status information
     */
    public getIndexingStatus(): {
        indexed: boolean;
        indexing: boolean;
        fileCount: number;
        entityCount: number;
        lastIndexTime: Date | null;
    } {
        if (!this.codeIndexer) {
            return {
                indexed: false,
                indexing: false,
                fileCount: 0,
                entityCount: 0,
                lastIndexTime: null
            };
        }

        return {
            indexed: this.codeIndexer.isWorkspaceIndexed(),
            indexing: this.codeIndexer.isIndexingInProgress(),
            fileCount: this.codeIndexer.getIndexedFileCount(),
            entityCount: this.codeIndexer.getIndexedEntityCount(),
            lastIndexTime: this.codeIndexer.getLastIndexTime()
        };
    }
}
