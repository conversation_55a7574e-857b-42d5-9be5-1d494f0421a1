import * as vscode from 'vscode';
import * as path from 'path';
import * as fs from 'fs';

/**
 * Manages file operations for the RAG Assistant
 */
export class FileManager {
    private static instance: FileManager;

    private constructor() { }

    /**
     * Get the singleton instance of FileManager
     */
    public static getInstance(): FileManager {
        if (!FileManager.instance) {
            FileManager.instance = new FileManager();
        }
        return FileManager.instance;
    }

    /**
     * Get the workspace root path
     * @returns The workspace root path or undefined if no workspace is open
     */
    public getWorkspaceRoot(): string | undefined {
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (!workspaceFolders || workspaceFolders.length === 0) {
            return undefined;
        }
        return workspaceFolders[0].uri.fsPath;
    }

    /**
     * Get the relative path from the workspace root
     * @param filePath The absolute file path
     * @returns The relative path from the workspace root
     */
    public getRelativePath(filePath: string): string {
        const workspaceRoot = this.getWorkspaceRoot();
        if (!workspaceRoot) {
            return filePath;
        }

        // Normalize paths to handle different OS path separators
        const normalizedWorkspaceRoot = workspaceRoot.replace(/\\/g, '/');
        const normalizedFilePath = filePath.replace(/\\/g, '/');

        if (normalizedFilePath.startsWith(normalizedWorkspaceRoot)) {
            return normalizedFilePath.substring(normalizedWorkspaceRoot.length + 1);
        }

        return filePath;
    }

    /**
     * Get the absolute path from a relative path
     * @param relativePath The relative path from the workspace root
     * @returns The absolute path
     */
    public getAbsolutePath(relativePath: string): string | undefined {
        const workspaceRoot = this.getWorkspaceRoot();
        if (!workspaceRoot) {
            return undefined;
        }

        return path.join(workspaceRoot, relativePath);
    }

    /**
     * Check if a file exists
     * @param filePath The file path to check
     * @returns True if the file exists, false otherwise
     */
    public fileExists(filePath: string): boolean {
        try {
            return fs.existsSync(filePath);
        } catch (error) {
            console.error(`Error checking if file exists: ${error}`);
            return false;
        }
    }

    /**
     * Get the file extension
     * @param filePath The file path
     * @returns The file extension (without the dot)
     */
    public getFileExtension(filePath: string): string {
        return path.extname(filePath).substring(1);
    }

    /**
     * Get the file name
     * @param filePath The file path
     * @returns The file name (with extension)
     */
    public getFileName(filePath: string): string {
        return path.basename(filePath);
    }

    /**
     * Open a file in VS Code
     * @param filePath The file path to open
     * @returns A promise that resolves to the text document
     */
    public async openFile(filePath: string): Promise<vscode.TextDocument> {
        try {
            // Check if the path is relative
            if (!path.isAbsolute(filePath)) {
                const absolutePath = this.getAbsolutePath(filePath);
                if (!absolutePath) {
                    throw new Error(`Could not resolve absolute path for ${filePath}`);
                }
                filePath = absolutePath;
            }

            // Check if the file exists
            if (!this.fileExists(filePath)) {
                throw new Error(`File does not exist: ${filePath}`);
            }

            // Open the file
            const document = await vscode.workspace.openTextDocument(filePath);
            await vscode.window.showTextDocument(document);
            return document;
        } catch (error) {
            console.error(`Error opening file: ${error}`);
            throw error;
        }
    }

    /**
     * Apply edits to a file
     * @param filePath The file path to edit
     * @param edits The edits to apply
     * @returns A promise that resolves when the edits are applied
     */
    public async applyEdits(
        filePath: string, 
        edits: { start: vscode.Position; end: vscode.Position; text: string }[]
    ): Promise<void> {
        try {
            // Open the file
            const document = await this.openFile(filePath);
            const editor = vscode.window.activeTextEditor;

            if (!editor) {
                throw new Error('No active text editor');
            }

            // Apply the edits
            await editor.edit(editBuilder => {
                for (const edit of edits) {
                    const range = new vscode.Range(edit.start, edit.end);
                    editBuilder.replace(range, edit.text);
                }
            });
        } catch (error) {
            console.error(`Error applying edits: ${error}`);
            throw error;
        }
    }

    /**
     * Calculate line changes between original and edited content
     * @param originalContent The original file content
     * @param edits The edits to apply
     * @returns Object with added and removed line counts
     */
    public calculateLineChanges(
        originalContent: string,
        edits: { start: vscode.Position; end: vscode.Position; text: string }[]
    ): { added: number; removed: number } {
        let added = 0;
        let removed = 0;

        for (const edit of edits) {
            // Calculate lines in the original content that will be replaced
            const originalLines = originalContent
                .split('\n')
                .slice(edit.start.line, edit.end.line + 1);
            
            // Adjust for partial first and last lines
            if (originalLines.length > 0) {
                // Adjust first line
                originalLines[0] = originalLines[0].substring(edit.start.character);
                
                // Adjust last line if it's different from the first line
                if (originalLines.length > 1) {
                    originalLines[originalLines.length - 1] = 
                        originalLines[originalLines.length - 1].substring(0, edit.end.character);
                }
            }

            // Count removed lines (non-empty lines only)
            removed += originalLines.filter(line => line.trim().length > 0).length;

            // Count added lines (non-empty lines only)
            const newLines = edit.text.split('\n').filter(line => line.trim().length > 0);
            added += newLines.length;
        }

        return { added, removed };
    }

    /**
     * Find a token in a file
     * @param filePath The file path to search
     * @param token The token to find
     * @returns The position of the token or undefined if not found
     */
    public async findToken(filePath: string, token: string): Promise<vscode.Position | undefined> {
        try {
            // Open the file
            const document = await this.openFile(filePath);
            const text = document.getText();

            // Find the token
            const index = text.indexOf(token);
            if (index === -1) {
                return undefined;
            }

            // Convert index to position
            return document.positionAt(index);
        } catch (error) {
            console.error(`Error finding token: ${error}`);
            return undefined;
        }
    }

    /**
     * Highlight a line in a file
     * @param filePath The file path
     * @param line The line number to highlight
     * @param duration The duration in milliseconds to highlight the line
     */
    public async highlightLine(filePath: string, position: vscode.Position, duration: number = 3000): Promise<void> {
        try {
            // Open the file
            const document = await this.openFile(filePath);
            const editor = vscode.window.activeTextEditor;

            if (!editor) {
                throw new Error('No active text editor');
            }

            // Create a decoration type
            const decorationType = vscode.window.createTextEditorDecorationType({
                backgroundColor: '#FFF9C4',
                isWholeLine: true
            });

            // Apply the decoration
            const range = document.lineAt(position.line).range;
            editor.setDecorations(decorationType, [range]);

            // Reveal the line
            editor.revealRange(range, vscode.TextEditorRevealType.InCenter);

            // Remove the decoration after the specified duration
            setTimeout(() => {
                decorationType.dispose();
            }, duration);
        } catch (error) {
            console.error(`Error highlighting line: ${error}`);
        }
    }

    /**
     * Create a backup of a file before editing
     * @param filePath The file path to backup
     * @returns The backup file path
     */
    public async createBackup(filePath: string): Promise<string> {
        try {
            // Check if the file exists
            if (!this.fileExists(filePath)) {
                throw new Error(`File does not exist: ${filePath}`);
            }

            // Create a backup file name
            const backupPath = `${filePath}.bak`;
            
            // Copy the file
            fs.copyFileSync(filePath, backupPath);
            
            return backupPath;
        } catch (error) {
            console.error(`Error creating backup: ${error}`);
            throw error;
        }
    }

    /**
     * Restore a file from backup
     * @param backupPath The backup file path
     * @returns A promise that resolves when the file is restored
     */
    public async restoreFromBackup(backupPath: string): Promise<void> {
        try {
            // Check if the backup exists
            if (!this.fileExists(backupPath)) {
                throw new Error(`Backup file does not exist: ${backupPath}`);
            }

            // Get the original file path
            const originalPath = backupPath.substring(0, backupPath.length - 4); // Remove .bak

            // Copy the backup to the original
            fs.copyFileSync(backupPath, originalPath);

            // Delete the backup
            fs.unlinkSync(backupPath);

            // Reload the file if it's open
            const openDocuments = vscode.workspace.textDocuments;
            for (const document of openDocuments) {
                if (document.fileName === originalPath) {
                    await vscode.window.showTextDocument(document, { preview: false, preserveFocus: false });
                    break;
                }
            }
        } catch (error) {
            console.error(`Error restoring from backup: ${error}`);
            throw error;
        }
    }
}
