from typing import Protocol, List, Dict
from dataclasses import dataclass

@dataclass
class ConceptNode:
    term: str
    relevance_score: float
    related_terms: List[str]
    context: str
    source_location: str = ""  # Track where concept was found
    confidence_score: float = 0.0  # Confidence in concept extraction

class ConceptExtractor(Protocol):
    """Protocol for concept extraction components"""
    async def extract(self, query: str) -> List[ConceptNode]:
        """Extract concepts from query"""
        ...

    async def validate(self, concepts: List[ConceptNode]) -> List[ConceptNode]:
        """Validate and filter extracted concepts"""
        ...

class QueryEnhancer(Protocol):
    """Protocol for query enhancement components"""
    async def enhance(self, query: str, concepts: List[ConceptNode]) -> str:
        """Enhance query using extracted concepts"""
        ...

    async def rewrite(self, query: str, feedback: Dict) -> str:
        """Rewrite query based on feedback"""
        ...