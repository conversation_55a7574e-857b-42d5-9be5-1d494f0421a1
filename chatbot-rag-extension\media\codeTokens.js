// Code token functionality for RAG Assistant

// Initialize code token functions
window.codeTokenFunctions = {
    // Process code tokens in content
    processCodeTokens: function(content) {
        if (!content) return '';

        // Regular expressions for different token formats
        // Format 1: `TokenName` - Basic token
        // Format 2: `TokenName:TokenType` - Token with type
        // Format 3: `TokenName:TokenType:FilePath` - Token with type and file path
        const tokenRegex = /`([^:`]+)(?::([^:`]+))?(?::([^:`]+))?`/g;

        // Replace tokens with clickable spans
        return content.replace(tokenRegex, (match, name, type, filePath) => {
            // Create a unique ID for this token
            const tokenId = `token-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

            // Create data attributes for the token
            const dataAttributes = `
                data-token-name="${name}"
                ${type ? `data-token-type="${type}"` : ''}
                ${filePath ? `data-token-file="${filePath}"` : ''}
            `;

            // Create the HTML
            return `<span id="${tokenId}" class="code-token ${type ? `token-type-${type.toLowerCase()}` : ''}" ${dataAttributes} onclick="window.codeTokenFunctions.findCodeToken('${name}', '${filePath || ''}', '${type || ''}', '${tokenId}')"><code>${name}${type ? `:${type}` : ''}${filePath ? `:${filePath}` : ''}</code></span>`;
        });
    },
    // Find a code token in the workspace
    findCodeToken: function(name, filePath, type, tokenId) {
        // Send a message to the extension to find the token
        if (window.vscode) {
            window.vscode.postMessage({
                type: 'findCodeToken',
                token: name,
                filePath: filePath,
                tokenType: type,
                tokenId: tokenId
            });
        }
    },

    // Update token UI after finding it
    updateTokenUI: function(tokenId, found) {
        const tokenElement = document.getElementById(tokenId);
        if (!tokenElement) return;

        if (found) {
            // Add a class to indicate the token was found
            tokenElement.classList.add('token-found');

            // Show a success indicator
            const indicator = document.createElement('span');
            indicator.className = 'token-indicator success';
            indicator.textContent = '✓';
            tokenElement.appendChild(indicator);

            // Remove the indicator after a delay
            setTimeout(() => {
                if (indicator.parentNode === tokenElement) {
                    tokenElement.removeChild(indicator);
                }
            }, 3000);
        } else {
            // Add a class to indicate the token was not found
            tokenElement.classList.add('token-not-found');

            // Show an error indicator
            const indicator = document.createElement('span');
            indicator.className = 'token-indicator error';
            indicator.textContent = '✗';
            tokenElement.appendChild(indicator);

            // Remove the indicator after a delay
            setTimeout(() => {
                if (indicator.parentNode === tokenElement) {
                    tokenElement.removeChild(indicator);
                }
            }, 3000);
        }
    },

    // Show token information on hover
    showTokenInfo: function(tokenId, info) {
        const tokenElement = document.getElementById(tokenId);
        if (!tokenElement) return;

        // Create a tooltip element
        const tooltip = document.createElement('div');
        tooltip.className = 'token-tooltip';

        // Add token information to the tooltip
        let tooltipContent = `<div class="token-tooltip-header">${info.name}</div>`;

        if (info.type) {
            tooltipContent += `<div class="token-tooltip-type">${info.type}</div>`;
        }

        if (info.filePath) {
            tooltipContent += `<div class="token-tooltip-file">${info.filePath}</div>`;
        }

        if (info.description) {
            tooltipContent += `<div class="token-tooltip-description">${info.description}</div>`;
        }

        tooltip.innerHTML = tooltipContent;

        // Position the tooltip
        const rect = tokenElement.getBoundingClientRect();
        tooltip.style.top = `${rect.bottom + 5}px`;
        tooltip.style.left = `${rect.left}px`;

        // Add the tooltip to the document
        document.body.appendChild(tooltip);

        // Remove the tooltip when the mouse leaves the token
        tokenElement.addEventListener('mouseleave', function() {
            if (tooltip.parentNode === document.body) {
                document.body.removeChild(tooltip);
            }
        });
    }
};

// Add hover event listeners to all code tokens
document.addEventListener('DOMContentLoaded', function() {
    // Add hover event listeners to existing tokens
    addTokenHoverListeners();

    // Use a mutation observer to add hover event listeners to new tokens
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                addTokenHoverListeners();
            }
        });
    });

    // Start observing the document
    observer.observe(document.body, { childList: true, subtree: true });
});

// Add hover event listeners to all code tokens
function addTokenHoverListeners() {
    const tokens = document.querySelectorAll('.code-token');

    tokens.forEach(function(token) {
        // Skip tokens that already have hover listeners
        if (token.dataset.hasHoverListener) return;

        // Add hover listener
        token.addEventListener('mouseenter', function() {
            // Get token information
            const name = token.dataset.tokenName;
            const type = token.dataset.tokenType;
            const filePath = token.dataset.tokenFile;

            if (name) {
                // Send a message to the extension to get token information
                if (window.vscode) {
                    window.vscode.postMessage({
                        type: 'getTokenInfo',
                        token: name,
                        tokenId: token.id
                    });
                }
            }
        });

        // Mark the token as having a hover listener
        token.dataset.hasHoverListener = 'true';
    });
}

// Listen for messages from the extension
window.addEventListener('message', function(event) {
    const message = event.data;

    // Handle token information
    if (message.type === 'tokenInfo' && message.tokenId) {
        window.codeTokenFunctions.showTokenInfo(message.tokenId, message.info);
    }

    // Handle token search results
    if (message.type === 'tokenSearchResult' && message.tokenId) {
        window.codeTokenFunctions.updateTokenUI(message.tokenId, message.found);
    }
});
