"""
Model interface for supporting multiple LLM providers.

This module provides a unified interface for interacting with different LLM providers,
including Google's Gemini, Anthropic's Claude, and Ollama models.
"""

import os
import logging
from typing import Dict, Any, List, Optional, Union, Callable
from enum import Enum
import json
import asyncio

# Configure logging
logger = logging.getLogger("model_interface")

class ModelProvider(Enum):
    """Enum for supported model providers"""
    GOOGLE = "google"
    ANTHROPIC = "anthropic"
    OLLAMA = "ollama"
    OPENAI = "openai"

class ModelResponse:
    """Unified response object for all model providers"""

    def __init__(
        self,
        text: str,
        raw_response: Any = None,
        usage: Optional[Dict[str, Any]] = None,
        model_name: Optional[str] = None
    ):
        self.text = text
        self.raw_response = raw_response
        self.usage = usage or {}
        self.model_name = model_name

    def __str__(self) -> str:
        return self.text

class BaseModelInterface:
    """Base interface for all model providers"""

    def __init__(self, model_name: str):
        self.model_name = model_name

    async def generate_content(self, prompt: str, **kwargs) -> ModelResponse:
        """
        Generate content from the model.

        Args:
            prompt: The prompt to send to the model
            **kwargs: Additional model-specific parameters

        Returns:
            ModelResponse object containing the generated text
        """
        raise NotImplementedError("Subclasses must implement generate_content")

    async def generate_with_tools(
        self,
        prompt: str,
        tools: List[Dict[str, Any]],
        **kwargs
    ) -> ModelResponse:
        """
        Generate content with tool calling capabilities.

        Args:
            prompt: The prompt to send to the model
            tools: List of tool definitions
            **kwargs: Additional model-specific parameters

        Returns:
            ModelResponse object containing the generated text and tool calls
        """
        raise NotImplementedError("Subclasses must implement generate_with_tools")

class GeminiModelInterface(BaseModelInterface):
    """Interface for Google's Gemini models"""

    def __init__(self, model_name: str = "gemini-1.5-pro"):
        super().__init__(model_name)
        try:
            import google.generativeai as genai
            from google.adk.models import GeminiModel

            # Initialize the Google API
            api_key = os.environ.get("GOOGLE_API_KEY")
            if not api_key:
                raise ValueError("GOOGLE_API_KEY environment variable not set")

            genai.configure(api_key=api_key)

            # For ADK compatibility
            self.adk_model = GeminiModel(model_name)

            # For direct use
            self.genai = genai
            self.model = genai.GenerativeModel(model_name)

        except ImportError:
            raise ImportError("Google GenerativeAI package not installed. Install with: pip install google-generativeai")

    async def generate_content(self, prompt: str, **kwargs) -> ModelResponse:
        """Generate content using Gemini"""
        try:
            # Use asyncio to run in a thread pool since Google's API is synchronous
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: self.model.generate_content(prompt, **kwargs)
            )

            return ModelResponse(
                text=response.text,
                raw_response=response,
                model_name=self.model_name
            )
        except Exception as e:
            logger.error(f"Error generating content with Gemini: {str(e)}")
            raise

    async def generate_with_tools(
        self,
        prompt: str,
        tools: List[Dict[str, Any]],
        **kwargs
    ) -> ModelResponse:
        """Generate content with tool calling capabilities using Gemini"""
        try:
            # Convert tools to Google's format
            google_tools = []
            for tool in tools:
                google_tools.append({
                    "function_declarations": [{
                        "name": tool["name"],
                        "description": tool["description"],
                        "parameters": tool["parameters"]
                    }]
                })

            # Use asyncio to run in a thread pool since Google's API is synchronous
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                None,
                lambda: self.model.generate_content(
                    prompt,
                    tools=google_tools,
                    **kwargs
                )
            )

            return ModelResponse(
                text=response.text,
                raw_response=response,
                model_name=self.model_name
            )
        except Exception as e:
            logger.error(f"Error generating content with tools using Gemini: {str(e)}")
            raise

class AnthropicModelInterface(BaseModelInterface):
    """Interface for Anthropic's Claude models"""

    def __init__(self, model_name: str = "claude-3-opus-20240229"):
        super().__init__(model_name)
        try:
            import anthropic

            # Initialize the Anthropic API
            api_key = os.environ.get("ANTHROPIC_API_KEY")
            if not api_key:
                raise ValueError("ANTHROPIC_API_KEY environment variable not set")

            self.client = anthropic.Anthropic(api_key=api_key)

            # Import the tool calling enhancer for models that don't support tool calling
            from chatbot_rag.adk.utils.tool_calling_enhancer import ToolCallingEnhancer
            self.tool_enhancer = ToolCallingEnhancer(temperature=0.2)

            # Models that support tool calling natively
            self.tool_capable_models = [
                "claude-3-opus-20240229",
                "claude-3-sonnet-20240229",
                "claude-3-haiku-20240307",
                "claude-3.5-sonnet-20240620"
            ]

        except ImportError:
            raise ImportError("Anthropic package not installed. Install with: pip install anthropic")

    async def generate_content(self, prompt: str, **kwargs) -> ModelResponse:
        """Generate content using Claude"""
        try:
            # Use the Anthropic client to generate content
            response = await self.client.messages.create(
                model=self.model_name,
                max_tokens=kwargs.get("max_tokens", 1024),
                temperature=kwargs.get("temperature", 0.7),
                messages=[{"role": "user", "content": prompt}]
            )

            # Extract the text from the response
            text = response.content[0].text

            return ModelResponse(
                text=text,
                raw_response=response,
                usage={"input_tokens": response.usage.input_tokens, "output_tokens": response.usage.output_tokens},
                model_name=self.model_name
            )
        except Exception as e:
            logger.error(f"Error generating content with Claude: {str(e)}")
            raise

    async def generate_with_tools(
        self,
        prompt: str,
        tools: List[Dict[str, Any]],
        **kwargs
    ) -> ModelResponse:
        """Generate content with tool calling capabilities using Claude"""
        try:
            # Check if the model supports tool calling natively
            if self.model_name in self.tool_capable_models:
                # Convert tools to Anthropic's format
                anthropic_tools = []
                for tool in tools:
                    anthropic_tools.append({
                        "name": tool["name"],
                        "description": tool["description"],
                        "input_schema": tool["parameters"]
                    })

                # Use the Anthropic client to generate content with tools
                response = await self.client.messages.create(
                    model=self.model_name,
                    max_tokens=kwargs.get("max_tokens", 1024),
                    temperature=kwargs.get("temperature", 0.7),
                    messages=[{"role": "user", "content": prompt}],
                    tools=anthropic_tools
                )

                # Extract the text from the response
                text = response.content[0].text

                # Extract tool calls if any
                tool_calls = []
                for content_block in response.content:
                    if hasattr(content_block, 'type') and content_block.type == 'tool_use':
                        tool_calls.append({
                            "name": content_block.name,
                            "args": content_block.input
                        })

                return ModelResponse(
                    text=text,
                    raw_response={
                        "response": response,
                        "tool_calls": tool_calls
                    },
                    usage={"input_tokens": response.usage.input_tokens, "output_tokens": response.usage.output_tokens},
                    model_name=self.model_name
                )
            else:
                # For models that don't support tool calling natively, use the enhancer
                logger.info(f"Model {self.model_name} doesn't support tool calling natively. Using tool calling enhancer.")

                # Use the tool calling enhancer to process the prompt with tools
                result = await self.tool_enhancer.process_with_tools(
                    model_interface=self,
                    prompt=prompt,
                    tools=tools,
                    max_tool_calls=kwargs.get("max_tool_calls", 3),
                    max_tokens=kwargs.get("max_tokens", 1024)
                )

                # Create a response with the tool calls
                return ModelResponse(
                    text=result["response"],
                    raw_response={
                        "tool_calls": result["tool_calls"],
                        "conversation": result["conversation"]
                    },
                    model_name=self.model_name
                )
        except Exception as e:
            logger.error(f"Error generating content with tools using Claude: {str(e)}")
            raise

class OllamaModelInterface(BaseModelInterface):
    """Interface for Ollama models"""

    def __init__(self, model_name: str = "llama3"):
        super().__init__(model_name)
        try:
            # Ollama typically runs on localhost:11434
            self.base_url = os.environ.get("OLLAMA_BASE_URL", "http://localhost:11434")

            # Check if requests is installed
            import requests
            self.requests = requests

            # Import the tool calling enhancer
            from chatbot_rag.adk.utils.tool_calling_enhancer import ToolCallingEnhancer
            self.tool_enhancer = ToolCallingEnhancer(temperature=0.2)

            # Test connection
            try:
                response = requests.get(f"{self.base_url}/api/tags")
                if response.status_code != 200:
                    logger.warning(f"Could not connect to Ollama at {self.base_url}")
            except Exception as e:
                logger.warning(f"Could not connect to Ollama: {str(e)}")

        except ImportError:
            raise ImportError("Requests package not installed. Install with: pip install requests")

    async def generate_content(self, prompt: str, **kwargs) -> ModelResponse:
        """Generate content using Ollama"""
        try:
            import aiohttp

            # Prepare the request
            url = f"{self.base_url}/api/generate"
            data = {
                "model": self.model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": kwargs.get("temperature", 0.7),
                    "num_predict": kwargs.get("max_tokens", 1024)
                }
            }

            # Make the request
            async with aiohttp.ClientSession() as session:
                async with session.post(url, json=data) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise Exception(f"Ollama API error: {error_text}")

                    result = await response.json()

            return ModelResponse(
                text=result.get("response", ""),
                raw_response=result,
                model_name=self.model_name
            )
        except Exception as e:
            logger.error(f"Error generating content with Ollama: {str(e)}")
            raise

    async def generate_with_tools(
        self,
        prompt: str,
        tools: List[Dict[str, Any]],
        **kwargs
    ) -> ModelResponse:
        """
        Generate content with tool calling capabilities using Ollama.

        Note: Ollama doesn't natively support tool calling, so we use the ToolCallingEnhancer
        to provide a more robust implementation.
        """
        try:
            # Use the tool calling enhancer to process the prompt with tools
            result = await self.tool_enhancer.process_with_tools(
                model_interface=self,
                prompt=prompt,
                tools=tools,
                max_tool_calls=kwargs.get("max_tool_calls", 3),
                max_tokens=kwargs.get("max_tokens", 1024)
            )

            # Create a response with the tool calls
            return ModelResponse(
                text=result["response"],
                raw_response={
                    "tool_calls": result["tool_calls"],
                    "conversation": result["conversation"]
                },
                model_name=self.model_name
            )
        except Exception as e:
            logger.error(f"Error generating content with tools using Ollama: {str(e)}")
            raise

class OpenAIModelInterface(BaseModelInterface):
    """Interface for OpenAI models"""

    def __init__(self, model_name: str = "gpt-4o"):
        super().__init__(model_name)
        try:
            import openai

            # Initialize the OpenAI API
            api_key = os.environ.get("OPENAI_API_KEY")
            if not api_key:
                raise ValueError("OPENAI_API_KEY environment variable not set")

            self.client = openai.AsyncOpenAI(api_key=api_key)

            # Import the tool calling enhancer for models that don't support tool calling
            from chatbot_rag.adk.utils.tool_calling_enhancer import ToolCallingEnhancer
            self.tool_enhancer = ToolCallingEnhancer(temperature=0.2)

            # Models that support tool calling natively
            self.tool_capable_models = [
                "gpt-4o", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo"
            ]

        except ImportError:
            raise ImportError("OpenAI package not installed. Install with: pip install openai")

    async def generate_content(self, prompt: str, **kwargs) -> ModelResponse:
        """Generate content using OpenAI"""
        try:
            # Use the OpenAI client to generate content
            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=[{"role": "user", "content": prompt}],
                max_tokens=kwargs.get("max_tokens", 1024),
                temperature=kwargs.get("temperature", 0.7)
            )

            # Extract the text from the response
            text = response.choices[0].message.content

            return ModelResponse(
                text=text,
                raw_response=response,
                usage={"prompt_tokens": response.usage.prompt_tokens, "completion_tokens": response.usage.completion_tokens},
                model_name=self.model_name
            )
        except Exception as e:
            logger.error(f"Error generating content with OpenAI: {str(e)}")
            raise

    async def generate_with_tools(
        self,
        prompt: str,
        tools: List[Dict[str, Any]],
        **kwargs
    ) -> ModelResponse:
        """Generate content with tool calling capabilities using OpenAI"""
        try:
            # Check if the model supports tool calling natively
            if self.model_name in self.tool_capable_models:
                # Convert tools to OpenAI's format
                openai_tools = []
                for tool in tools:
                    openai_tools.append({
                        "type": "function",
                        "function": {
                            "name": tool["name"],
                            "description": tool["description"],
                            "parameters": tool["parameters"]
                        }
                    })

                # Use the OpenAI client to generate content with tools
                response = await self.client.chat.completions.create(
                    model=self.model_name,
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=kwargs.get("max_tokens", 1024),
                    temperature=kwargs.get("temperature", 0.7),
                    tools=openai_tools
                )

                # Extract the text from the response
                text = response.choices[0].message.content

                # Extract tool calls if any
                tool_calls = []
                if hasattr(response.choices[0].message, 'tool_calls') and response.choices[0].message.tool_calls:
                    for tool_call in response.choices[0].message.tool_calls:
                        if tool_call.type == 'function':
                            import json
                            args = json.loads(tool_call.function.arguments)
                            tool_calls.append({
                                "name": tool_call.function.name,
                                "args": args
                            })

                return ModelResponse(
                    text=text,
                    raw_response={
                        "response": response,
                        "tool_calls": tool_calls
                    },
                    usage={"prompt_tokens": response.usage.prompt_tokens, "completion_tokens": response.usage.completion_tokens},
                    model_name=self.model_name
                )
            else:
                # For models that don't support tool calling natively, use the enhancer
                logger.info(f"Model {self.model_name} doesn't support tool calling natively. Using tool calling enhancer.")

                # Use the tool calling enhancer to process the prompt with tools
                result = await self.tool_enhancer.process_with_tools(
                    model_interface=self,
                    prompt=prompt,
                    tools=tools,
                    max_tool_calls=kwargs.get("max_tool_calls", 3),
                    max_tokens=kwargs.get("max_tokens", 1024)
                )

                # Create a response with the tool calls
                return ModelResponse(
                    text=result["response"],
                    raw_response={
                        "tool_calls": result["tool_calls"],
                        "conversation": result["conversation"]
                    },
                    model_name=self.model_name
                )
        except Exception as e:
            logger.error(f"Error generating content with tools using OpenAI: {str(e)}")
            raise

def create_model_interface(
    provider: Union[str, ModelProvider],
    model_name: Optional[str] = None
) -> BaseModelInterface:
    """
    Factory function to create a model interface based on the provider.

    Args:
        provider: The model provider (google, anthropic, ollama, openai)
        model_name: Optional model name. If not provided, a default will be used.

    Returns:
        A model interface for the specified provider
    """
    # Convert string to enum if needed
    if isinstance(provider, str):
        try:
            provider = ModelProvider(provider.lower())
        except ValueError:
            raise ValueError(f"Unsupported provider: {provider}. Supported providers: {[p.value for p in ModelProvider]}")

    # Create the appropriate interface
    if provider == ModelProvider.GOOGLE:
        return GeminiModelInterface(model_name or "gemini-1.5-pro")
    elif provider == ModelProvider.ANTHROPIC:
        return AnthropicModelInterface(model_name or "claude-3-sonnet-20240229")
    elif provider == ModelProvider.OLLAMA:
        return OllamaModelInterface(model_name or "llama3")
    elif provider == ModelProvider.OPENAI:
        return OpenAIModelInterface(model_name or "gpt-4o")
    else:
        raise ValueError(f"Unsupported provider: {provider}")

# ADK Compatibility Layer
class ADKModelWrapper:
    """
    Wrapper to make our model interface compatible with Google's ADK.

    This allows using our model interface with the existing ADK-based agent code.
    """

    def __init__(self, model_interface: BaseModelInterface):
        self.model_interface = model_interface
        self.model_name = model_interface.model_name

    async def generate_content(self, prompt: str, **kwargs):
        """Generate content using the wrapped model interface"""
        response = await self.model_interface.generate_content(prompt, **kwargs)

        # Create a response object that mimics the ADK response
        class ADKCompatResponse:
            def __init__(self, text):
                self.text = text

        return ADKCompatResponse(response.text)

    def __str__(self) -> str:
        return f"ADKModelWrapper({self.model_interface.model_name})"
