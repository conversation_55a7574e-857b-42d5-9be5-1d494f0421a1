from typing import Dict, List, Optional
from datetime import datetime, timedelta

class ConceptCache:
    def __init__(self, ttl_minutes: int = 60):
        self.cache: Dict[str, Dict] = {}
        self.ttl = timedelta(minutes=ttl_minutes)

    def get(self, query: str) -> Optional[List[ConceptNode]]:
        if query not in self.cache:
            return None
            
        entry = self.cache[query]
        if datetime.now() - entry['timestamp'] > self.ttl:
            del self.cache[query]
            return None
            
        return entry['concepts']

    def store(self, query: str, concepts: List[ConceptNode]):
        self.cache[query] = {
            'concepts': concepts,
            'timestamp': datetime.now(),
            'usage_count': 0
        }

    def update_stats(self, query: str):
        if query in self.cache:
            self.cache[query]['usage_count'] += 1